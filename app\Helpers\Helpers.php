<?php 

namespace App\Helpers;
use App\model\Students;
use App\Model\v2\Colleges;
use Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Redis;

class Helpers { 

    public static $MEMORY_ITEMS = [];

    
    public static function changeRootPath($filePath, $subFolderName = '', $collegeId = ''){

        if (empty($collegeId)) {
            if (!empty(Auth()->guard('student')->user())) {
                $collegeId = Auth()->guard('student')->user()->college_id;
            }
            if (!empty(Auth()->guard('teacher')->user())) {
                $collegeId = Auth()->guard('teacher')->user()->college_id;
            }
            if (!empty(Auth()->guard('agent')->user())) {
                $collegeId = Auth()->guard('agent')->user()->college_id;
            }
            if (!empty(Auth::user())) {
                $collegeId = Auth::user()->college_id;
            }
        }
        if(empty($collegeId)){
            $collegeId = Colleges::orderByDesc("status")->orderByDesc("id")->value("id");
        } 

        $key = '{college_id}';
        $value = $collegeId;
        $default = str_replace("$key", $value, $filePath['default']);
        $view = str_replace("$key", $value, $filePath['view']);
        if (!is_dir($default)) { @mkdir($default, 0777); }
        if(empty($subFolderName)){
            $resultArr = ['default' => $default, 'view' => $view];
        }else{
            $resultArr = ['default' => $default.$subFolderName.'/', 'view' => $view.$subFolderName.'/'];
        }

        return $resultArr;
    }

    public static function getCurrentUploadFolder($basePath, $yearMonth, $groupName)
    {
        return ['view' => rtrim($basePath['view'], '/') . '/' . $yearMonth . '/' . $groupName, 'default' => rtrim($basePath['default'], '/') . '/' . $yearMonth . '/' . $groupName,];
    }

    public static function shout($string){
        return strtoupper($string);
    }

    public static function isRedisReady($connection = null)
    {
        $isReady = true;
        try {
             Redis::connection();
        } catch (\Exception $e) {
            $isReady = false;
        }

        return $isReady;
    }

    public static function changeCustomRootPath($filePath){
        
        //echo "$collegeId<pre/>";print_r($filePath);exit;
        $collegeId = Auth::user()->college_id;
        $resultArr = [];
        $key = '{college_id}';
        $value = $collegeId;
            
        foreach($filePath as $rowKey => $rowValue){
            $default = str_replace("$key", $value, $rowValue['default']);
            $view = str_replace("$key", $value, $rowValue['view']);
            $resultArr[$rowKey] = ['default' => $default, 'view' => $view];
        }
        return $resultArr;
    }
    
    public static function checkStudentType($studentId){
        return Students::where('id', $studentId)->value('student_type');
        return "Student Id : $studentId";
    }
    
    public static function setNumericValue($value, $digit){
        $string = '';
        for ($i=1; $i < $digit; $i++) {
            if(($digit - strlen($value)) > 0){
                $string .= "0$string";
            }else{
                break;
            }
        }
        $result = $string . $value;
        return $result;
    }

    public static function encrypt_str($string, $key) {
        $result = '';
        for($i=0; $i<strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key))-1, 1);
            $char = chr(ord($char)+ord($keychar));
            $result.=$char;
        }
    
        return base64_encode($result);
    }
    
    public static function decrypt_str($string, $key) {
        $result = '';
        $string = base64_decode($string);
    
        for($i=0; $i<strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key))-1, 1);
            $char = chr(ord($char)-ord($keychar));
            $result.=$char;
        }
        return $result;
    }

    public function getOperators(){
        return [
            'eq' => '=',
            'gt' => '>',
            'gte' => '>=',
            'lt' => '<',
            'lte' => '<=',
            'neq' => '!=',
            'startswith' => 'like',
            'contains' => 'like',
            'doesnotcontain' => 'not like',
            'endswith' => 'like',
            'isnull' => '=',
            'isnotnull' => '!=',
            'isempty' => '=',
            'isnotempty' => '!='
        ];
    }

    public function convertFilterValue($filterOperator, $filterValue){
        //Do some value manupulations for some kendo operators
        switch ($filterOperator) {
            case 'startswith':
                $value = $filterValue . '%';
                break;
            case 'endswith':
                $value = '%' . $filterValue;
                break;
            case 'contains':
            case 'doesnotcontain':
                $value = '%' . $filterValue . '%';
                break;
            case 'isnull':
            case 'isnotnull':
                $value = null;
                break;
            case 'isempty':
            case 'isnotempty':
                $value = '';
                break;
            default:
                $value = $filterValue;
                break;

        }
        return $value;
    }

    public function getOrientationFilterCategory(){
        return array(
            //['id'=>0, 'hasChild'=>false, 'text'=>'Select Campus'],
            ['id'=>1, 'hasChild'=>true, 'text'=>'Student By'],
            ['id'=>2, 'hasChild'=>true, 'text'=>'Course Start Date'],
            ['id'=>3, 'hasChild'=>true, 'text'=>'Course Type'],
            ['id'=>4, 'hasChild'=>true, 'text'=>'Course Applied'],
            ['id'=>5, 'hasChild'=>true, 'text'=>'Applied Year']
        );
    }

    public function getStudentProfilePicPath($student_id, $profile_picture){
        $filePath = Config::get('constants.uploadFilePath.StudentPics');
        $destinationPath = Helpers::changeRootPath($filePath, $student_id);

        $profile_pic = $destinationPath['default'] . $profile_picture;

        if (file_exists($profile_pic) && !empty($profile_picture)) {
            $profile_pic_url = asset($destinationPath['view'] . $profile_picture);
        } else {
            $profile_pic_url = '';
            //$profile_pic_url = asset('icon/profile.png');
        }
        return $profile_pic_url;
    }

    //TODO::use this function for generate certificate blade file
    public static function setStudentNameForCertificate($res)
    {
        return trim("{$res['first_name']} {$res['middel_name']} {$res['family_name']}");
        //return trim("{$res['name_title']} {$res['first_name']} {$res['middel_name']} {$res['family_name']}");
    }

    public static function phpDateFormatToMySQL($phpFormat)
    {
        $replacements = [
            'd' => '%d',  // Day
            'm' => '%m',  // Month (numeric)
            'M' => '%b',  // Month (short name)
            'Y' => '%Y',  // Year (4 digits)
            'y' => '%y',  // Year (2 digits)
        ];
        return strtr($phpFormat, $replacements);
    }

    public static function toMysqlDateFormat($format=null)
    {
        $dateFormat = $format ?? Config::get('app.displayDateFormatPHP', 'Y-m-d');
        return self::phpDateFormatToMySQL($dateFormat);
    }

    public static function convertDateToReadableFormat($date, $format=null)
    {
        if(!empty($date)){
            $format = $format ?: Config::get('app.displayDateFormatPHP', 'Y-m-d'); // Use default format if none is provided
            return date($format, strtotime($date));
        }
        return '';
    }

    /* Date convert with Time Zone */
    public static function convertDateTimeToReadableFormat($date, $format=null, $timezone=null)
    {
        if(!empty($date)){
            $format = $format ?: Config::get('app.displayDateTimeFormatPHP', 'd M Y h:i A');
            $timezone = $timezone ?? auth()->user()->timezone ?? Config::get('timezone.defaultProfileTimeZone');
            //$timezone = $timezone ? $timezone : 'UTC';
            $date = new \DateTime($date);
            $date->setTimezone(new \DateTimeZone($timezone));
            return $date->format($format);
        }
        return '';
    }

    /*public function toMysqlDate($date, $format = 'm-d-Y', $returnFormat = 'Y-m-d'){
        $newDate = \DateTime::createFromFormat($format, $date);
        // dd(DateTime::getLastErrors());
        return $newDate ? $newDate->format($returnFormat) : $date;
    }

    public function toMysqlDateTime($date, $format = 'm-d-Y', $returnFormat = 'Y-m-d', $timezone_offset = 0){
        // dd($timezone_offset);
        $newDate = \DateTime::createFromFormat($format, $date, new \DateTimeZone($timezone_offset));
        if(!$newDate){
            return null;
        }
        $newDate->setTimezone(new \DateTimeZone('+0:00'));
        // dd($newDate->getOffset());
        // dd($newDate->getTimezone());
        // dd(DateTime::getLastErrors());
        return $newDate ? $newDate->format($returnFormat) : $date;
    }

    public function toAppDate($date, $format = 'm-d-Y'){
        return $date ? date($format, strtotime($date)) : $date;
    }

    function toTimezoneDate($date, $format = 'Y-m-d H:i', $timezone = 'UTC'){
        // dd($date);
        $timezone = $timezone ? $timezone : 'UTC';
        $date = new DateTime($date);
        $date->setTimezone(new DateTimeZone($timezone));
        return $date->format($format);
    }*/

    /* Color code for student course status */
    public static function getStudCourseColorCode($status)
    {
        $colorMap = [
            'Current Student' => 'primary-blue',
            'Cancelled'       => 'red',
            'Transitioned'    => 'yellow',
            'Completed'       => 'green',
            'Finished'        => 'green',
            'Withdrawn'       => 'pink',
            'Suspended'       => 'red',
        ];
        return $colorMap[$status] ?? 'gray';
    }
}