import { ref, onMounted, watch, computed, reactive, inject } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import { mergeFirstObject } from '../helpers';
import useConfirm from '@spa/services/useConfirm';
import { useLoaderStore } from '../stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper.js';

const initialization = reactive({});

export function useCourseResource(config = {}) {
    const $page = usePage();
    // let items = ref([]);
    const url = $page.props.uri;

    const loaderStore = useLoaderStore();

    const only = config['only'] || [];

    const state = reactive({
        filters: {},
        sortBy: null,
        error: null,
        selected: [],
        selectAll: false,
        pageable: {
            totalItems: 0,
            totalPages: 0,
            currentPage: 1,
            skip: 0,
            buttonCount: 5,
            info: true,
            type: 'numeric',
            pageSizes: [10, 20, 50, 100, 150, 200],
            previousNext: true,
            pageSizeValue: 10,
        },
    });

    if (config['filters']) {
        state.filters = { ...state.filters, ...config['filters'] };
        if ($page.props && $page.props.query) {
            state.filters = mergeFirstObject(state.filters, $page.props.query);
            state.filters['type'] = parseInt(state.filters['type']) || null;
            //"duration": "40", "take": "50", "page": "1"
            let currentPage = parseInt($page.props.courses?.meta?.current_page) || 0;
            let totalPages = parseInt($page.props.courses?.meta?.last_page) || 0;
            let totalItems = parseInt($page.props.courses?.meta?.total) || 0;
            let take = parseInt($page.props.courses?.meta?.per_page) || 10;
            let skip = (currentPage - 1) * take;
            state.pageable['currentPage'] = currentPage;
            state.pageable['pageSizeValue'] = take;
            state.pageable['skip'] = skip;
            state.pageable['totalPages'] = totalPages;
            state.pageable['totalItems'] = totalItems;
            state.filters['take'] = take;
            state.filters['page'] = currentPage;
        }
        initialization[url] = true;
    }

    /* fetch data */
    const fetch = async (params = {}) => {
        const focusKewordInput = params['keywordActive'] || false;
        delete params['keywordActive'];
        params = { ...state.filters, ...params };
        params = Object.fromEntries(
            Object.entries(params).filter(
                ([key, value]) => value !== undefined && value !== null && value !== ''
            )
        );
        router.get(url, params, {
            only: [...only, 'query', 'params'],
            preserveState: true,
            onBefore: (visit) => {
                loaderStore.startContextLoading('course-list');
            },
            onError: (resp) => {
                loaderStore.stopContextLoading('course-list');
            },
            onFinish: (visit) => {
                /*
                to reload pagination 
                */
                let currentPage = parseInt($page.props.courses?.meta?.current_page) || 0;
                let totalPages = parseInt($page.props.courses?.meta?.last_page) || 0;
                let totalItems = parseInt($page.props.courses?.meta?.total) || 0;
                let take = parseInt($page.props.courses?.meta?.per_page) || 10;
                let skip = (currentPage - 1) * take;
                state.pageable['currentPage'] = currentPage;
                state.pageable['pageSizeValue'] = take;
                state.pageable['skip'] = skip;
                state.pageable['totalPages'] = totalPages;
                state.pageable['totalItems'] = totalItems;
                state.filters['take'] = take;
                state.filters['page'] = currentPage;
                loaderStore.stopContextLoading('course-list');
            },
        });
    };

    /* ask for confirmation while removing the course and remove the course */
    const onRemove = async (id, message = '', reFetch = true, params = {}) => {
        const $confirm = useConfirm();
        $confirm.require({
            message: message || 'Are you sure you want to remove?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: async () => {
                //const context = `course-item`;
                const context = `course-item-${id}`;
                loaderStore.startContextLoading(context);
                try {
                    let resp = await $http.delete(route('spa.courses.destroy', parseInt(id)));
                    loaderStore.stopContextLoading(context);
                    if (resp['success']) {
                        if (reFetch) {
                            fetch({ ...params });
                        }
                    }
                } catch (error) {
                    loaderStore.stopContextLoading(context);
                }
            },
            reject: () => {
                //callback to execute when user rejects the action
            },
            onHide: () => {
                //Callback to execute when dialog is hidden
            },
        });
    };

    /* ask for confirmation while changing the status and update the status */
    const onStatusChange = async (course = {}, message = '', reFetch = true, params = {}) => {
        const courseId = course.id || 0;
        const courseStatus = course.status || 0;
        if (!courseId) {
            return;
        }
        const headerTitle = courseStatus ? 'Activate Course' : 'Deactivate Course';
        const $confirm = useConfirm();
        $confirm.require({
            message: message || 'Are you sure you want to change the status of this course?',
            header: headerTitle,
            icon: 'pi pi-exclamation-triangle',
            accept: async () => {
                const context = `course-item-${courseId}`;
                loaderStore.startContextLoading(context);
                try {
                    let resp = await $http.post(route('spa.courses.status'), course);
                    loaderStore.stopContextLoading(context);
                    if (resp['success']) {
                        if (reFetch) {
                            fetch({ ...params });
                        }
                    }
                } catch (error) {
                    loaderStore.stopContextLoading(context);
                }
            },
            reject: () => {
                //callback to execute when user rejects the action
            },
            onHide: () => {
                //Callback to execute when dialog is hidden
            },
        });
    };

    /* Sync From Moodle*/
    const fromSync = async (message = '', reFetch = true, params = {}) => {
        const $confirm = useConfirm();
        const postData = {};
        $confirm.require({
            message: message || `Are you sure you want to All course sync from Moodle?`,
            header: 'Sync From Moodle',
            //icon: "pi pi-exclamation-triangle",
            accept: async () => {
                loaderStore.startContextLoading('course-list');
                try {
                    let resp = await $http.post(route('spa.courses.syncfrommoodle'), postData);
                    loaderStore.stopContextLoading('course-list');
                    if (resp['success']) {
                        globalHelper.methods.showPopupSuccess(resp.message, 'Success');
                        if (reFetch) {
                            fetch({ ...params });
                        }
                    }
                } catch (error) {
                    loaderStore.stopContextLoading('course-list');
                }
            },
            reject: () => {
                //callback to execute when user rejects the action
            },
            onHide: () => {
                //Callback to execute when dialog is hidden
            },
        });
    };

    /* Sync to Moodle*/
    const onSync = async (id, type = 'sync', message = '', reFetch = true, params = {}) => {
        const $confirm = useConfirm();
        const postData = {
            type: type,
            ...(type === 'bulk-sync' ? { ids: id } : { id: id }),
        };

        $confirm.require({
            message: message || `Are you sure you want to ${type} this course to Moodle?`,
            header: `${{ 're-sync': 'Re-sync', 'bulk-sync': 'Bulk Sync' }[type] || 'Sync'} To Moodle`,
            //icon: "pi pi-exclamation-triangle",
            //svgType: type,
            accept: async () => {
                loaderStore.startContextLoading(`course_moodle_sync_${id}`);
                try {
                    let resp = await $http.post(route('spa.courses.synctomoodle'), postData);
                    loaderStore.stopContextLoading(`course_moodle_sync_${id}`);
                    if (resp['success']) {
                        globalHelper.methods.showPopupSuccess(resp.message, 'Success');
                        if (reFetch) {
                            fetch({ ...params });
                        }
                    }
                } catch (error) {
                    //console.error(error);
                    loaderStore.stopContextLoading(`course_moodle_sync_${id}`);
                }
            },
            reject: () => {
                //callback to execute when user rejects the action
            },
            onHide: () => {
                //Callback to execute when dialog is hidden
            },
        });
    };

    return {
        state,
        fetch,
        onRemove,
        onSync,
        fromSync,
        onStatusChange,
    };
}

export const courseGeneral = (inputData) => {
    let parsedDate =
        inputData.superseded_date !== '0000-00-00' && inputData.superseded_date !== ''
            ? new Date(inputData.superseded_date)
            : null;
    if (parsedDate != null && isNaN(parsedDate.getTime())) {
        parsedDate = null;
    }
    let formattedData = {
        id: parseInt(inputData.id) || null,
        college_id: parseInt(inputData.college_id) || null,
        is_vet_synced: !!inputData.is_vet_synced,
        course_type_id: parseInt(inputData.course_type_id) || null,
        module_delivery: inputData.module_delivery || false,
        is_superseded: inputData.is_superseded || false,
        superseded_date: parsedDate,
        is_super_code: inputData.is_super_code || '',
        national_code: inputData.national_code || '',
        course_code: inputData.course_code || '',
        cricos_code: inputData.cricos_code || '',
        is_cricos_code: !!inputData.is_cricos_code,
        course_name: inputData.course_name || '',
        core_units: inputData.core_units || null,
        elective_units: inputData.elective_units || null,
        delivery_target: inputData.delivery_target || 0,
        qualification_prerequisite_id: inputData.qualification_prerequisite_id || 0,
        course_recognition_id: inputData.course_recognition_id,
        level_of_education_id: inputData.level_of_education_id,
        field_of_education_id: inputData.field_of_education_id,
        ANZSCO_code: inputData.ANZSCO_code,
        total_nominal_hours: parseInt(inputData.total_nominal_hours),
        AVETMISS_Report: inputData.AVETMISS_Report || false,
        qtac_course_id: inputData.qtac_course_id,
        course_description: inputData.course_description || '',
        course_summary: inputData.course_summary || '',
        entry_requirements: inputData.entry_requirements || '',
        identity_proof: inputData.identity_proof || '',
        superseded_course: inputData.superseded_course || null,
        core_units_number: parseInt(inputData.core_units_number) || null,
        elective_units_number: parseInt(inputData.elective_units_number) || null,
        activated_now: !!inputData.activated_now,
        major: inputData.major || '',
        course_tags: inputData.course_tags || '',
        current_cover_image: inputData.cover_image ?? '',
        cover_image: [],
    };
    return formattedData;
};
export const testCourseGeneral = (inputData) => {
    let formattedData = {
        firstName: inputData.firstName || null,
        lastName: inputData.lastName || null,
        email: inputData.email || null,
    };
    return formattedData;
};
export const courseFeeDuration = (inputData) => {
    let startDate = inputData.effective_start ? new Date(inputData.effective_start) : null;
    if (startDate != null && isNaN(startDate.getTime())) {
        startDate = null;
    }
    let endDate = inputData.effective_end ? new Date(inputData.effective_end) : null;
    if (endDate != null && isNaN(endDate.getTime())) {
        endDate = null;
    }
    let formattedData = {
        id: parseInt(inputData.id) || null,
        college_id: parseInt(inputData.college_id) || null,
        course_type_id: parseInt(inputData.course_type_id) || null,
        delivery_target: inputData.delivery_target || 0,
        activated_now: !!inputData.activated_now,
        flexible_attendance: !!inputData.flexible_attendance,
        course_duration: parseFloat(inputData.course_duration) || null,
        couse_duration_type: inputData.couse_duration_type || 2,
        online_hours: parseFloat(inputData.online_hours) || null,
        face_to_face_hours: parseFloat(inputData.face_to_face_hours) || null,
        tuition_fee: parseFloat(inputData.tuition_fee) || null,
        domestic_fee: parseFloat(inputData.domestic_fee) || null,
        maximum_weekly_study: parseFloat(inputData.maximum_weekly_study) || null,
        face_to_face_hours: parseFloat(inputData.face_to_face_hours) || null,
        effective_start: startDate,
        effective_end: endDate,
        fee_recurring_type: inputData.fee_recurring_type || null,
        is_fee_recurring: inputData.is_fee_recurring || false,
        seat_limit: inputData.seat_limit || null,
        is_seat_fixed: inputData.is_seat_fixed || false,
    };
    return formattedData;
};
export const courseFaculty = (inputData, allCampusesData) => {
    const selectedCampuses = inputData.campus_list ? inputData.campus_list.split(',') : [];
    const selectedCampusesNum = selectedCampuses.map(Number);
    const updatedCampuses = allCampusesData.reduce((acc, item) => {
        const key = `campus_list_${item.id}`;
        acc[key] = selectedCampusesNum.includes(item.id);
        return acc;
    }, {});
    let formattedData = {
        id: parseInt(inputData.id) || null,
        college_id: parseInt(inputData.college_id) || null,
        course_type_id: parseInt(inputData.course_type_id) || null,
        delivery_target: inputData.delivery_target || 0,
        activated_now: !!inputData.activated_now,
        results_calculation_methods: parseInt(inputData.results_calculation_methods) || null,
        course_delivery_type: inputData.course_delivery_type || '',
        work_placement: !!inputData.work_placement,
        work_placement_component_type: inputData.work_placement_component_type || null,
        work_placement_hour: parseFloat(inputData.work_placement_hour) || null,
        placement_officer_id: inputData.placement_officer_id || 0,
        vocational_duration: parseFloat(inputData.vocational_duration) || null,
        vocational_startweek: parseFloat(inputData.vocational_startweek) || null,
        couse_level: !!inputData.couse_level,
        fee_help: !!inputData.fee_help,
        free_help_study_type: parseInt(inputData.free_help_study_type) || '',
        free_help_study_load: parseFloat(inputData.vocational_startweek) || null,
        is_tcsi: !!inputData.is_tcsi,
        course_completion_type: inputData.course_completion_type || '',
        course_credit_point: parseFloat(inputData.course_credit_point) || null,
        faculty_id: inputData.faculty?.id || '',
        department_id: inputData.department?.id || '',
        faculty: inputData.faculty
            ? { id: inputData.faculty?.id, label: inputData.faculty?.name }
            : [],
        department: inputData.department
            ? {
                  id: inputData.department?.id,
                  label: inputData.department?.name,
              }
            : [],
        campuses: allCampusesData,
    };
    formattedData = Object.assign({}, updatedCampuses, formattedData);
    return formattedData;
};

export const courseAvetmiss = (inputData) => {
    let formattedData = {
        id: parseInt(inputData.id) || null,
        college_id: parseInt(inputData.college_id) || null,
        course_type_id: parseInt(inputData.course_type_id) || null,
        delivery_target: inputData.delivery_target || 0,
        activated_now: !!inputData.activated_now,
        course_recognition_id: parseInt(inputData.course_recognition_id) || 0,
        level_of_education_id: parseInt(inputData.level_of_education_id) || 0,
        field_of_education_id: parseInt(inputData.field_of_education_id) || 0,
        ANZSCO_code: inputData.ANZSCO_code || '',
        total_nominal_hours: parseFloat(inputData.total_nominal_hours) || null,
        qtac_course_id: parseInt(inputData.qtac_course_id) || null,
        course_description: inputData.course_description || '',
    };
    return formattedData;
};

export const courseSubjectsData = (inputData, course) => {
    const delivery_modes = (inputData.delivery_mode || 'NNN')
        .split('')
        .map((letter) => letter.toUpperCase());
    const courseId = course?.id || null;
    const isVetCourse = course?.course_type_id != 17 && course?.course_type_id != 1;
    const defaultGrading = !isVetCourse ? 14 : 22;
    let formattedData = {
        id: inputData.id || null,
        subject_id: inputData.subject_id || null, //subject_id of old setup
        course_subject_id: inputData.course_subject_id || null, //course_subject_id of old setup
        map_id: inputData.map_id || null,
        course_id: courseId || null,
        course_type: inputData.course_type || null,
        subject_code: inputData.subject_code || '',
        subject_name: inputData.subject_name || '',
        grading_type: inputData.grading_type || defaultGrading,
        max_marks_allow: inputData.max_marks_allow || null,
        contact_hours: inputData.contact_hours || null,
        level: inputData.level || null,
        credit_point: inputData.credit_point || null,
        is_long_indicator: !!inputData.is_long_indicator || false,
        is_assessment: !!inputData.is_assessment || true,
        discipline_broad_type: parseInt(inputData.discipline_broad_type) || 0,
        discipline_narrow_type: parseInt(inputData.discipline_narrow_type) || 0,
        discipline_narrow_sub_type: parseInt(inputData.discipline_narrow_sub_type) || 0,
        course_stage: inputData.course_stage || '',
        subject_type: inputData.subject_type || 'Core',
        subject_fee: inputData.subject_fee || null,
        domestic_subject_fee: inputData.domestic_subject_fee || null,
        unit_type: inputData.unit_type || '',
        delivery_mode: inputData.delivery_mode || null,
        delivery_mode_internal: delivery_modes[0] == 'Y' ? true : false,
        delivery_mode_external: delivery_modes[1] == 'Y' ? true : false,
        delivery_mode_workplace: delivery_modes[2] == 'Y' ? true : false,
        EFTSL_study_load: inputData.EFTSL_study_load || null,
        is_active:
            typeof inputData.is_active === 'undefined' || inputData.is_active === null
                ? true
                : !!inputData.is_active,
        inc_in_certificate:
            typeof inputData.inc_in_certificate === 'undefined' ||
            inputData.inc_in_certificate === null
                ? true
                : !!inputData.inc_in_certificate,
        units: inputData.assigned_units || [],
    };
    return formattedData;
};
export const courseSubjectsDropdownData = (inputData) => {
    return {
        id: parseInt(inputData.id) || 0,
        text: [inputData.subject_code || null, inputData.subject_name || null].join(': '),
    };
};

export const unitEditData = (inputData, courseId, isVet) => {
    console.log(inputData);
    const delivery_modes = (inputData.delivery_mode || 'NNN')
        .split('')
        .map((letter) => letter.toUpperCase());
    let AVETMISS_Report =
        inputData.AVETMISS_Report !== undefined && inputData.AVETMISS_Report !== null
            ? parseInt(inputData.AVETMISS_Report)
            : isVet === true;

    let vet_flag =
        inputData.vet_flag !== undefined && inputData.vet_flag !== null
            ? inputData.vet_flag == 'Yes'
            : isVet === true;
    const subjectData = inputData.subject_details ?? [];
    /*
    EFTSL_study_load: null
    
*/
    let formattedData = {
        id: parseInt(inputData.id) || null,
        subject_id: parseInt(inputData.subject_id) || null,
        course_subject_id: parseInt(inputData.course_subject_id) || null,
        course_id: parseInt(courseId) || null,
        solo_subject: inputData.solo_subject || false,
        unit_type: inputData.unit_type || 'Elective',
        tution_fees: parseFloat(inputData.tution_fees) || null,
        domestic_tution_fees: parseFloat(inputData.domestic_tution_fees) || null,
        delivery_mode: inputData.delivery_mode || 'NNN',
        delivery_mode_internal: inputData.internal == 'Y' ? true : false,
        delivery_mode_external: inputData.external == 'Y' ? true : false,
        delivery_mode_workplace: inputData.workplace_based_delivery == 'Y' ? true : false,
        nominal_hours: inputData.nominal_hours || null,
        AVETMISS_Report: inputData.AVETMISS_Report ? true : false,
        field_education: parseInt(inputData.field_education) || 0,
        unit_code: inputData.unit_code || '',
        vet_unit_code: inputData.vet_unit_code || '',
        unit_name: inputData.unit_name || '',
        description: inputData.description || '',
        module_unit_flag: inputData.module_unit_flag == 'M' ? 'M' : 'c',
        vet_flag: vet_flag,
        work_placement: inputData.work_placement == 'Yes' ? true : false,
        AVETMISS_Report: AVETMISS_Report == 1,
        status: inputData.status == 1,
        unit_source: inputData.unit_source ? inputData.unit_source : 'custom',
        EFTSL_study_load: subjectData.EFTSL_study_load ?? null,
        /* subject based data */
        course_stage: subjectData.course_stage ?? null,
        credit_point: subjectData.credit_point ?? null,
        discipline_broad_type: subjectData.discipline_broad_type ?? null,
        discipline_narrow_sub_type: subjectData.discipline_narrow_sub_type ?? null,
        discipline_narrow_type: subjectData.discipline_narrow_type ?? null,
        grading_type: subjectData.grading_type ?? null,
        inc_in_certificate: subjectData.inc_in_certificate == 1,
        is_assessment: subjectData.is_assessment == 1,
        is_long_indicator: subjectData.is_long_indicator == 1,
        level: subjectData.level ?? null,
        max_marks_allow: subjectData.max_marks_allow ?? null,
    };
    return formattedData;
};
export const higheredInfo = (inputData, course) => {
    let formattedData = {
        id: parseInt(inputData.id) || null,
        course_code: course?.course_code || null,
        course_name: course?.course_name || null,
        course_id: parseInt(course?.id) || null,
        course_fee_type: inputData.course_fee_type || '0',
        student_contribution_fee: inputData.student_contribution_fee || '0',
        study_type: inputData.study_type || '0',
        study_load: inputData.study_load || '0',
        entry_cut_off: inputData.entry_cut_off || '0',
        entry_cut_off_csp: inputData.entry_cut_off_csp || '0',
        eligibility_score: inputData.eligibility_score || '0',
        qld_entry_cut_off: inputData.qld_entry_cut_off || '0',
        qld_entry_cut_off_csp: inputData.qld_entry_cut_off_csp || '0',
        qld_eligibility_score: inputData.qld_eligibility_score || '0',
        tac_offer: inputData.tac_offer || '0',
        type_of_operation: inputData.type_of_operation || '0',
        principal_mode_of_delivery_of_offshore_course:
            inputData.principal_mode_of_delivery_of_offshore_course || '0',
        offshore_delivery_indicator: inputData.offshore_delivery_indicator || '0',
        additional_entrance_criteria: inputData.additional_entrance_criteria || '0',
        special_course_type: inputData.special_course_type || '0',
        field_of_education_broad_type: parseInt(inputData.field_of_education_broad_type) || '0',
        field_of_education_narrow_type: parseInt(inputData.field_of_education_narrow_type) || '0',
        field_of_education_narrow_sub_type:
            parseInt(inputData.field_of_education_narrow_sub_type) || '0',
        field_of_education_supplementary_broad_type:
            parseInt(inputData.field_of_education_supplementary_broad_type) || '0',
        field_of_education_supplementary_narrow_type:
            parseInt(inputData.field_of_education_supplementary_narrow_type) || '0',
        field_of_education_supplementary_narrow_sub_type:
            parseInt(inputData.field_of_education_supplementary_narrow_sub_type) || '0',
        combined_course_of_study: inputData.combined_course_of_study || '0',
        areas_of_study: inputData.areas_of_study || '0',
        course_search_keywords: inputData.course_search_keywords || '0',
    };
    return formattedData;
};
export const courseIntakes = (inputData) => {
    return inputData.map((intake) => {
        const intake_start_end = { start: intake.intake_start, end: intake.intake_end };
        const intake_class_start_end = {
            start: intake.class_start_date,
            end: intake.class_end_date,
        };
        const class_timings = { start: intake.class_start_time, end: intake.class_end_time };
        let course_fee_text = '';
        if (intake.intake_receiver == 'Both') {
            course_fee_text =
                intake.course_fee +
                (intake.course_international_fee ? ` (${intake.course_international_fee})` : '');
        } else if (intake.intake_receiver == 'Domestic') {
            course_fee_text = intake.course_fee;
        } else {
            course_fee_text = intake.course_international_fee;
        }
        return {
            id: intake.id,
            intake_name: intake.intake_name,
            intake_year: intake.intake_year,
            intake_duration: intake.intake_duration,
            intake_receiver: intake.intake_receiver,
            batch_number: intake.batch_number,
            intake_start_end: intake_start_end,
            class_start_end_date: intake_class_start_end,
            class_timings: class_timings,
            intake_seats: intake.intake_seats,
            restrict_enrollments_to_seats: intake.restrict_enrollments_to_seats,
            intake_seats_text: intake.intake_seats
                ? (intake.restrict_enrollments_to_seats ? ' Only' : '+/-') +
                  ` ${intake.intake_seats}`
                : '',
            course_fee: intake.course_fee,
            course_international_fee: intake.course_international_fee,
            course_fee_text: course_fee_text,
            campuses: intake.campuses,
            created_at: intake.created_at,
            created_text: intake.created_text,
            creator: intake.creator,
            updated_at: intake.updated_at,
            updated_text: intake.updated_text,
            updater: intake.updater,
            active: intake.active,
            status: intake.active ? 'Active' : 'Inactive',
            selected: false,
            action_buttons: [
                {
                    text: 'View Detail',
                    value: 'info',
                    icon: 'eye',
                    type: 'emit',
                    action: null,
                },
                {
                    text: 'Edit',
                    value: 'edit',
                    icon: 'edit',
                    type: 'emit',
                    action: null,
                },
                {
                    text: 'Delete',
                    value: 'delete',
                    icon: 'delete',
                    type: 'emit',
                    action: null,
                },
            ],
        };
    });
};
export const prepareIntakeFormData = (course, fees, intake) => {
    let startTime = intake.class_timings?.start || '';
    let endTime = intake.class_timings?.start || '';
    startTime = startTime.split(':');
    endTime = endTime.split(':');
    return {
        id: intake.id || null,
        course_id: intake.course_id || course.id || null,
        course_type: course.course_type_id || null,
        intake_name: intake.intake_name || '',
        intake_duration: intake.intake_duration,
        intake_receiver:
            intake.id == null ? course.delivery_target || null : intake.intake_receiver || null,
        batch_number: intake.batch_number,
        intake_start: intake.intake_start_end?.start || '',
        intake_end: intake.intake_start_end?.end || '',
        class_start_date: intake.class_start_end_date?.start || '',
        class_end_date: intake.class_start_end_date?.end || '',
        class_start_time: intake.class_timings?.start || '',
        class_start_hour: parseInt(startTime[0] ?? '00') || null,
        class_start_minute: parseInt(startTime[1] ?? '00') || null,
        class_end_hour: parseInt(endTime[0] ?? '00') || null,
        class_end_minute: parseInt(endTime[1] ?? '00') || null,
        intake_seats: intake.intake_seats || null,
        restrict_enrollments_to_seats: intake.restrict_enrollments_to_seats || 0,
        course_fee: intake.id == null ? fees.tuition_fee || 0 : intake.course_fee || 0,
        course_domestic_fee:
            intake.id == null ? fees.domestic_fee || 0 : intake.course_domestic_fee || 0,
        recurring_class: intake.recurring_class || 'non-recurring',
        intake_status: intake.id ? intake.active || 0 : 1,
        campuses: intake.campuses?.map((campus) => campus.campus_id).join(','),
    };
};
