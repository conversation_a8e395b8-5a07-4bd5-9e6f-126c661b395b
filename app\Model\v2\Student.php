<?php

namespace App\Model\v2;

use App\Classes\SiteConstants;
use App\Helpers\Helpers;
use App\Repositories\StudentRepository;
use App\Users;
use Domains\Xero\Traits\XeroableContact;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Integrations\Base\Models\IntegrationItem;
use Integrations\Base\Traits\ThirdPartySyncableItem;
use Integrations\Zoho\Contracts\CanConnectToZoho;
use Integrations\Zoho\Contracts\CanUploadToZoho;
use Integrations\Zoho\Entities\ZohoPayload;
use Integrations\Zoho\Factories\ZohoStudentPayloadFactory;
use Integrations\Zoho\Traits\ZohoSyncableItem;
use Integrations\Zoho\Traits\ZohoUploadableItem;
use Laravel\Scout\Searchable;
use Notifications\Notifiable;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Contracts\IsGloballySearchable;
use Support\Services\UploadService;
use Support\Traits\CreaterUpdaterTrait;
use Support\Traits\HasSecureId;
use Support\Traits\TenantAwareSearchable;
use Zoha\Metable;

class Student extends Model implements CanConnectToZoho, CanUploadToZoho, IsGloballySearchable
{
    use CreaterUpdaterTrait;
    use HasFactory;
    use HasSecureId;
    use LogsActivity;
    use Metable;
    use Notifiable;
    use TenantAwareSearchable;
    use ThirdPartySyncableItem;
    use XeroableContact;
    use ZohoSyncableItem;
    use ZohoUploadableItem;

    protected $table = 'rto_students';

    const ACTIVE_STUDENT = '1';   // "is_student" value 1 for student active

    protected $fillable = [
        'name_title',
        'first_name',
        'middel_name',
        'family_name',
        'gender',
        'college_id',
        'application_reference_id',
        'status',
        'student_type',
        'email',
        'optional_email',
        'birthplace',
        'birth_country',
        'nationality',
        'passport_no',
        'passport_expiry',
        'DOB',
        'visa_status',
        'visa_number',
        'visa_expiry_date',
        'is_applicant',
        'current_country',
        'current_building_name',
        'current_unit_detail',
        'current_street_no',
        'current_street_name',
        'current_city',
        'current_state',
        'current_postcode',
        'current_mobile_phone_code',
        'current_mobile_phone',
        'current_home_phone',
        'permanent_country',
        'permanent_street_name',
        'postal_building_name',
        'postal_city',
        'postal_country',
        'postal_postcode',
        'postal_state',
        'postal_street_name',
        'postal_street_no',
        'postal_unit_detail',
        'is_postal_address',
        'USI',
        'is_usi_verified',
        'usi_invalid_reason',
        'read_and_agree',
        'usi_identity',
        'city_of_birth',
        'is_apply_usi',
        'profile_picture',
        'is_offered',
        'is_student',
        'created_by',
        'last_address_updated_date',
        'last_address_updated_date',
        'uuid',
        'stud_id_type',
        'generated_stud_id', // put for avitmess import
    ];

    protected $logAttributes = [
        'name_title',
        'generated_stud_id',
        'first_name',
        'middel_name',
        'family_name',
        'gender',
        'college_id',
        'application_reference_id',
        'usi_invalid_reason',
        'status',
        'student_type',
        'email',
        'optional_email',
        'birthplace',
        'birth_country',
        'nationality',
        'passport_no',
        'passport_expiry',
        'DOB',
        'visa_status',
        'visa_number',
        'visa_expiry_date',
        'is_applicant',
        'current_country',
        'current_building_name',
        'current_unit_detail',
        'current_street_no',
        'current_street_name',
        'current_city',
        'current_state',
        'current_postcode',
        'current_mobile_phone',
        'current_home_phone',
        'permanent_country',
        'permanent_street_name',
        'postal_building_name',
        'postal_city',
        'postal_country',
        'postal_postcode',
        'postal_state',
        'postal_street_name',
        'postal_street_no',
        'postal_unit_detail',
        'is_postal_address',
        'USI',
        'read_and_agree',
        'usi_identity',
        'city_of_birth',
        'is_apply_usi',
        'profile_picture',
        'is_offered',
        'is_student',
        'created_by',
        'updated_by',
        'last_address_updated_date',
    ];

    protected static function booted()
    {
        static::created(function ($student) {
            if (empty($student->application_reference_id)) {
                $student->generateUniqueApplicationReference();
                StudentApplicationProcess::create([
                    'student_id' => $student->id,
                    'user_id' => $student->created_by,
                    'last_status' => 'inprogress',
                ]);
            }
        });
    }

    public function generateUniqueApplicationReference(): string
    {
        do {
            $length = rand(6, 10);
            // Generate a lowercase alphanumeric reference key
            $reference = Str::upper(Str::random($length));
            // Check case-insensitive uniqueness in the database
            $exists = Student::whereRaw('UPPER(application_reference_id) = ?', [$reference])->exists();
        } while ($exists);

        $this->application_reference_id = $reference;
        $this->saveQuietly();

        return $reference;
    }

    public function getGalaxyLogNameAttribute(): string
    {
        return implode('-', [@$this->generated_stud_id]);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => ":subject.galaxy_log_name Student has been {$eventName}");
    }

    private function getOldAttributes()
    {
        // Here you can retrieve the old attributes from the database or model
        return $this->getOriginal(); // Assuming this method retrieves the model's original state
    }

    public function tapActivity(Activity $activity, string $eventName)
    {

        $activity->log_name = (new self)->getMorphClass();
        $activity->subject_type = self::class;

        if ($eventName != 'deleted') {
            $attributes = @$activity->properties['attributes'] ?? [];
            if (isset($attributes['current_country'])) {
                $attributes['current_country'] = $this->getCountryName($this->current_country);
            }
            // Modify the existing 'group_id' in the 'old' array
            $oldAttributes = [];
            if (isset($activity->properties['old'])) {
                $oldAttributes = $activity->properties['old'];
            }

            if (isset($oldAttributes['current_country'])) {
                $oldAttributes['current_country'] = $this->getCountryName($this->getOriginal('current_country'));
            }

            // Update the properties with the modified arrays
            $activity->properties = $activity->properties->put('attributes', $attributes);
            $activity->properties = $activity->properties->put('old', $oldAttributes);
            // Address-specific fields
            $addressFields = ['current_building_name', 'current_unit_detail', 'current_street_no', 'current_street_name', 'current_city', 'current_state', 'current_postcode', 'current_country'];
            $phoneFields = ['current_mobile_phone'];
            $emailFields = ['email'];
            $optionalEmailFields = ['optional_email'];
            $properties = json_decode($activity->properties, true);
            $changedAttributes = $properties['attributes'] ?? [];
            $allOldAttributes = $this->getOldAttributes();
            $changedAddressFields = array_intersect_key($changedAttributes, array_flip($addressFields));
            $changedMobileFields = array_intersect_key($changedAttributes, array_flip($phoneFields));
            $changedEmailFields = array_intersect_key($changedAttributes, array_flip($emailFields));
            $changedOptionalEmailFields = array_intersect_key($changedAttributes, array_flip($optionalEmailFields));
            if (! empty($changedAddressFields)) {
                $activity->log_name = 'student_address_'.$this->id;
                $activity->description = $this->getAddress($properties['attributes'], $allOldAttributes);
            } elseif (! empty($changedMobileFields)) {
                $activity->log_name = 'student_email_phone_'.$this->id;
                $activity->description = 'Mobile:: '.$properties['attributes']['current_mobile_phone'];
            } elseif (! empty($changedEmailFields)) {
                $activity->log_name = 'student_email_phone_'.$this->id;
                $activity->description = 'Email:: '.$properties['attributes']['email'];
            } elseif (! empty($changedOptionalEmailFields)) {
                $activity->log_name = 'student_email_phone_'.$this->id;
                $activity->description = 'Optional Email:: '.$properties['attributes']['optional_email'];
            }
        }

    }

    public function getCountryName($country_id)
    {
        return Country::find($country_id)->name ?? 'Unknown Country';
    }

    private function getAddress($attributes, $old): string
    {
        $components = [
            $this->getValue($attributes, 'current_street_no', $old, 'current_street_no'),
            $this->getValue($attributes, 'current_street_name', $old, 'current_street_name'),
            $this->getValue($attributes, 'current_city', $old, 'current_city'),
            $this->getValue($attributes, 'current_state', $old, 'current_state'),
            $this->getValue($attributes, 'current_postcode', $old, 'current_postcode'),
            $this->getValue($attributes, 'current_building_name', $old, 'current_building_name'),
            $this->getValue($attributes, 'current_country', $old, 'current_country'),
        ];

        return implode(',', array_filter($components));
    }

    private function getValue(array $new, string $newKey, array $old = [], string $oldKey = ''): ?string
    {
        return ! empty($new[$newKey]) ? $new[$newKey] : ($old[$oldKey] ?? null);
    }

    public function studentCourses()
    {
        return $this->hasMany(StudentCourses::class, 'student_id', 'id');
    }

    public function favouriteCourses()
    {
        return $this->hasMany(StudentFavouriteCourses::class, 'student_id', 'id');
    }

    public function studentEmployment()
    {
        return $this->hasMany(StudentEmployment::class, 'student_id', 'id');
    }

    public function studentEducation()
    {
        return $this->hasMany(StudentEducation::class, 'student_id', 'id')->with(['getqualification' => function ($query) {
            $query->select('id', 'avaitmiss_id', 'name');
        }]);
    }

    public function appprocess()
    {
        return $this->hasOne(StudentApplicationProcess::class, 'student_id', 'id');
    }

    public function getStudents($studentId)
    {
        return Student::find($studentId);
    }

    public function college()
    {
        return $this->belongsTo(Colleges::class, 'college_id');
    }

    /**
     * Current Relationship
     * student -> colleges -> campus
     * student.college_id -> colleges.id
     * colleges.id -> campus.college_id
     *
     * so directly
     * student.college_id -> campus.college_id should fetch the campus with one relation
     */
    public function collegeCampus()
    {
        return $this->hasOne(Checklist::class, 'college_id', 'college_id');
    }

    public function birthCountry()
    {
        return $this->hasOne(Country::class, 'id', 'birth_country');
    }

    public function studentNationality()
    {
        return $this->hasOne(Country::class, 'id', 'nationality');
    }

    public function currentCountry()
    {
        return $this->hasOne(Country::class, 'id', 'current_country');
    }

    public function premanentCountry()
    {
        return $this->hasOne(Country::class, 'id', 'permanent_country');
    }

    public function postalCountry()
    {
        return $this->hasOne(Country::class, 'id', 'postal_country');
    }

    public function studentDetails()
    {
        return $this->hasOne(StudentDetails::class, 'student_id', 'id');
    }

    public function enrollments()
    {
        return $this->hasMany(StudentSubjectEnrolment::class, 'student_id');
    }

    public function determineStudentOrigin()
    {
        $studentNationality = $this->studentNationality ?? null;
        $countryName = $studentNationality->name ?? null;
        if (! in_array($countryName, SiteConstants::DOMESTIC_COUNTRUES)) {
            return 'foreign';
        } else {
            return 'domestic';
        }
    }

    public static function GetLoggedInStudentId(Users $user, $get = 'id')
    {
        $collegeId = $user->college_id ?? null;
        $studentId = $user->username ?? null;

        return self::where('college_id', '=', $collegeId)->where('generated_stud_id', '=', $studentId)->value($get);
    }

    public function isApiAccessible(\App\Users|\App\Model\v2\Users $currentUser)
    {
        if ($currentUser->role_id == SiteConstants::STUDENT_ROLE_ID) {
            // check if the user is handling his/hers own application
            return $this->generated_stud_id == $currentUser->username || $currentUser->username == $this->email;
        }

        return true;
    }

    public function firstenrollments()
    {
        return $this->hasMany(StudentSubjectEnrolment::class, 'student_id', 'id')
            ->select(
                'rto_student_courses.id AS studentcourse_id',
                'rto_student_subject_enrolment.id',
                'rto_student_subject_enrolment.course_id',
                'rto_student_subject_enrolment.student_id',
                'rto_student_subject_enrolment.unit_id',
                'rto_student_subject_enrolment.final_outcome',
                'rsu.unit_name as unit_title',
                DB::raw("CONCAT(rsu.vet_unit_code, ' : ', rsu.unit_name) AS unit_name"),
                DB::raw("(CASE
                    WHEN rto_student_subject_enrolment.final_outcome = 'C' THEN 'green-500'
                    WHEN rto_student_subject_enrolment.final_outcome = 'NYC' THEN 'red-500'
                    ELSE 'gray-200'
                END) AS color"),
                DB::raw('(CASE
                    WHEN rto_student_subject_enrolment.activity_start_date <= CURDATE()
                    AND rto_student_subject_enrolment.activity_finish_date >= CURDATE()
                    THEN 1
                    ELSE 0
                END) AS is_active'),
                DB::raw("DENSE_RANK() OVER (PARTITION BY rto_student_subject_enrolment.student_id
                    ORDER BY
                        CASE
                            WHEN rto_student_courses.status = 'Current Student' THEN 'a1'
                            WHEN rto_student_courses.status = 'Enrolled' THEN 'a2'
                            ELSE rto_student_courses.status
                        END ASC
                ) AS row_num")
            )
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                    ->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
            })
            ->leftJoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id');
    }

    public function initialPayments()
    {
        return $this->hasMany(StudentInitialPayment::class, 'student_id');
    }

    public function associatedUserAccount()
    {
        return $this->belongsTo(Users::class, 'generated_stud_id', 'username');
    }

    public function getFullNameAttribute($val)
    {
        return implode(' ', [$this->first_name, /* $this->middel_name, */ $this->family_name]);
    }

    public function getLivingTypeAttribute($val)
    {
        if (strtolower($this->student_type) == 'offshore') {
            return 'Overseas';
        }
        if (strtolower($this->student_type) == 'onshore') {
            return 'Overseas';
        }

        return 'Domestic';
    }

    public function getProfilePicPath($imageType = 'thumb')
    {
        $thumbText = Config::get('constants.defaultThumbText');
        $smallThumbText = Config::get('constants.smallThumbText');
        $filePath = Config::get('constants.uploadFilePath.StudentPics');
        $destinationPath = Helpers::changeRootPath($filePath, $this->id);

        $imageName = ($imageType == 'original') ? $this->profile_picture : (($imageType == 'small') ? $smallThumbText : $thumbText).$this->profile_picture;

        $profile_pic = $destinationPath['default'].$this->profile_picture;
        $profile_pic_url = '';

        if (UploadService::exists($destinationPath['view'].$imageName)) {
            $profile_pic_url = UploadService::preview($destinationPath['view'].$imageName);
        }
        // if (file_exists($profile_pic) && ! empty($this->profile_picture)) {
        //     $profile_pic_url = asset($destinationPath['view'].$imageName);
        // } else {
        //     $profile_pic_url = '';
        // }

        return $profile_pic_url;
    }

    public function scopeForGlobalSearch(Builder $q): Builder
    {
        $q->where('is_student', '=', 1);

        return $q;
    }

    /** Modify the query used to retrieve models when making all the models searchable. */
    protected function makeAllSearchableUsing(Builder $query): Builder
    {
        return $query
            ->whereNotNull('generated_stud_id')
            ->whereHas('studentCourses')
            ->with(['studentCourses.course', 'studentDetails', 'enrollments.timetables']);
    }

    public function toSearchableArray($context = null): array
    {

        return [
            'id' => (int) $this->id,
            'first_name' => cleanNonUTF($this->first_name),
            'middel_name' => cleanNonUTF($this->middel_name),
            'family_name' => cleanNonUTF($this->family_name),
            'generated_stud_id' => cleanNonUTF($this->generated_stud_id),
            'generated_stud_id_last3' => (string) substr((string) $this->generated_stud_id, -3),
            'generated_stud_id_last4' => (string) substr((string) $this->generated_stud_id, -4),
            'generated_stud_id_last5' => (string) substr((string) $this->generated_stud_id, -5),
            'application_reference_id' => cleanNonUTF($this->application_reference_id),
            'current_mobile_phone' => cleanNonUTF($this->current_mobile_phone),
            'email' => cleanNonUTF($this->email),
            'optional_email' => cleanNonUTF($this->optional_email),
            'student_type' => cleanNonUTF($this->student_type),
            'living_type' => cleanNonUTF($this->livingType),
            'birthplace' => cleanNonUTF($this->birthplace),
            'gender' => cleanNonUTF($this->gender),
            'postal_street_no' => cleanNonUTF($this->postal_street_no),
            'postal_street_name' => cleanNonUTF($this->postal_street_name),
            'postal_po_box' => cleanNonUTF($this->postal_po_box),
            'postal_city' => cleanNonUTF($this->postal_city),
            'postal_state' => cleanNonUTF($this->postal_state),
            'postal_home_phone' => cleanNonUTF($this->postal_home_phone),
            'postal_work_phone' => cleanNonUTF($this->postal_work_phone),
            'postal_mobile_phone' => cleanNonUTF($this->postal_mobile_phone),
            'postal_postcode' => cleanNonUTF($this->postal_postcode),
            'permanent_country' => cleanNonUTF($this->permanent_country),
            'permanent_street_name' => cleanNonUTF($this->permanent_street_name),
            'permanent_city' => cleanNonUTF($this->permanent_city),
            'permanent_state' => cleanNonUTF($this->permanent_state),
            'permanent_postcode' => cleanNonUTF($this->permanent_postcode),
            'permanent_home_phone' => cleanNonUTF($this->permanent_home_phone),
            'permanent_work_phone' => cleanNonUTF($this->permanent_work_phone),
            'permanent_mobile_phone' => cleanNonUTF($this->permanent_mobile_phone),
            'current_state' => cleanNonUTF($this->current_state),
            'current_postcode' => cleanNonUTF($this->current_postcode),
            'postal_country' => cleanNonUTF($this->postal_country),
            'applicant' => cleanNonUTF($this->applicant),
            'current_work_phone' => cleanNonUTF($this->current_work_phone),
            'birth_country' => cleanNonUTF($this->birth_country),
            'city_of_birth' => cleanNonUTF($this->city_of_birth),
            'nationality' => cleanNonUTF($this->nationality),
            'passport_no' => cleanNonUTF($this->passport_no),
            'DOB' => cleanNonUTF($this->DOB),
            'is_student' => (int) $this->is_student,

            /* student details columns */
            // 'teacher_id' => $this->studentDetails ? $this->studentDetails->account_manager_id : null,

            'courses' => $this->studentCourses->map(fn ($item) => $item->toSearchableArray('student')),
            'enrollments' => $this->enrollments->map(fn ($item) => $item->toSearchableArray('student')),

        ];

    }

    public function getFullName()
    {
        return trim(implode(' ', array_filter([$this->first_name, $this->middel_name, $this->family_name])));
    }

    public function getShortName()
    {
        $shortFirstName = ! empty($this->first_name) ? $this->first_name[0] : '';
        $shortLastName = ! empty($this->family_name) ? $this->family_name[0] : (! empty($this->middel_name) ? $this->middel_name[0] : '');

        return strtoupper($shortFirstName.$shortLastName);
    }

    public function getProfilePicture()
    {
        return (new Helpers)->getStudentProfilePicPath($this->id, $this->profile_picture);
    }

    public function getZohoPayload(): ZohoPayload
    {
        $zohoItem = $this->zohoItem()->first();
        if ($zohoItem && $zohoItem->isConvertedToContact()) {
            return ZohoStudentPayloadFactory::CreateCurrentStudent($this);
        }

        return ZohoStudentPayloadFactory::CreateFutureStudent($this);
    }

    public function integrationItem()
    {
        return $this->morphOne(IntegrationItem::class, 'syncable');
    }

    public function getStudentId($collegeId, $studentId)
    {
        return Student::where('college_id', '=', $collegeId)->where('generated_stud_id', '=', $studentId)->get(['id'])->toarray();
    }

    public function getStudentNationalReport($collegeId, $userId)
    {

        $agentId = Agent::where('college_id', $collegeId)->where('user_id', $userId)->value('id');

        return self::leftjoin('rto_student_courses', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_country', 'rto_students.current_country', '=', 'rto_country.id')
            ->where('rto_student_courses.agent_id', $agentId)
            ->where('rto_student_courses.offer_status', 'Enrolled')
            ->groupBy(['rto_country.id'])
            ->get(['rto_country.name as countryName', DB::raw('COUNT( rto_students.`id`) AS total_students')]);
    }

    public function getStudentDetails($studentId, $studentCourseId = '')
    {

        $objstudentDetails = Student::leftjoin('rto_student_details as rsd', 'rto_students.id', '=', 'rsd.student_id')
            ->leftjoin('rto_student_education as rst', 'rto_students.id', '=', 'rst.student_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.nationality')
            ->leftjoin('rto_english_test as ret', 'rsd.EPL_test_name', '=', 'ret.id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->leftjoin('rto_employment_status as res', 'rsd.current_employment_status', '=', 'res.id')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as cour', 'cour.id', '=', 'rsc.course_id')
            ->leftjoin('rto_setup_section as rvc', 'rvc.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.current_country')
            ->select('rto_students.*', 'rsd.*', 'rst.*', 'ret.name as english_name', 'rsc.issued_date as issued_date', 'cour.course_name as course_name', 'rsc.start_date as start_date', 'rsc.finish_date as finish_date', 'rsc.enroll_fee as enroll_fee', 'rsc.course_id as course_id', 'rsc.course_fee as course_fee', 'rsc.course_upfront_fee as course_upfront_fee', 'rsc.course_material_fee as course_material_fee', 'rsc.offer_id as offer_id', 'ra.agency_name', 'rc.name as campus_name', 'rvc.value as visa_type', 'rto_country.nationality as stud_nationality', 'rto_country.name as country_name', 'rto_colleges.college_name', 'country.nationality as nationality_of_student')
            ->where('rto_students.id', '=', $studentId);
        if ($studentCourseId != '') {
            $objstudentDetails->where('rsc.id', '=', $studentCourseId);
        }
        $studentDetails = $objstudentDetails->get();
        $studentDetails[0]->id = $studentId;

        return $studentDetails;
    }

    public function getExistsReferenceId($studentId, $collegeId)
    {
        return Student::where('college_id', '=', $collegeId)
            ->where('id', '=', $studentId)
            ->select('id', 'college_id', 'application_reference_id', 'email', 'generated_stud_id', 'name_title', 'first_name', 'middel_name', 'student_type', 'generated_stud_id', DB::raw('concat(name_title, first_name, " ", family_name) as fullName'))
            ->get();
    }

    public function getAreaofDisability($areaOfDisability)
    {
        $arrAreaOfDisabilitys = explode(',', $areaOfDisability);

        $arrAreaOfDisabilityName = [];

        for ($i = 0; $i < count($arrAreaOfDisabilitys); $i++) {
            $arrAreaOfDisability = $arrAreaOfDisabilitys[$i];
            $getName = AreaOfDisability::select('name')->where('avaitmiss_id', $arrAreaOfDisability)->get()->toArray();
            if (count($getName) > 0) {
                $arrAreaOfDisabilityName[] = $getName[0]['name'];
            }
        }

        $data = implode(', ', $arrAreaOfDisabilityName);

        return $data;
    }

    public function generateStudentId()
    {
        $collegeID = $this->college_id ?? 3;
        $studID = $this->id ?? null;
        if (! $studID) {
            return;
        }
        $studIDFormate = new StudentIdFormate;
        $studentIdFormate = $studIDFormate->getStudentIdFormate($collegeID);
        $alphabeat = $studentIdFormate->alphabeat;
        $yeardigit = $studentIdFormate->yeardigit;
        $auto_no = $studentIdFormate->auto_increment;

        $str = '';
        $positions = [
            'position1' => $studentIdFormate->position1,
            'position2' => $studentIdFormate->position2,
            'position3' => $studentIdFormate->position3,
        ];
        // Prepare variables
        $str = '';
        // Loop through the positions
        foreach ($positions as $position => $value) {
            switch ($value) {
                case 'countrycode':
                    $str .= '061';
                    break;
                case 'alphabeat':
                    if (! empty($alphabeat)) {
                        $str .= $alphabeat;
                    }
                    break;
                case 'yeardigit':
                    if (! empty($yeardigit)) {
                        $str .= $yeardigit;
                    }
                    break;
            }
        }
        $generateID = $str.$auto_no;
        $this->generated_stud_id = $generateID;
        $this->save();
        $studIDFormate = StudentIdFormate::where('college_id', $collegeID)->first();
        $studIDFormate->auto_increment = ($studIDFormate->auto_increment + 1);
        $studIDFormate->save();

        return $generateID;
    }

    public static function getLoginStudentInfo()
    {
        return Student::where('generated_stud_id', auth()->guard()->user()->username)->get(['first_name', 'id'])->first();
    }

    public function addCourseAsFavourite(Courses|int $course, $initiator = null, $toggle = false)
    {
        if (! $course instanceof Courses) {
            $course = Courses::find($course);
        }
        if (! $course) {
            return false;
        }
        $addedFrom = ($initiator == 'enroll') ? 'enroll_action' : (($initiator == 'click') ? 'click_action' : 'others');
        // check if the course is available
        $favCourse = StudentFavouriteCourses::where(['course_id' => $course->id, 'student_id' => $this->id])->first();
        $fav = 1;
        $saveData = [
            'student_id' => $this->id,
            'course_id' => $course->id,
            'is_favourite' => $fav,
            'added_from' => $addedFrom,
        ];

        if (! $favCourse) {
            $favCourse = new StudentFavouriteCourses($saveData);
            $favCourse->save();
        } else {
            if ($toggle) {
                /* the course will be removed from fav if added as favourite and will be added if not */
                $fav = $favCourse->is_favourite == 1 ? 0 : 1;
                $favCourse->is_favourite = $fav;
                $favCourse->save();
            } elseif ($course->is_favourite != 1) {
                /* the course will be added to favourite if it was removed from fav previously */
                $favCourse->is_favourite = 1;
                $favCourse->save();
            }
        }

        return $fav;
    }

    public function scopeQuery($query, $value)
    {
        if (! $value) {
            return $query;
        }
        $studentRepository = new StudentRepository($this);
        $studentIds = $studentRepository->getFilteredStudentIds(
            $value,
        );

        return $query->whereIn('id', $studentIds);
    }

    public function scopeHasUsi($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }
        if ($value == 1) {
            return $query->whereNotNull('USI');
        } else {
            return $query->whereNull('USI');
        }
    }

    public function scopeHasID($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value) {
            return $query->whereNotNull('generated_stud_id');
        }

        return $query->whereNull('generated_stud_id');
    }

    public function scopeUsiStatus($query, $value)
    {
        if (! $value) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }
        if ($value == 1) {
            return $query->where('is_usi_verified', 1);
        } else {
            return $query->where('is_usi_verified', 0);
        }
    }

    public function scopeCourseTypeIds($query, $ids)
    {
        if (! $ids) {
            return $query;
        }
        if ($ids == 'all') {
            return $query;
        }

        return $query->whereHas('studentCourses', function ($q) use ($ids) {
            $q->whereIn('course_type_id', $ids);
        });
    }
}
