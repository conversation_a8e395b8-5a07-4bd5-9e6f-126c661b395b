@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')

<div class="main-conntent">
@if ( $errors->count() > 0 )
<section class="content server-side-validation">
    <div class="row">
        <div class="col-md-12">
            <p>The following errors have occurred:</p>
            <ul class="error-list">
                @foreach( $errors->all() as $message )
                <li>{{ $message }}</li>
                @endforeach
            </ul>
        </div>
    </div>
</section>
@endif

<section class="content">
    <div class="row">
            <div class="col-md-12">
                <div class="custom-header">
                    <div class="row">
                        <div class="col-md-12">
                            <span class="pull-right add-btn-block">
                                <span class="{{ (Route::current()->getName() == 'student-payment-summary') ? 'icon-active-action' : '' }}">
                                       <a href="javascript:;" data-original-title="Create New Payment Schedule" data-target="#" data-toggle="modal" class="generateSchedule">
                                        <div class="btn-add"><i class="fa fa-plus"></i></div>
                                    </a>
                                </span>
<!--                                <a href="javascript:;" data-toggle="tooltip" id="combineLink" class="combineLink" data-original-title="Create Combine Payment Schedule">
                                    <div class="btn-add"><i class="fa fa-copy"></i></div>
                                </a>-->
                                <a href="javascript:;" data-toggle="tooltip" id="statementOfAccount" class="statementOfAccount" data-original-title="Generate Statement of Account">
                                    <div class="btn-add"><i class="fa fa-file-pdf"></i></div>
                                </a>
                                <a class="studentInvoice" data-original-title="Generate Payment Schedule Invoice" data-toggle="tooltip"  >
                                    <div class="btn-add"><i class="fa fa-file-alt"></i></div>
                                </a>
                                <a href="{{ route('add-student-sanction', array('id' => $student_id)) }}" data-toggle="tooltip" data-original-title="Apply Student Transaction" >
                                    <div class="btn-add"><i class="fa fa-ban"></i></div>
                                </a>
                                <a href="{{ route('student-communication-log', array('id' => $student_id)) }}" data-toggle="tooltip" data-original-title="Diary" >
                                    <div class="btn-add"><i class="fa fa-book"></i></div>
                                </a>
                                <span class="{{ (Route::current()->getName() == 'student-payment-checklist') ? 'icon-active-action' : '' }}">
                                    <a href="{{ route('student-payment-checklist', array('id' => $student_id)) }}" data-toggle="tooltip" data-original-title="View Payment Checklist" >
                                        <div class="btn-add"><i class="fa fa-calendar"></i></div>
                                    </a>
                                </span>
                                <a href="{{ route('student-account') }}" data-toggle="tooltip" data-original-title="Go back" >
                                    <div class="btn-add"><i class="fa fa-reply"></i></div>
                                </a>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="nav-tabs-custom">
                    <ul class="nav nav-tabs">
                        <li class="active"><a href="javascript:;">Summary</a></li>
                        <li><a href="{{ route('student-initial-payment', array('id'=>$student_id)) }}">Initial</a></li>
                        <li><a href="{{ route('student-miscellaneous-payment', array('id'=>$student_id)) }}">Miscellaneous</a></li>
                        <li><a href="{{ route('student-service-paymentv2',array('id'=>$student_id)) }}">Service</a></li>
                        <li><a href="{{ route('student-agent-commission', array('id'=>$student_id)) }}">Agent Commission</a></li>
                        <li><a href="{{ route('student-refund-history', array('id'=>$student_id, 'refund_history'=>'0')) }}" id="student_refund_href">Refund History</a></li>
                        <li><a href="{{ route('student-transfer-payment', array('id'=>$student_id)) }}">Transfer</a></li>
                        <li><a href="{{ route('student-scholarship', array('id'=>$student_id)) }}">Student Scholarship</a></li>
                    </ul>
                    <div class="tab-content">
                        <div class="active tab-pane padding-30">
			            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="student_id" id="student_id" value="{{ $student_id }}">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="box box-info">
                                        <div class="custom-header">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h3 class="box-title">Additional Service Information Request</h3>
                                                </div>
                                                <div class="col-md-4">
                                                    <span class="pull-right">
                                                        <h5> Student Name : <strong>{{ (!empty($applicationRefID->fullName)) ? $applicationRefID->fullName : '' }}</strong></h5>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="box-body">
                                            <div class="row margin-b-30">
                                                <div class="form-group col-sm-3">
                                                    <label for="inputEmail3" class="control-label"> Student ID </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="application_id">{{ $objStudentsLists[0]->generated_stud_id }}</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-6">
                                                    <label for="inputEmail3" class="control-label"> Courses Enrolled </label>
                                                    <div class="">
                                                        {{ Form::select('course_id', $arrStudentCourse , null, array('class' => 'form-control', 'id' => 'course_id')) }}
                                                        {{ Form::hidden('sessionPermissionData', $sessionPermissionData ,array('class' => 'form-control', 'id' => 'sessionPermissionData')) }}
                                                        {{ Form::hidden('allowToModifyScheduleInvoice', $allowToModifyScheduleInvoice ,array('class' => 'form-control', 'id' => 'allowToModifyScheduleInvoice')) }}
                                                    </div>   
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <div class='clearfix'></div>
                                                </div>
                                            </div>
                                            <div class="row margin-b-30">
                                                <div class="form-group col-sm-3">
                                                    <label for="nickname" class="control-label">Agent Name </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="nickname" id="agent_name">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="student_type" class="control-label">Course Duration  </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="course_duration">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="gender" class="control-label">Total Course Fee </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="gender" id="course_fee">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="dob" class="control-label">Total Course Fee Balance Due </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="dob" id="totalCourseFeeBalanceDue">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row margin-b-30">
                                                <div class="form-group col-sm-3">
                                                    <label for="contry_birth" class="control-label">Invoiced Due Amount </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="country_birth" id="invoicedDueAmount">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="passport_no" class="control-label">Status </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="passport_no" id="offer_status">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="passport_expiry" class="control-label">Total Fee Paid </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="passport_expiry" id="totalFeePaid">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="visa_type" class="control-label">Course Miscellaneous Fee Due </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="visa_type" id="courseMiscellaneousFeeDue">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-sm-3">
                                                    <label class="control-label">Total Miscellaneous Fee Due </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="totalMiscellaneousFeeDue">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="course_service_fee" class="control-label">Course Service Fee Due </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="course_service_fee_value" id="courseServiceFeeDue">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="total_service_fee" class="control-label">Total Service Fee Due </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" for="total_service_fee_value" id="totalServiceFeeDue">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3 padding-t-7">
                                                    <button data-dismiss="modal" class="btn btn-info" type="button" id="flip">Show Detail</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="box box-info" id="panel" style="display: none;">
                                        <div class="custom-header">
                                            <h3 class="box-title">Student Subject Enrollment Payment Information</h3>
                                        </div>       
                                        <div class="box-body">
                                            <div class="row margin-b-30">
                                                <div class="form-group col-sm-3">
                                                    <label for="application_id" class="control-label">Student Course Fee </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="studentCourseFee">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="nickname" class="control-label">Total Schedule Amount </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="scheduleAmount">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="student_type" class="control-label">Total Paid Amount </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="totalPaidAmount">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-3">
                                                    <label for="nationality" class="control-label">No. of Subject(s) Enrolled </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="noOfSubjectEnrolled">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-sm-3">
                                                    <label for="passport_no" class="control-label">Total Enrolled Subject(s) Cost</label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="totalEnrollCost">-</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-sm-6">
                                                    <label for="passport_expiry" class="control-label">Status </label>
                                                    <div class="info-dis">
                                                        <span class="info-text" id="divStatus">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="custom-header">
                                        <h3 class="box-title">Payment Schedule For Student : {{ $objStudentsLists[0]->generated_stud_id}} and Course : <span class="courseHeader"></span></h3>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover table-custom" id="subjectData">
                                            <thead>
                                                <tr>
                                                    <th scope="col">Invoice <br/>Id</th>
                                                    <th scope="col">Invoice <br/>No</th>
                                                    <th scope="col">Inv Start <br/>Date</th>
                                                    <th scope="col">Due <br/>Date</th>
                                                    <th scope="col">Invoice Type</th>
                                                    <th scope="col">Agent <br/>Name</th>
                                                    <th scope="col">Fees</th>
                                                    <th scope="col">Fee <br/>Paid</th>
                                                    <th scope="col">Accrued <br/>Fee</th>
                                                    <th scope="col">Paid <br/>Duration</th>
                                                    <th scope="col">Comm% <br/>+ GST</th>
                                                    <th scope="col">Commission</th>
                                                    <th scope="col">GST</th>
                                                    <th scope="col">Invoice <br/>Sent</th>
                                                    <th scope="col">Invoice <br/>Credit</th>
                                                    <th scope="col">Remarks</th>
                                                </tr>
                                            </thead>
                                            <tbody id="dataAppend">
                                                <tr>
                                                    <td colspan="16" style="text-align: center">
                                                        <p style="color:red;">No Record Found</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="custom-header">
                                        <h3 class="box-title">Paid Payment List For Student : {{ $objStudentsLists[0]->generated_stud_id}} and Course : <span class="courseHeader"></span></h3>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover table-custom" id="paidPayment">
                                            <thead>
                                                <tr>
                                                    <th scope="col"></th>
                                                    <th scope="col">Tran <br/>No</th>
                                                    <th scope="col">Invld</th>
                                                    <th scope="col">Receipt <br/>No</th>
                                                    <th scope="col">Payment <br/>Date</th>
                                                    <th scope="col">Amount <br/>Paid</th>
                                                    <th scope="col">Amount <br/>Deposited</th>
                                                    <th scope="col">Payment <br/>Mode</th>
                                                    <th scope="col">Ref. <br/>No</th>
                                                    <th scope="col">Bank Dep. <br/>Date</th>
                                                    <th scope="col">Remarks</th>
                                                    <th scope="col">Scholarship</th>
                                                    <th scope="col">Amount <br/>Refunded</th>
                                                    <th scope="col">Agent <br/>Bonus</th>
                                                    <th scope="col">Bonus <br/>GST</th>
                                                    <th scope="col">Bonus Paid <br/>Date</th>
                                                    <th scope="col">Bad <br/>Debt</th>
                                                    <th scope="col">Reversed</th>
                                                    <th scope="col">Receipt <br/>Sent</th>
                                                </tr>
                                            </thead>
                                            <tbody id="paidPaymentAmount">
                                                <tr>
                                                    <td colspan="19" style="text-align: center">
                                                        <p style="color:red;">No Record Found</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <div class="modal fade" id="editModel" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post','id'=>'student_initial_payment1')) }}
                {{ Form::hidden('edit_id', null , array('class' => 'form-control', 'id' => 'edit_id')) }}
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Modify Payment Schedule</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Agent Name :</label>
                                        <div class="col-sm-6">
                                            {{ Form::select('agent_id', $arrgetAgentList ,(empty($subject->course_type) ? null : $subject->course_type), array('class' => 'form-control', 'id' => 'agent_id')) }}
                                        </div>
                                    </div>
                                    <div class="form-group"><label class="col-sm-4 control-label">Invoice Start Date : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-6">
                                            {{ Form::hidden('payment_status', null , array('class' => 'form-control', 'id' => 'payment_status')) }}
                                            {{ Form::text('invoiced_start_date', null , array('class' => 'form-control dateField', 'id' => 'invoiced_start_date')) }}
                                        </div></div>
                                    <div class="form-group"><label class="col-sm-4 control-label">Due Date : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-6">
                                            {{ Form::text('invoiced_due_date', null , array('class' => 'form-control dateField', 'id' => 'invoiced_due_date')) }}
                                        </div></div>
                                    <div class="form-group"><label class="col-sm-4 control-label">Agent Commission :</label>
                                        <div class="col-sm-6">
                                            {{ Form::text('commission_value', null , array('class' => 'form-control', 'id' => 'commission_value')) }}
                                        </div></div>
                                    <div class="form-group"><label class="col-sm-4 control-label">GST :</label>
                                        <div class="col-sm-6">
                                            <div class="display-ib" style="">
                                                {{ Form::radio('GST', 'GST', true, array('class' => 'gst_radio type_of_enrollment', 'id'=> 'GST')) }}
                                                <label for="GST">GST</label>
                                                &nbsp;&nbsp;&nbsp;
                                                {{ Form::radio('GST', 'NO GST', NULL, array('class' => 'gst_radio courseSelect', 'id'=> 'NoGST')) }}
                                                <label for="NoGST">No GST</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group"><label class="col-sm-4 control-label">Amount :</label>
                                        <div class="col-sm-6">
                                            {{ Form::text('upfront_fee_to_pay', null , array('class' => 'form-control', 'id' => 'upfront_fee_to_pay')) }}
                                        </div></div>
                                    <div class="form-group"><label class="col-sm-4 control-label">Paid Duration :</label>
                                        <div class="col-sm-3">
                                            {{ Form::text('paid_duration_text', null, array('class' => 'form-control', 'id' => 'paid_duration_text')) }}
                                        </div>
                                        <div class="col-sm-3">
                                            {{ Form::select('paid_duration_day', $arrPaidDuration, null , array('class' => 'form-control', 'id' => 'paid_duration_day')) }}
                                        </div></div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Remarks :</label>
                                        <div class="col-sm-6">
                                            {{ Form::textarea('remarks', null, array('class' => 'form-control', 'id' => 'remarks','placeholder'=>'Enter remarks')) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <input name="Add" class="btn btn-info" value="Edit" type="submit">
                </div>
                {{ Form::close() }}
            </div>
        </div>
    </div>

    <div class="modal fade" id="modifyTransaction" role="dialog">
        <div class="modal-dialog" style="width: 800px !important">
            <div class="modal-content">
                {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post', 'id'=>'student_initial_payment')) }}
                {{ Form::hidden('edit_id', null , array('class' => 'form-control', 'id' => 'mod_edit_id')) }}
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Modify Transaction</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Receipt Number :</label>
                                        <div class="col-sm-5">
                                            {{ Form::select('receipt_no_mod', array(""=>"--Select Receipt Number--") ,(empty($subject->course_type) ? null : $subject->course_type), array('class' => 'form-control', 'id' => 'receipt_no_mod')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Flag Info :</label>
                                        <div class="col-sm-5">
                                            {{ Form::select('agent_id', array(""=>"Tution Fee Paid") ,(empty($subject->course_type) ? null : $subject->course_type), array('class' => 'form-control', 'id' => 'agent_id', 'disabled')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Transaction Number :</label>
                                        <label class="col-sm-5" id="transactoionNumberMod">-</label>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Payment Date :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('payment_date_mod', null , array('class' => 'form-control', 'id' => 'payment_date_mod')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Total Schedule Fee :</label>
                                        <label class="col-sm-5" id="scheduleFeeMod">-</label>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Transaction Amount :</label>
                                        <label class="col-sm-5" id="transactionAmountMod">-</label>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Bank Deposite Date :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('bank_deposite_date_mod', null , array('class' => 'form-control', 'id' => 'bank_deposite_date_mod')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Agent Bonus :</label>
                                        <div class="col-sm-2">
                                            {{ Form::hidden('editTransaction', '1') }}
                                            {{ Form::text('agent_bonus_mod', null , array('class' => 'form-control', 'id' => 'agent_bonus_mod')) }}
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="display-ib">
                                                {{ Form::radio('GST_mod', 'GST', true, array('class' => 'type_of_enrollment', 'placeholder' => '', 'id'=> 'GST_Mod')) }}<label class='margin-r-15' for="GST_Mod">GST</label>
                                                {{ Form::radio('GST_mod', 'NO GST', NULL, array('class' => 'courseSelect', 'id'=> 'NoGST_Mod')) }}<label class='margin-r-15' for="NoGST_Mod">No GST</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Payment Mode :</label>
                                        <div class="col-sm-5">
                                            {{ Form::select('payment_mode_mod', $arrPaidMode ,(empty($subject->course_type) ? null : $subject->course_type), array('class' => 'form-control', 'id' => 'payment_mode_mod')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Bonus Paid Date :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('bonus_paid_date_mod', null , array('class' => 'form-control', 'id' => 'bonus_paid_date_mod')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">&nbsp;</label>
                                        <div class="col-sm-8">
                                            {{ Form::checkbox('afrterAgentBonus', 1,true, array('class' => 'pull-left', 'id' => 'after_agent_bonus')) }}
                                            <label class="label-value-view" for="after_agent_bonus"> Calculate commission after agent bonus </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Bad Debt :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('bad_debt_mod', null , array('class' => 'form-control', 'id' => 'bad_debt_mod')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Receipt Sent On :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('receipt_sent_date', null , array('class' => 'form-control', 'id' => 'receipt_sent_date')) }}
                                            {{ Form::hidden('transactionAmountModHid', null , array('id' => 'transactionAmountModHid')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Bank Deposited :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('bank_deposited_mod', null , array('class' => 'form-control', 'id' => 'bank_deposited_mod')) }}
                                        </div>
                                        <div class="col-sm-3">
                                            {{ Form::checkbox('receiptSent', 1, true,array('class' => 'pull-left', 'id' => 'receiptSent_mod')) }} &nbsp;
                                            <label class="label-value-view" for="receiptSent_mod"> Receipt Sent </label>
                                            <!--<label for="receiptSent_mod" class="col-sm-10 control-label">Receipt Sent</label>-->
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Remarks :</label>
                                        <div class="col-sm-5">
                                            {{ Form::textarea('remarks_mod', null , array('class' => 'form-control', 'id' => 'remarks_mod','rows'=>'3')) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <input name="Add" class="btn btn-info" value="Edit" type="submit">
                </div>
                {{ Form::close() }}
            </div><!-- /.modal-content -->

        </div>
    </div>

    <div class="modal fade" id="generateSchedule" role="dialog">
        {{ Form::open( array('class' => 'form-horizontal vertical-add-form','route' => array('generate-new-schedule', $student_id),'method' => 'post','id'=>'generateNewSchedule')) }}
        {{ Form::hidden('course_id', null , array('class' => 'form-control', 'id' => 'course_id_schedule')) }}
        {{ Form::hidden('student_id', $student_id , array('class' => 'form-control', 'id' => 'student_id_schedule')) }}
        <div class="modal-dialog width-max">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Generate New Schedule</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Invoice Number :</label>
                                        <div class="col-sm-5">
                                            <label class="label-value-view" >{{ $arrInvoiceNumber[0]['invoice_number']}}</label>
                                            {{ Form::hidden('invoice_number', $arrInvoiceNumber[0]['invoice_number'] , array('class' => 'form-control', 'id' => 'invoice_number')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Invoice Start Date :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('invoiced_start_date', null , array('class' => 'form-control', 'id' => 'invoiced_start_date_schedule','placeholder'=>'Enter Invoice Start Date')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Due Date :</label>
                                        <div class="col-sm-5">
                                            {{ Form::text('due_date', null , array('class' => 'form-control', 'id' => 'invoiced_due_date_schedule' ,'placeholder'=>'Enter Invoice End Date')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Suggested Agent Commission :</label>
                                        <div class="col-sm-5">
                                            {{ Form::select('suggest_agent_commission', array(""=>"--Select Agent Commission--") ,null, array('class' => 'form-control', 'id' => 'suggest_agent_commission')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Applied Commission :
                                            <span id="" class="required-field">*<div></div></span>
                                        </label>
                                        <div class="col-sm-5">
                                            {{ Form::text('commission_value', null , array('class' => 'form-control', 'placeholder' => 'Enter Commission Value', 'id' => 'commission_value_schedule')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">GST:</label>
                                        <div class="col-sm-8">
                                            <div class="display-ib" style="margin-left:10px;">
                                                {{ Form::radio('GST', 'GST', true, array('class' => 'type_of_enrollment', 'id'=> 'GST_Schedule')) }}<label  for="GST_Schedule">GST</label>
                                                &nbsp;&nbsp;&nbsp;
                                                {{ Form::radio('GST', 'NO GST', NULL, array('class' => 'courseSelect', 'id'=> 'NoGST_Schedule')) }}<label  for="NoGST_Schedule">No GST</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Amount :
                                            <span id="" class="required-field">*<div></div></span>
                                        </label>
                                        <div class="col-sm-5">
                                            {{ Form::text('upfront_fee_to_pay', null , array('class' => 'form-control', 'placeholder' => 'Enter Amount', 'id' => 'upfront_fee_to_pay')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Paid Duration : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-3">
                                            {{ Form::text('paid_duration_text', null , array('class' => 'form-control', 'placeholder' => 'Enter Duration', 'id' => 'paid_duration_text')) }}
                                        </div>
                                        <div class="col-sm-2">
                                            {{ Form::select('paid_duration_day', $arrPaidDuration,2 , array('class' => 'form-control', 'id' => 'paid_duration_day')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">Remarks : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-8">
                                            {{ Form::textarea('remarks', null , array('class' => 'form-control', 'id' => 'remarks' , 'placeholder' => 'Enter Remarks')) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <input name="create" class="btn btn-info" value="Create" type="submit">
                </div>
            </div><!-- /.modal-content -->
        </div>
        {{ Form::close() }}
    </div>

    <!--view schedule info model-->
    <div class="modal fade" id="viewScheduleInfo" role="dialog">
        <div class="modal-dialog payment-summery">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Student Payment Info</h4>
                </div>
                <div class="modal-body display-ib" style="width: 100%;">

                    <div class="table-responsive no-padding">
                        <table class="table table-hover table-custom" id="paymentInfoData">
                            <thead>
                                <tr>
                                    <th> Transaction <br/>No. </th>
                                    <th> Receipt<br/>&nbsp; </th>
                                    <th> Payment <br/>Date </th>
                                    <th> Paid <br/>Amount </th>
                                    <th> Deposite <br/>Amount </th>
                                    <th> Pay <br/>Mode </th>
                                    <th> Cheque <br/>No. </th>
                                    <th> Remarks <br/>&nbsp; </th>
                                    <th> Bank Dept. <br/>Date </th>
                                    <th> Refund <br/>Amount </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="10" style="text-align: center">
                                        <p style="color:red;">No Record Found</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="studentEmailSend" role="dialog">
        <div class="modal-dialog payment-summery">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h3 class="box-title">Send Email</h3>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        {{ Form::model(array('method' => 'post'), ['class' => 'form-horizontal vertical-add-form', 'route' => array('student-profile-send-mail', $student_id), 'files' => 'true', 'id' => 'studentInfoEmail']) }}

                        {{ Form::hidden('email_to', $applicationRefID->email, array('class' => 'form-control','id'=>'email_to')) }}
                        {{ Form::hidden('agent_id', $agent_id, array('class' => 'form-control','id'=>'agent_id_email')) }}
                        {{ Form::hidden('type',  "Payment", array('class' => 'form-control','id'=>'type')) }}
                        {{ Form::hidden('page_type',  "stdent-payment-summary", array('class' => 'form-control','id'=>'page_type')) }}
                        {{ Form::hidden('enrolled_course_id',  NULL, array('class' => 'enrolled_course_id', 'id' => 'enrolled_course_id')) }}
                        {{ Form::hidden('studentPaymentId',  NULL, array('class' => 'studentPaymentId', 'id' => 'studentPaymentId')) }}
                        {{ Form::hidden('studentInvoiceNo',  NULL, array('class' => 'studentInvoiceNo', 'id' => 'studentInvoiceNo')) }}

                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group no-margin">
                                        <label for="email_from" class="col-sm-4 control-label">From : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-5">
                                            <select class="form-control" name="email_from" id="email_from">
                                                @foreach($arrFromMail as $row)
                                                <option value="{{ $row['email'] }}">{{ $row['type'] .' : '. $row['email'] }}</option>
                                                @endforeach
                                                @if(!empty($personalEmail))
                                                <option value="{{ $personalEmail }}">{{ 'Personal Email : '. $personalEmail }}</option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group no-margin">
                                        <label for="student_id" class="col-sm-4 control-label">Student ID : </label>
                                        <div class="col-sm-5">
                                            <span class="label-value-view">
                                                {{ empty($applicationRefID->generated_stud_id) ? null : $applicationRefID->generated_stud_id }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="form-group no-margin">
                                        <label for="student_name" class="col-sm-4 control-label">Student Name : </label>
                                        <div class="col-sm-5">
                                            <span class="label-value-view">
                                                {{ empty($applicationRefID->fullName) ? null : $applicationRefID->fullName }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="form-group no-margin">
                                        <label for="email_from" class="col-sm-4 control-label">Personal email : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-5">
                                            <span class="label-value-view">
                                                {{ $applicationRefID->email }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_from" class="col-sm-4 control-label">Agent Email : </label>
                                        <div class="col-sm-5">
                                            {{ Form::select('email_bcc', array(), null, array('class' => 'form-control agent_email', 'id' => 'agent_email')) }}
                                            <!--{{ Form::select('email_bcc', array(''=>'- - Select Agent Email - -') + $objStdentAgentEmail  , null, array('class' => 'form-control', 'id' => 'agent_email')) }}-->
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_cc" class="col-sm-4 control-label"> CC : </label>
                                        <div class="col-sm-5">
                                            {{ Form::text('email_cc',  null, array('class' => 'form-control','id'=>'email_cc','placeholder'=>'Enter CC Email')) }}                
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_template" class="col-sm-4 control-label"> Select Template : </label>
                                        <div class="col-sm-5">
                                            {{ Form::select('email_template', $arrEmailTemplate, null, array('class' => 'form-control', 'id' => 'email_template')) }}
                                            <span class="loader"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_subject" class="col-sm-4 control-label"> Subject : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-5">
                                            {{ Form::text('email_subject',  null, array('class' => 'form-control', 'id'=>'email_subject', 'placeholder'=>'Enter Subject')) }}                
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <!--<label for="editor1" class="col-sm-3 control-label"><span id="" class="required-field">*<div></div></span></label>-->
                                        <div class="col-sm-12">
                                            {{ Form::textarea('email_content', null , array('class' => 'form-control', 'rows' => 3, 'id'=>'editor1')) }}                
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_attachment_2" class="col-sm-4 control-label">File Attachment:</label>
                                        <div class="col-sm-5">
                                            {{ Form::file('email_attachment_1', ['class' => 'inputfile', 'id' => 'file-1']) }}
                                            <!--{{ Form::file('email_attachment_1', NULL, array('class' => 'inputfile', 'data-multiple-caption'=>'{count} files selected')) }}-->
                                            <label for="file-1"><i class="fa fa-upload"></i><span class='file-name'> Choose a File&hellip;</span></label>
                                        </div>
                                    </div>
                                    <div class="form-group no-margin">
                                        <label class="col-sm-4">&nbsp;</label>
                                        <div class="col-sm-6">
                                            {{ Form::checkbox('student_invoice', 1, false, array('class' => 'student_invoice','id' => 'student_invoices')) }} &nbsp;
                                            <label for="student_invoices" class="text-green">Attach Student Schedule Invoice</label>
                                        </div>
                                    </div>
                                    <div class="form-group no-margin">
                                        <label class="col-sm-4">&nbsp;</label>
                                        <div class="col-sm-6">
                                            {{ Form::checkbox('offer_comm_log', 1, true, array('class' => 'offer_comm_log','id' => 'offer_comm_logs')) }} &nbsp;
                                            <label for="offer_comm_logs" class="text-green">Add To Student Communication Log</label>
                                        </div>
                                    </div>
                                    <div class="form-group no-margin">
                                        <label class="col-sm-4">&nbsp;</label>
                                        <div class="col-sm-5">
                                            {{ Form::checkbox('invoice_sent', 1, true, array('class' => 'invoice_sent','id' => 'invoice_sents')) }} &nbsp;
                                            <label for="invoice_sents" class="text-green">Agent Invoice Sent</label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label class="col-sm-4"> &nbsp; </label>
                                        <div class="col-sm-4">
                                            <button type="submit" class="btn disable btn-info send-mail">Send</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ Form::close() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="studentReceiptEmailSend" role="dialog">
        <div class="modal-dialog payment-summery">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Send Email</h4>
                </div>
                <div class="modal-body display-ib" style="width: 100%;">
                    <div class="box box-info">
                        {{ Form::model(array('method' => 'post'), ['class' => 'form-horizontal vertical-add-form', 'route' => array('student-profile-send-mail-receipt', $student_id), 'files' => 'true', 'id' => 'studentInfoEmailReceipt']) }}

                        {{ Form::hidden('email_to', $applicationRefID->email, array('class' => 'form-control','id'=>'email_to')) }}
                        {{ Form::hidden('agent_id', $agent_id, array('class' => 'form-control','id'=>'agent_id_email')) }}
                        {{ Form::hidden('type',  "Payment", array('class' => 'form-control','id'=>'type')) }}
                        {{ Form::hidden('page_type',  "stdent-payment-summary", array('class' => 'form-control','id'=>'page_type')) }}
                        {{ Form::hidden('enrolled_course_id',  NULL, array('class' => 'enrolled_course_id', 'id' => 'enrolled_course_id_receipt')) }}
                        {{ Form::hidden('enrolled_student_id',  NULL, array('class' => 'enrolled_student_id', 'id' => 'enrolled_student_id')) }}
                        {{ Form::hidden('studentPaymentId',  NULL, array('class' => 'studentPaymentId', 'id' => 'studentPaymentId_receipt')) }}
                        {{ Form::hidden('studentInvoiceNo',  NULL, array('class' => 'studentInvoiceNo', 'id' => 'studentInvoiceNo_receipt')) }}

                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="email_from" class="col-sm-4 control-label">From : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-5">
                                            <select class="form-control" name="email_from" id="email_from">
                                                @foreach($arrFromMail as $row)
                                                <option value="{{ $row['email'] }}">{{ $row['type'] .' : '. $row['email'] }}</option>
                                                @endforeach
                                                @if(!empty($personalEmail))
                                                <option value="{{ $personalEmail }}">{{ 'Personal Email : '. $personalEmail }}</option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="student_id" class="col-sm-4 control-label">Student ID : </label>
                                        <div class="col-sm-5">
                                            <span class="label-value-view">{{ empty($applicationRefID->generated_stud_id) ? null : $applicationRefID->generated_stud_id }}</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="student_name" class="col-sm-4 control-label">Student Name : </label>
                                        <div class="col-sm-5">
                                            <span class="label-value-view">{{ empty($applicationRefID->fullName) ? null : $applicationRefID->fullName }}</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_from" class="col-sm-4 control-label">Personal email address : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-5">
                                            <span class="label-value-view">{{ $applicationRefID->email }}</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_from" class="col-sm-4 control-label">Agent Email : </label>
                                        <div class="col-sm-5">
                                            {{ Form::select('email_bcc', array(''=>'- - Select Agent Email - -'), null, array('class' => 'form-control agent_email', 'id' => 'agent_email')) }}
                                            <!--{{ Form::select('email_bcc', array(''=>'- - Select Agent Email - -') + $objStdentAgentEmail  , null, array('class' => 'form-control', 'id' => 'agent_email')) }}-->
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_cc" class="col-sm-4 control-label"> CC : </label>
                                        <div class="col-sm-5">
                                            {{ Form::text('email_cc',  null, array('class' => 'form-control','id'=>'email_cc','placeholder'=>'Enter CC Email')) }}                
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_template" class="col-sm-4 control-label"> Select Template : </label>
                                        <div class="col-sm-5">
                                            {{ Form::select('email_template', $arrEmailTemplate, null, array('class' => 'form-control', 'id' => 'email_template_receipt')) }}
                                            <span class="loader"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="email_subject" class="col-sm-4 control-label"> Subject : <span id="" class="required-field">*<div></div></span></label>
                                        <div class="col-sm-5">
                                            {{ Form::text('email_subject',  null, array('class' => 'form-control', 'id'=>'email_subject_receipt', 'placeholder'=>'Enter Subject')) }}                
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <!--<label for="editor1" class="col-sm-3 control-label"><span id="" class="required-field">*<div></div></span></label>-->
                                        <div class="col-sm-12">
                                            {{ Form::textarea('email_content', null , array('class' => 'form-control', 'rows' => 3, 'id'=>'editor1_receipt')) }}                
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label for="email_attachment_2" class="col-sm-4 control-label">File Attachment:</label>
                                        <div class="col-sm-5">
                                            {{ Form::file('email_attachment_1', ['class' => 'inputfile', 'id' => 'file-3']) }}
                                            <label for="file-3"><i class="fa fa-upload"></i><span class='file-name'> Choose a File&hellip;</span></label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label class="col-sm-4">&nbsp;</label>
                                        <div class="col-sm-6">
                                            {{ Form::checkbox('student_invoice', 1, false, array('class' => 'student_invoice','id' => 'student_invoice')) }} &nbsp;
                                            <label class="text-green" for="student_invoice">Attach Student Schedule Invoice</label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label class="col-sm-4">&nbsp;</label>
                                        <div class="col-sm-6">
                                            {{ Form::checkbox('offer_comm_log', 1, true, array('class' => 'offer_comm_log','id' => 'offer_comm_log')) }} &nbsp;
                                            <label class="text-green" for="offer_comm_log">Add To Student Communication Log</label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label class="col-sm-4">&nbsp;</label>
                                        <div class="col-sm-5">
                                            {{ Form::checkbox('invoice_sent', 1, true, array('class' => 'invoice_sent','id' => 'invoice_sent')) }} &nbsp;
                                            <label class="text-green" for="invoice_sent">Receipt sent</label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label class="col-sm-4"> &nbsp; </label>
                                        <div class="col-sm-4">
                                            <button type="submit" class="btn disable btn-info send-mail">Send</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ Form::close() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="invoiceCredit" role="dialog">
        {{ Form::open(array('id' => 'invoice_credit','class' => 'form-horizontal vertical-add-form')) }}
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Invoice Credit</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label id="invalid_amount_message" class="col-md-12 green-font"></label>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label"> Schedule Amount :</label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="schedule_amount_value" >0</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label" for="paid_amount" > Paid Amount :</label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="paid_amount_value" >0</span>
                                            {{ Form::hidden('hidden_student_id',null, array('id' => 'hidden_student_id')) }}
                                            {{ Form::hidden('hidden_course_id',null, array('id' => 'hidden_course_id')) }}
                                            {{ Form::hidden('hidden_initial_payment_detail_id',null, array('id' => 'hidden_initial_payment_detail_id')) }}
                                            {{ Form::hidden('hidden_credit_amount',null, array('id' => 'hidden_credit_amount')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label" for="credit_amount" > Credit Amount : <span class="required-field">*</span></label>
                                        <div class="col-md-5">
                                            {{ Form::text('credit_amount',null, array('class' => 'form-control', 'id' => 'credit_amount' , 'placeholder'=>'Enter Amount')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label" for="credit_date" > Credit Date : <span class="required-field">*</span></label>
                                        <div class="col-md-5">
                                            {{ Form::text('credit_date',date('d-m-Y'), array('class' => 'form-control', 'id' => 'credit_date' , 'placeholder' => 'dd-mm-yyyy')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label" for="remarks" > Remarks : </label>
                                        <div class="col-md-5">
                                            {{ Form::textarea('remarks_invoice', null , array('class' => 'form-control ', 'id' => 'remarks_invoice' , 'rows' => '2')) }}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label" for="Transaction Processing Type" > Transaction Processing Type : </label>
                                        <div class="col-md-5">
                                            <div class="display-ib" id="transaction_processing" >

                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4" > &nbsp; </label>
                                        <div class="col-md-5">
                                            <input name="Add" class="btn btn-info" id="submit_invoice_credit" value="Add Invoice Credit" type="submit">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="table-responsive no-padding">
                                        <table class="table table-hover table-custom" id="invoiceCreditInfoData">
                                            <thead>
                                                <tr>
                                                    <th>Credited Date</th>
                                                    <th>Credited Amount</th>
                                                    <th>Remarks</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="3" style="text-align: center">
                                                        <p style="color:red;">No Record Found</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">                    
                </div>
            </div>
        </div>
        {{ Form::close() }}
    </div>

    <div class="modal fade" id="reverseTransaction" role="dialog">
        <div class="modal-dialog" style="width: 800px">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Reverse Transaction</h4>
                </div>
                <div class="modal-body">
<!--                    <div class="callout callout-info">
                        <p> <i class="icon fa fa-info" style="margin-right: 25px"></i>Please be noted that you may not have access to make this transaction back to normal</p>
                    </div>-->
                    <div class="box box-info">
                        <div class="box-body form-horizontal vertical-add-form">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="reverse_comment">Reverse Comment : <span id="" class="required-field">*<div></div></span></label>
                                    <div class="col-sm-5">
                                        {{ Form::hidden('reverse_transaction_id', null , array('id' => 'reverse_transaction_id')) }}
                                        {{ Form::textarea('reverse_comment', null , array('class' => 'form-contro', 'id' => 'reverse_comment')) }}
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label">&nbsp;</label>
                                    <div class="col-sm-3">
                                        <input name="Add" class="btn btn-info reverce-now-click" value="Reverse Now" id="reverceNowButton" type="button">
                                    </div>
                                </div>
                            </div>  
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="feeRefundModal" role="dialog">
        <div class="modal-dialog">
            {{ Form::open(array('id'=>'save_fee_refund_form', 'class'=>'form-horizontal vertical-add-form')) }}
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Fee Refund Detail</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="col-sm-12">
                                <span class="red-font" id="invalid_refund_amount_message"></span>
                                <div class="form-group">
                                    <span class="green-font">Student Refund Details</span>
                                </div>
                                <div class="form-group no-margin">
                                    <label class="control-label col-md-6">Transaction Number : </label>
                                    <div class='col-md-5 input-group'>
                                        <span class="label-value-view" id="transaction_no"></span>
                                    </div>
                                </div>
                                <div class="form-group no-margin">
                                    <label class="control-label col-md-6">Student Paid Amount : </label>
                                    <div class='col-md-5 input-group'>
                                        <span class="label-value-view"> AUD $<strong class="label-value-view" id="student_paid_amount"></strong></span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-6">Student refund Amount : <span id="" class="required-field">*<div></div></span></label>
                                    <div class='col-md-4 input-group'>
                                        {{ Form::text('student_refund_amount' , null , array('class' => 'form-control numberValid', 'id' => 'student_refund_amount')) }}
                                        {{ Form::hidden('hidden_agent_bonus' , null , array('id' => 'hidden_agent_bonus')) }}
                                        {{ Form::hidden('hidden_bonus_gst' , null , array('id' => 'hidden_bonus_gst')) }}
                                        {{ Form::hidden('hidden_transaction_id' , null , array('id' => 'hidden_transaction_id')) }}
                                        {{ Form::hidden('edit_id', null , array('class' => 'form-control editId', 'id' => 'editId')) }}
                                        {{ Form::hidden('agent_id', null , array('class' => 'form-control agent_id', 'id' => 'agent_id')) }}
                                        {{ Form::hidden('invoiced_start_date', null , array('class' => 'form-control invoiced_start_date', 'id' => 'invoiced_start_date')) }}
                                        {{ Form::hidden('invoiced_due_date', null , array('class' => 'form-control invoiced_due_date', 'id' => 'invoiced_due_date')) }}
                                        {{ Form::hidden('upfront_fee_to_pay', null , array('class' => 'form-control upfront_fee_to_pay', 'id' => 'upfront_fee_to_pay')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="administration_cost" class="col-md-6 control-label">Administration Cost : <span id="" class="required-field">*<div></div></span></label>
                                    <div class='col-md-4 input-group'>
                                        {{ Form::text('administration_cost' , null , array('class' => 'form-control numberValid', 'id' => 'administration_cost')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="green-font">Agent Refund Details</span>
                                </div>
                                <div class="form-group">
                                    <label for="agent_commission_refund" class="col-md-6 control-label">Agent Commission refund amount : <span id="" class="required-field">*<div></div></span></label>
                                    <div class='col-md-4 input-group'>
                                        {{ Form::text('agent_commission_refund' , null , array('class' => 'form-control numberValid', 'id' => 'agent_commission_refund')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-6 control-label">GST refund Amount : </label>
                                    <div class='col-md-5 input-group'>
                                        <span class="label-value-view">$ <strong id="gst_amount">0.00</strong></span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="agent_refunded" class="col-md-6 control-label">Agent Refunded : <span id="" class="required-field">*<div></div></span></label>
                                    <div class='col-md-4 input-group'>
                                        {{ Form::text('agent_refunded' , null , array('class' => 'form-control numberValid', 'id' => 'agent_refunded')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="remarks" class="col-md-6 control-label">Remarks : <span id="" class="required-field">*<div></div></span></label>
                                    <div class='col-md-6 input-group'>
                                        {{ Form::textarea('remarks' ,null , array('class' => 'form-control', 'id' => 'remarks','rows'=>'5')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="student_net_received" class="col-md-6 control-label">Student Net Received : <span id="" class="required-field">*<div></div></span></label>
                                    <div class='col-md-4 input-group'>
                                        {{ Form::text('student_net_received' ,null , array('class' => 'form-control numberValid', 'id' => 'student_net_received')) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-success" type="submit" id="refundSaveData">Refund</button>
                    <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                </div>
            </div>
            {{ Form::close() }}
        </div>
    </div>

    <div class="modal fade" id="deleteModal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Delete Record</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <p> You want to delete record. Are you sure?</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <button class="btn btn-success yes-sure" type="button">Yes</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="deleteScheduleModal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Delete Record</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <p> You want to delete record. Are you sure?</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <button class="btn btn-success confirm-delete-schedule" type="button">Yes</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="deleteTransactionModal" role="dialog">
        <div class="modal-dialog">
            {{ Form::open( array('class' => 'form-horizontal vertical-add-form', 'method' => 'post', 'id' => 'deleteTransactionInfo')) }}
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title"> Delete Confirmation</h4>
                </div>
                <div class="modal-body box-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">Transaction No : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_transaction_no"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">Receipt No : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_receipt_no"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">Payment Date : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_payment_date"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">payment Mode : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_payment_mode"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">Total Paid Amount : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_student_paid_amount"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">Refund Amount : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_amount_refund"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label">Remarks : </label>
                                        <div class="col-md-5">
                                            <span class="label-value-view" id="delete_remarks"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="table-responsive no-padding">
                                        <table class="table table-hover table-custom" id="deleteDiscriptionTable">
                                            <thead>
                                                <tr>
                                                    <th>Description</th>
                                                    <th>Amount(dr.)</th>
                                                    <th>Amount(cr.)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="3" style="text-align: center">
                                                        <p style="color:red;">No Record Found</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label">Commission Payable (GST Inc:) : </label>
                                        <div class="col-md-4">
                                            <span class="label-value-view" id="delete_agent_comm_pay"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-6 control-label">Commission Paid (GST Inc:) : </label>
                                        <div class="col-md-4">
                                            <span class="label-value-view" id="delete_agent_comm_paid"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-6 control-label">Payment Date : </label>
                                        <div class="col-md-4">
                                            <span class="label-value-view" id="delete_agent_pay_date"></span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-6 control-label">Payment Mode : </label>
                                        <div class="col-md-4">
                                            <span class="label-value-view" id="delete_agent_pay_mode"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-6 control-label"> Reason to Delete  : <span id="" class="required-field">*</span>  </label>
                                        <div class="col-md-6">
                                            {{ Form::text('reason_to_delete', '', array('class' => 'form-control', 'id' => 'reason_to_delete')) }}
                                            {{ Form::hidden('delete_transaction_id',null, array('id' => 'delete_transaction_id')) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input name="confirm_delete" class="btn btn-info confirm-delete-transaction" value="Confirm Delete" type="button">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                </div>
            </div>
            {{ Form::close() }}
        </div>
    </div>
</section>
</div>
@endsection