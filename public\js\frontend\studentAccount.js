var studentAccount = function () {

    var ajaxCall = function (url, postData, callBack) {
        $.ajax({
            type: "POST",
            url: url,
            headers: {'X-CSRF-TOKEN': $('input[name="_token"]').val()},
            data: postData,
            success: callBack
        });
    };
    $(function () {
        //CKEDITOR.replace('comment');
        if (typeof CKEDITOR != 'undefined') {
            CKEDITOR.replace('editor1').config.height = "400px";
        }
        if (typeof CKEDITOR != 'undefined') {
            CKEDITOR.replace('editor1_receipt').config.height = "400px";
        }

        //bootstrap WYSIHTML5 - text editor
        //$(".textarea").wysihtml5();            
    });

    var studentTransfer = function () {

        dateFormate('.dateField');

        $("body").on('change', '#course_id', function () {
            var courseId = $(this).val();
            var studentId = $('#student_id').val();

            $('#course_id2 option').show();
            if (courseId != '') {
                $('#course_id2').val('')
                $('#course_id2 option[value="' + courseId + '"]').hide();
            }

            $("#student_refund_href").attr('href', site_url + "student-refund-history/" + studentId + "/" + courseId);

            var dataArr = {'courseId': courseId, 'studentId': studentId};
            $('#arrPaidPaymentList tbody tr').remove();

            ajaxAction('student-account/ajaxAction', 'studentPaidPaymentList', dataArr, function (data) {
                var obj = jQuery.parseJSON(data);

                $('#transactionNoList').find('option').remove();
                $('#transactionNoList').append($('<option>', {
                    value: '', text: '- - Select Transaction No - -'
                }));

                $.each(obj.arrTransNo, function (i, item) {
                    $('#transactionNoList').append($('<option>', {
                        value: item.transaction_no, text: item.transaction_no
                    }));
                });
                $('#transactionNoList').selectric('refresh');

                if (obj.arrPayment.length > 0) {
                    $.each(obj.arrPayment, function (i, item) {
                        var table = '<tr id="">' +
                                '<td>' + item.transection_no + '</td>' +
                                '<td>' + item.receipt_no + '</td>' +
                                '<td>' + item.payment_date + '</td>' +
                                '<td>' + item.paid_amount + '</td>' +
                                '<td>' + item.deposited_amount + '</td>' +
                                '<td>' + item.mode_name + '</td>' +
                                '<td>' + item.cheque_no + '</td>' +
                                '<td>' + item.bank_deposit_date + '</td> ' +
                                '<td>' + item.remarks + '</td>' +
                                '<td>' + item.amount_refund + '</td>' +
                                '</tr>';
                        $("#arrPaidPaymentList tbody").append(table);
                        $('#noRecord').hide();
                    });
                } else {
                    $('#noRecord').show();
                }
            });


        });

        $("body").on('change', '#course_id2', function () {
            var courseId = $(this).val();
            var studentId = $('#student_id').val();

            var dataArr = {'courseId': courseId, 'studentId': studentId};
            $('#arrPaidPaymentList tbody tr').remove();

            ajaxAction('student-account/ajaxAction', 'studentPaidPaymentList', dataArr, function (data) {
                var obj = jQuery.parseJSON(data);

                $('#transactionNoList').find('option').remove();
                $('#transactionNoList').append($('<option>', {
                    value: '', text: '- - Select Transaction No - -'
                }));

                $.each(obj.arrTransNo, function (i, item) {
                    $('#transactionNoList').append($('<option>', {
                        value: item.transaction_no, text: item.transaction_no
                    }));
                });
                $('#transactionNoList').selectric('refresh');

                if (obj.arrPayment.length > 0) {
                    $.each(obj.arrPayment, function (i, item) {
                        var table = '<tr id="">' +
                                '<td>' + item.transection_no + '</td>' +
                                '<td>' + item.receipt_no + '</td>' +
                                '<td>' + item.payment_date + '</td>' +
                                '<td>' + item.paid_amount + '</td>' +
                                '<td>' + item.deposited_amount + '</td>' +
                                '<td>' + item.mode_name + '</td>' +
                                '<td>' + item.cheque_no + '</td>' +
                                '<td>' + item.bank_deposit_date + '</td> ' +
                                '<td>' + item.remarks + '</td>' +
                                '<td>' + item.amount_refund + '</td>' +
                                '</tr>';
                        $("#arrPaidPaymentList tbody").append(table);
                        $('#noRecord').hide();
                    });
                } else {
                    $('#noRecord').show();
                }
            });


        });

        $("body").on('change', '#transactionNoList', function () {
            var id = $(this).val();

            $("#totalStudentPayAmount").html("-");
            $("#dateOfPayment").html("-");
            $("#transactionAmount").html("-");
            $("#availableAmountToTransfer").html("-");
            $("#commissionPayable").html("-");
            $("#commissionPaidAmount").html("-");

            if (typeof id != 'undefined' && id != '' && id != null) {
                ajaxAction('student-account/ajaxAction', 'getTransactionDetail', {'id': id}, function (data) {
                    var obj = jQuery.parseJSON(data);
                    //console.log(obj.paid_amount);

                    $("#dateOfPayment").html(obj.payment_date);
                    $("#transactionAmount").html(obj.paid_amount);
                    $("#availableAmountToTransfer").html(obj.paid_amount);
                    $("#commissionPayable").html(obj.paid_amount);
                    $("#commissionPaidAmount").html(obj.paid_amount);

                });
            }
        });
    };

    var studentFilterList = function () {
        $('.searchFunction').on('click', function () {
            var dropDownId = $('.fieldchange').val();
            var searchValue = $('#searchValue').val();
            var that = $(this);
            loadingStart(that);

            if (dropDownId.length == 0) {
                $('#searchValue').val('');
            }
            $.ajax({
                url: site_url + "student-account/ajaxAction",
                method: "POST",
                headers: {'X-CSRF-TOKEN': $('input[name="_token"]').val()},
                data: {'action': 'filterData', 'data': {'dropDownId': dropDownId, 'searchValue': searchValue}},
                success: function (data) {

                    loadingEnd(that);
                    var obj = jQuery.parseJSON(data);
                    $("#dataCount").empty()
                    $("#dataCount").append(obj.length)
                    $("#studentAccount").replaceWith("<tbody id='studentAccount'>" + "</tbody>");

                    $.each(obj, function (i, item) {

                        var table = '<tr id="studentAccount">' +
                                '<td>' + item.generated_stud_id +
                                '<div class="action-overlay">' +
                                '<ul class="icon-actions-set">' +
                                '<li><a href="student-payment-summary/' + item.id + '" class="link-black text-sm" data-toggle="tooltip" data-original-title="View Student Payment Summer"> <i class="fa fa-search "></i></a></li>' +
                                '</ul>' +
                                '</div>' +
                                '</td>' +
                                '<td>' + item.name_title + '</td>' +
                                '<td>' + item.first_name + '</td>' +
                                '<td>' + item.family_name + '</td>' +
                                '<td>' + item.nickname + '</td>' +
                                '<td>' + item.gender + '</td>' +
                                '<td>' + item.student_type + '</td>' +
                                '<td>' + item.DOB + '</td>' +
                                '</tr>';
                        $("#studentAccount").append(table);
                    });

                    var total = obj.length;
                    if (total == 0) {
                        $("#studentAccount").append('<tr id="studentAccount"><td colspan="9"><p style="color:red;text-align: center;">No Record Found</p></td></tr>');
                        $('#noRecords').show();
                    } else {
                        $('#noRecords').hide();
                    }
                }
            });
        });

        $('.fieldchange').on('change', function () {
            $('#searchValue').val("");
        });
        $("body").on("onkeydown", "#searchValue", function (event) {
            if (event.keyCode == 13) {
                $(".searchFunction").trigger("click");
                event.preventDefault();
                return false;
            }
        });
        var dataArr = {};
        var columnWidth = {"width": "10%", "targets": 0};
        var arrList = {
            'tableID': '#studentAccountTable',
            'ajaxURL': site_url + "student-account/ajaxAction",
            'ajaxAction': 'getFinanceStudentData',
            'postData': dataArr,
            'hideColumnList': [8, 9, 10, 11],
            'noSearchApply': [],
            'noSortingApply': [],
            'defaultSortColumn': 0,
            'defaultSortOrder': 'desc',
            'setColumnWidth': columnWidth
        };
        getDataTable(arrList);
    };

    var studentList = function () {

        deleteSingleData(site_url + 'service-payment-delete/');

        $('#payment_mode').on('change', function () {
            if (this.value != 17) {
                $('#receiptNo').show();
            } else {
                $('#receiptNo').hide();
            }
        });

        $('.receiptNoNew').on('change', function () {
            if (this.value == "") {
                $('#receiptNoHtml').empty();
                $('#receiptNoHtml').append('<input type="text" name="receipt_no" class="form-control" value="' + $('#resceiptNumber').val() + '">');
            }
        });

        $('#payment_mode_add').on('change', function () {
            if (this.value != 17) {
                $('#receiptNoAdd').show();
            } else {
                $('#receiptNoAdd').hide();
            }
        });

        $('.receiptNoNewAdd').on('change', function () {
            if (this.value == "") {
                $('#receiptNoHtmlAdd').empty();
                $('#receiptNoHtmlAdd').append('<input type="text" name="receipt_no_add" class="form-control" value="' + $('#resceiptNumber').val() + '">');
            }
        });

        $('.modifyPaymentServiceClass').on('click', function () {
            var paymentId = $(this).attr('id');
            $('#modifyPaymentService').modal('show');
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "student-account/ajaxAction",
                data: {'action': 'modifyPaymentService', 'data': {'id': paymentId}},
                success: function (data) {

                    var obj = jQuery.parseJSON(data);
                    //  console.log(obj)
                    $('#service_type').val(obj.payment_type);
                    $('#service_edit_id').val(paymentId);
                    $('#service_student_id').val(obj.student_id);
                    $('#remarks').val(obj.remarks);
                    $('#amount').val(obj.amount);
                    $('#invoice_no').val(obj.invoice_number);
                    $('#receiptNo').hide();
                    $('#reference_no_edit').val(obj.provider_referance);
                    if (obj.payment_status == "unpaid") {
                        $('#payment_mode').val(17);
                    }
                    if (obj.payment_status == "paid") {
                        $('#payment_mode').val(obj.payment_mode);
                        $('#receiptNo').show();
                        $('#receipt_no').val(obj.resceipt_number);
                    }

                    var serviceStartDate = obj.pay_date;
                    var serviceFormattedDate = new Date(serviceStartDate);

                    $("#service_start_date").datepicker({
                        showOn: 'both',
                        changeMonth: true,
                        changeYear: true,
                        dateFormat: 'dd-mm-yyyy'
                    }).datepicker('setDate', serviceFormattedDate);
                    var serviceDueDate = obj.due_date;
                    var serviceDueFormattedDate = new Date(serviceDueDate);
                    $("#invoiced_due_date").datepicker({
                        showOn: 'both',
                        changeMonth: true,
                        changeYear: true,
                        dateFormat: 'dd-mm-yyyy'
                    }).datepicker('setDate', serviceDueFormattedDate);
                },
                error: function (err) {

                }
            });
        });

        dateFormateToday('#service_start_date_add');
        dateFormateToday('#invoiced_due_date_add');

        $(document).on("keypress", "form", function (event) {
            if (event.keyCode == 13)
            {
                $(".searchFunction").trigger("click");
                return false;
            }
        });

        $("#student_services_payment").validate({
            rules: {
                payment_mode: {
                    required: true
                },
                invoice_no: {
                    required: true
                },
                payment_type: {
                    required: true
                },
                invoiced_due_date: {
                    required: true
                },
                service_start_date: {
                    required: true
                },
                amount: {
                    required: true, number: true, min: 1
                }
            },
            errorPlacement: function (error, element) {
            }
        });

        $("#student_services_add").validate({
            rules: {
                payment_mode_add: {
                    required: true
                },
                invoice_no_add: {
                    required: true
                },
                reference_no: {required: true},
                invoiced_due_date_add: {
                    required: true
                },
                service_start_date_add: {
                    required: true
                },
                amount_add: {
                    required: true, number: true, min: 1
                }
            },
            errorPlacement: function (error, element) {
            }
        });

    };

    var studentPayment = function () {

        setTimeout(function () {
            $('#course_id').trigger('change');
        }, 100);

        $('#course_id').trigger('change');
        dateFormate('.dateField');
        //checkDateRange('.dateField', '#invoiced_start_date', '#invoiced_due_date', 'Promotion To Date Must be after Promotion  From Start Date');

        $("#student_initial_payment1").validate({
            rules: {
                payment_mode_mod: {
                    required: true
                }
            },
            errorPlacement: function (error, element) {
            }
        });

        $("#student_initial_payment").validate({
            rules: {
                payment_mode_mod: {
                    required: true
                },
                course_id: {
                    required: true
                },
                commission_period: {
                    required: true
                },
                payment_type: {
                    required: true
                },
                invoiced_due_date: {
                    required: true
                },
                invoiced_start_date: {
                    required: true
                },
                applied_commission: {
                    required: true, number: true
                }
            },
            errorPlacement: function (error, element) {
            }
        });

        $("#studentInfoEmail").validate({
            'ignore': [],
            rules: {
                email_from: {required: true, email: true},
//                email_cc: {required: true, email: true},
                email_bcc: {required: true},
                //email_template: {required: true},
                email_subject: {required: true},
                //email_content: {required: true},
                email_content: {required: function (textarea) {
                        CKEDITOR.instances[textarea.id].updateElement(); // update textarea
                        var editorcontent = textarea.value.replace(/<[^>]*>/gi, ''); // strip tags
                        var chk = editorcontent.length === 0;
                        $('#cke_editor1').css('border', (chk) ? '1px solid red' : '1px solid #b6b6b6')
                        return chk;
                    }
                }
            },
            errorPlacement: function (error, element) {
            }
        });
        $("#studentInfoEmailReceipt").validate({
            'ignore': [],
            rules: {
                email_from: {required: true, email: true},
//                email_cc: {required: true, email: true},
//                email_bcc: {required: true},
//                email_template: {required: true},
                email_subject: {required: true},
                //email_content: {required: true},
                email_content: {required: function (textarea) {
                        CKEDITOR.instances[textarea.id].updateElement(); // update textarea
                        var editorcontent = textarea.value.replace(/<[^>]*>/gi, ''); // strip tags
                        var chk = editorcontent.length === 0;
                        $('#cke_editor1').css('border', (chk) ? '1px solid red' : '1px solid #b6b6b6')
                        return chk;
                    }
                }
            },
            errorPlacement: function (error, element) {
            }
        });

        $("#generateNewSchedule").validate({
            rules: {
                invoice_number: {
                    required: true
                },
                invoiced_start_date: {
                    required: true
                },
                due_date: {
                    required: true
                },
                commission_value: {
                    required: true,
                    number: true
                },
                upfront_fee_to_pay: {
                    required: true,
                    number: true
                },
                paid_duration_text: {
                    required: true,
                    number: true
                },
                paid_duration_day: {
                    required: true
                },
                remarks: {
                    required: true
                }
            },
            errorPlacement: function (error, element) {
            }
        });

        $('#course_id').on('change', function () {
            $(".courseHeader").empty()
            var student_id = $('#student_id').val();
            var course_id = this.value;

            var resultExplode = $("#course_id option:selected").text().split(':');
            $(".courseHeader").append(resultExplode[0])

            $("#student_refund_href").attr('href', site_url + "student-refund-history/" + student_id + "/" + course_id);

            var invoice_no = this.invoice_no;
            var resultArr = {'student_id': student_id, 'student_course_id': course_id};

            var that = $(this);
            loadingStart(that);
            //$('#subjectList tbody tr').remove();
            $('#hidden_course_start').val('');
            $('#hidden_invoiced_due_date').val('');

            $.ajax({
                url: site_url + "student-account/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'studentPayment', 'data': resultArr},
                success: function (data) {
                    var sessionPermissionData = $("#sessionPermissionData").val()
                    var allowToModifyScheduleInvoice = $("#allowToModifyScheduleInvoice").val()
                    loadingEnd(that);
                    var obj = jQuery.parseJSON(data);
                    $('#dataAppend').empty();
                    $('#paidPaymentAmount').empty();
                    $('#agent_name').empty();
                    $('#offer_status').empty();
                    $('#course_fee').empty();
                    $('#course_duration').empty();
                    $('#course_length').empty();
                    $('#studentCourseFee').empty();
                    $('#scheduleAmount').empty();
                    $('#totalPaidAmount').empty();
                    $('#noOfSubjectEnrolled').empty();
                    $('#totalEnrollCost').empty();
                    $('#divStatus').empty();


                    $('#agent_name').append(obj['studentDetail'].agency_name);
                    $('#agent_id').val(obj['studentDetail'].agent_id);
                    $('#offer_status').append(obj['studentDetail'].status);
                    $('#course_fee').append('$' + obj['studentDetail'].course_fee.toFixed(2));

                    $('#studentCourseFee').append('$' + obj['studentDetail'].course_fee.toFixed(2));
                    $('#scheduleAmount').append('$' + obj['studentPaymentDetail'].totalInvoicedDueAmount.toFixed(2));
                    $('#totalPaidAmount').append('$' + obj['studentPaymentDetail'].totalFeePaid.toFixed(2));
                    $('#noOfSubjectEnrolled').append(obj['countStudentSubjectEnrolment']);
                    $('#totalEnrollCost').append('$' + obj['countStudentSubjectEnrolment'].toFixed(2));
                    $('#divStatus').append("Remaining Subject enrolment amount: AU$" + obj['studentPaymentDetail'].totalInvoicedDueAmount);

                    $('#tution_fee').val(obj['studentDetail'].course_fee);
                    $('#course_length').append(obj['studentDetail'].total_weeks + " " + "Weeks");
                    var startDate = (obj['studentDetail'].start_date == '' ? 'NA' : reverseDateV2(obj['studentDetail'].start_date));
                    var endDate = (obj['studentDetail'].finish_date == '' || obj['studentDetail'].finish_date == null ? 'NA' : reverseDateV2(obj['studentDetail'].finish_date));
                    $('#course_duration').append(startDate + " To " + endDate);

                    $('#totalCourseFeeBalanceDue').html('$' + obj['studentPaymentDetail']['totalCourseFeeBalanceDue'].toFixed(2));
                    $('#invoicedDueAmount').html('$' + obj['studentPaymentDetail']['invoicedDueAmount'].toFixed(2));
                    $('#courseMiscellaneousFeeDue').html('$' + obj['studentPaymentDetail']['courseMiscellaneousFeeDue'].toFixed(2));
                    $('#totalMiscellaneousFeeDue').html('$' + obj['studentPaymentDetail']['totalMiscellaneousFeeDue'].toFixed(2));
                    $('#courseServiceFeeDue').html('$' + obj['studentPaymentDetail']['courseServiceFeeDue'].toFixed(2));
                    $('#totalServiceFeeDue').html('$' + obj['studentPaymentDetail']['totalServiceFeeDue'].toFixed(2));
                    $('#totalFeePaid').html('$' + obj['studentPaymentDetail']['totalFeePaid'].toFixed(2));

                    if (obj['paymentDetail'].length != 0) {

                        $.each(obj['paymentDetail'], function (i, item) {
                            var remarks = '-';
                            var editData = "";
                            var scheduleDol = "";
                            var scheduleIcon = "";
                            var scheduleDeleteIcon = "";
                            var agent_name = "";
                            if (allowToModifyScheduleInvoice == "yes") {
                                editData = '<li><a class="link-black text-sm modify-payment-schedule" data-toggle="tooltip" data-original-title="Modify Payment Schedule" id="' + item.id + '"> <i class="fa fa-edit"></i> </a></li>';
                            }

                            if (item.payment_status == 'unpaid' || item.payment_status == 'partially paid' || item.upfront_fee_pay < item.upfront_fee_to_pay) {
                                scheduleDol = '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Pay Schedule Fee" href="/student-pay-schedule-fee/' + student_id + '/' + item.student_payment_detail_id + '"><i class="fa fa-usd"></i></a></li>';
                                scheduleIcon = '<li><a class="link-black text-sm invoiceCredit" data-toggle="tooltip" data-original-title="Invoice Credit" id="' + item.id + '" href="javascript:;"><i class="fa fa-file-text"></i></a></li>';
                            }
                            if (item.payment_status == 'unpaid') {
                                scheduleDeleteIcon = '<li><a href="javascript:;" class="link-black text-sm delete delete-schedule-modal" data-toggle="modal"  data-original-title="Delete Schedule Payment Info" data-id="' + item.id + '" data-target="#deleteScheduleModal" ><i class="fa fa-remove"></i></a></li>';
                            }

                            if (item.remarks != "") {
                                remarks = item.remarks;
                            }
                            if (item.upfront_fee_pay > 0)
                            {
                                agent_name = '<span style="color: blue">' + item.agency_name + '</span>';
                            } else {
                                agent_name = item.agency_name;
                            }
                            var acurate_fee;
                            var check_due_date = item.due_date;
                            var new_due_date = check_due_date.split("-");
                            var final_due_date = new Date(new_due_date[2], new_due_date[1] - 1, new_due_date[0]);
//                            var new_due_date = moment(check_due_date, "YYY-MM-DDTHH:mm:ssZ").toDate();
//                            console.log(new_due_date);
//                            console.log(check_due_date.toDateString());
//                            console.log(Date());
                            
                            var invoiced_date = item.invoiced_start_date;
                            var from = invoiced_date.split("-");
                            var from_invoiced_date = new Date(from[2], from[1] - 1, from[0]);
                                
                            var current_date = new Date();
                            if (final_due_date.getTime() < current_date.getTime()) {
                                acurate_fee = item.upfront_fee_to_pay;
                            }else if (from_invoiced_date.getTime() > current_date.getTime()) {
                                acurate_fee = 0;
                            } else {
                                

                                var due_date = item.due_date;
                                var to = due_date.split("-");
                                var to_due_date = new Date(to[2], to[1] - 1, to[0]);

                                var date1 = new Date(from_invoiced_date);

                                var date2 = new Date(to_due_date);
                                var timeDiff = Math.abs(date2.getTime() - date1.getTime());
                                var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                                //acurate_fee = ((parseInt(item.upfront_fee_to_pay) / (parseInt(item.paid_duration_text) * 7)) * (diffDays));
                                var cdate = current_date.getFullYear()+'/'+(current_date.getMonth()+1)+'/'+current_date.getDate();
                                cdate=new Date(cdate);
                                var currentTimeDiff = Math.abs(cdate.getTime() - date1.getTime());
                                var currentTiffDays = Math.ceil(currentTimeDiff / (1000 * 3600 * 24));

                                acurate_fee = ((parseInt(item.upfront_fee_to_pay)* currentTimeDiff ) /  timeDiff);
                                
                            }
                            var table = '<tr id="dataAppend">' +
                                    '<td>' +
                                    '<div class="action-overlay">' +
                                    '<ul class="icon-actions-set">' +
                                    editData +
                                    '<li><a href="/agent-pro-invoice-pdf/' + student_id + '/' + course_id + '/' + item.invoice_number + '" class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Generate Agent Invoice"><i class="fa fa-file-pdf"></i></li>' +
                                    '<li><a href="/schedule-invoice-pdf/' + student_id + '/' + course_id + '/' + item.invoice_number + '" class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Generate Schedule Invoice"><i class="fa fa-file-code"></i></li>' +
                                    '<li><a href="javascript:;" class="link-black text-sm delete studentEmailSend" data-toggle="tooltip" data-original-title="Send Email" student-id="' + student_id + '" id="' + item.id + '" data-id="' + item.id + '" data-invoice-id="' + item.invoice_number + '"><i class="fa fa-envelope"></i></li>' +
                                    '<li><a href="javascript:;" class="link-black text-sm delete viewSchedulePaymentInfo" data-toggle="tooltip" data-original-title="View Schedule Payment Info" id="' + item.id + '"><i class="fa fa-search"></i></li>' +
                                    scheduleDol +
                                    scheduleIcon +
                                    scheduleDeleteIcon +
                                    '</ul>' +
                                    '</div>' +
                                    item.id +
                                    '</td>' +
                                    '<td>' + item.invoice_number + '</td>' +
                                    '<td>' + item.invoiced_start_date + '</td>' +
                                    '<td>' + item.due_date + '</td>' +
                                    '<td>' + ((item.payment_type == 'Schedual') ? 'Schedule Fee' : 'Initial Fee') + '</td>' +
                                    '<td>' + agent_name + '</td>' +
                                    '<td>' + '$' + (parseFloat(item.upfront_fee_to_pay).toFixed(2)) + '</td>' +
                                    '<td>' + '$' + (parseFloat(item.upfront_fee_pay).toFixed(2)) + '</td>' +
                                    '<td>' + "$" + parseFloat(acurate_fee).toFixed(2) + '</td>' +
                                    '<td>' + item.paid_duration_text + " " + item.duration_type + '</td>' +
                                    '<td>' + item.commission_value + "%+" + item.GST + '</td>' +
                                    '<td>' + '$' + item.commission + '</td>' +
                                    '<td>' + '$' + parseFloat(item.gst_amount).toFixed(2) + '</td>' +
                                    '<td>' + item.invoice_sent + '</td>' +
                                    '<td>' + '$' + parseFloat(item.invoice_credit).toFixed(2) + '</td>' +
                                    '<td>' + remarks + '</td>' +
                                    '</tr>';
                            $("#dataAppend").append(table);
                        });
                    } else {
                        noRecordFoundNew("#dataAppend", 0, 16);
                        //$("#dataAppend").append('<tr><td colspan="16" style="color:red; text-align: center">No Record Found</td></tr>');
                    }

                    var deleteIcon = obj['initialPaymentTransaction'].length;
                    //Paid Payment List For Student Table
                    var i = 1;

                    $.each(obj['initialPaymentTransaction'], function (i, item) {

                        var bonusGST = '';
                        var reversed = '';
                        var receiptSent = '';
                        var deleteIconShow = '';
                        var IconFlag = '';

                        if (item.bonus_gst == 'GST') {
                            bonusGST = '<input type="checkbox" name="bonus_gst_checkbox" checked disabled>';
                        } else {
                            bonusGST = '<input type="checkbox" name="bonus_gst_checkbox" disabled>';
                        }

                        if (item.reversed == 1) {
                            reversed = '<input type="checkbox" name="reversed_checkbox" checked disabled>';
                        } else {
                            reversed = '<input type="checkbox" name="reversed_checkbox" disabled>';
                        }

                        if (item.receipt_sent == 1) {
                            receiptSent = '<input type="checkbox" name="receipt_sent_checkbox" checked disabled>';
                        } else {
                            receiptSent = '<input type="checkbox" name="receipt_sent_checkbox" disabled>';
                        }

                        if (++i == deleteIcon) {
                            deleteIconShow = '<li><a class="link-black text-sm deleteTransactionModal" data-toggle="tooltip" data-original-title="Delete Transaction" id="' + item.id + '" href="javascript:;"><i class="fa fa-remove"></i></li>';
                        }
                        if (item.is_approved == 0 && item.is_process==0 ) {
                            IconFlag = '<i class="fa fa-flag" data-toggle="tooltip" data-original-title="Agent Commission Not Approved" style="color: dodgerblue;"></i>';
                        } else if (item.is_approved == 1 && item.is_process==0 ) {
                            IconFlag = '<i class="fa fa-flag" data-toggle="tooltip" data-original-title="Agent Commission Ready To Process" style="color: dodgerblue;"></i>';
                        } else {
                            IconFlag = '<i class="fa fa-flag" data-toggle="tooltip" data-original-title="Agent Commission Paid" style="color: dodgerblue;"></i>';
                        }

                        var table = '<tr id="paidPaymentAmount">' +
                                '<td>' +
                                '<div class="action-overlay">' +
                                '<ul class="icon-actions-set">';

                        if (item.reversed == 1) {
                            table += '<li><a class="link-black text-sm studentEmailSend" data-toggle="tooltip" data-original-title="Send Email" id="' + item.id + '" href="javascript:;"><i class="fa fa-envelope"></i></a></li>';
                            table += '<li><a class="link-black text-sm reverceBackTransaction" data-toggle="tooltip" data-original-title="Revert back to normal transaction" id="' + item.id + '" href="javascript:;"><i class="fa fa-arrow-left"></i></a></li>';
                        } else {
                            if (sessionPermissionData == 'yes') {
                                table += '<li><a class="link-black text-sm modify-transaction" data-toggle="tooltip" data-original-title="Modify Transaction" id="' + item.id + '" data-id="' + item.initial_payment_detail_id + '"> <i class="fa fa-edit"></i> </a></li>';
                            }
                            table +=
                                    '<li><a href="/tax-receipt-pdf/' + student_id + '/' + course_id + '/' + item.id + '" class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Generate Student Receipt"><i class="fa fa-file-pdf"></i></a></li>' +
                                    '<li><a href="/tax-agent-receipt/' + student_id + '/' + course_id + '/' + item.id + '" class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Generate Agent Receipt"><i class="fa fa-file-code"></i></a></li>' +
                                    //'<li><a href="/tax-agent-receipt/' + student_id + '/' + course_id + '/' + item.id + '" class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Generate Agent Receipt"><i class="fa fa-file-code"></i></a></li>' +
                                    '<li><a class="link-black text-sm delete studentEmailSendReceipt" data-toggle="tooltip" data-original-title="Send Email" id="' + item.id + '"  student-id="' + student_id + '"  course-id="' + course_id + '" data-id="' + item.id + '" data-invoice-id="' + item.invoice_number + '" href="javascript:;"><i class="fa fa-envelope"></i></a></li>' +
                                    deleteIconShow +
                                    '<li><a class="link-black text-sm feeRefundModal" data-toggle="tooltip" data-original-title="Refund Payment" id="' + item.id + '" href="javascript:;"><i class="fa fa-usd"></i></a></li>' +
                                    '<li><a class="link-black text-sm reverseTransaction" data-toggle="tooltip" data-original-title="Reverse Transaction" id="' + item.id + '" href="javascript:;"><i class="fa fa-arrows-alt"></i></a></li>';
                        }

                        table += '</ul></div>' +
                                IconFlag +
                                '</td>' +
                                '<td>' + item.transection_no + '</td>' +
                                '<td>' + item.initial_payment_detail_id + '</td>' +
                                '<td>' + item.receipt_no + '</td>' +
                                '<td>' + item.payment_date + '</td>' +
                                '<td>' + '$' + item.paid_amount.toFixed(2) + '</td>' +
                                '<td>' + '$' + item.deposited_amount.toFixed(2) + '</td>' +
                                '<td>' + item.payment_mode_value + '</td>' +
                                '<td>' + item.reference_no + '</td>' +
                                '<td>' + item.bank_deposit_date + '</td>' +
                                '<td>' + item.remarks + '</td>' +
                                '<td>' + '$' + item.scholarship.toFixed(2) + '</td>' +
                                '<td>' + '$' + item.amount_refund.toFixed(2) + '</td>' +
                                '<td>' + '$' + item.agent_bonus.toFixed(2) + '</td>' + //?
                                '<td>' + bonusGST + '</td>' +
                                '<td>' + item.bonus_paid_date + '</td>' +
                                '<td>' + '$' + ((item.bad_debt) > 0 ? item.bad_debt.toFixed(2) : 0) + '</td>' +
                                '<td>' + reversed + '</td>' +
                                '<td>' + receiptSent + '</td>' +
                                '</tr>';

                        $("#paidPaymentAmount").append(table);

                    });
                    if (obj['initialPaymentTransaction'].length == 0) {
                        noRecordFoundNew("#paidPaymentAmount", 0, 19);
                        //$("#paidPaymentAmount").append('<tr><td colspan="19" style="color:red; text-align: center">No Record Found</td></tr>');
                    }
                    radioCheckboxClass();
                    //End Paid Payment List For Student Table

                    var $el = $("#suggest_agent_commission");
                    $el.empty();
                    $el.append($("<option></option>").attr("value", "").text("--Select Agent Commission--"));
                    $.each(obj['paymentDetail'], function (i, item) {
                        $el.append($("<option></option>").attr("value", item.commission_value + "%+" + item.GST).text(item.commission_value + "%+" + item.GST));
                    });
                    $el.selectric('refresh');
                    //    $receiptNoMod
                    //console.log(obj['receiptNoMod'])
                    var $el = $("#receipt_no_mod");
                    $el.empty();
                    $.each(obj['receiptNoMod'], function (i, item) {
                        $el.append($("<option></option>").attr("value", item).text(item));
                    });
                    $el.selectric('refresh');

                    $("#course_id_schedule").val(course_id);
                    $('#suggest_agent_commission').on('change', function () {
                        var suggestValue = this.value;
                        var arrSplit = suggestValue.split('+');

                        if (arrSplit[1] == "GST") {
                            $("#GST_Schedule").prop("checked", true);
                        } else {
                            $("#NoGST_Schedule").prop("checked", true);
                        }
                        arrSplit[0] = arrSplit[0].replace("%", "");
                        $("#commission_value_schedule").val(arrSplit[0]);

                    });

                    if (obj['arrStudentInitialPayment'].record_save_status == '1') {
                        var newOptions = {"Schedule Only": "3"};
                        var $el = $("#payment_type");
                        $el.empty(); // remove old options
                        $.each(newOptions, function (key, value) {
                            $el.append($("<option></option>").attr("value", value).text(key));
                        });
                        $el.selectric('refresh');
                    }
                    if (obj['arrStudentInitialPayment'].record_save_status == '0') {
                        var newOptions = {"Initial Payment With Schedule": "1",
                            "Initial Payment Only": "2",
                            "Schedule Only": "3",
                        };
                        var $el = $("#payment_type");
                        $el.empty(); // remove old options
                        $.each(newOptions, function (key, value) {
                            $el.append($("<option></option>").attr("value", value).text(key));
                        });
                        $el.selectric('refresh');
                    }
                    if (obj['arrStudentInitialPayment'] == '') {
                        var newOptions = {"Initial Payment With Schedule": "1",
                            "Initial Payment Only": "2",
                            "Schedule Only": "3",
                        };
                        var $el = $("#payment_type");
                        $el.empty(); // remove old options
                        $.each(newOptions, function (key, value) {
                            $el.append($("<option></option>").attr("value", value).text(key));
                        });
                        $el.selectric('refresh');
                    }

                    //check last_invoice_date exixts or not
                    if (obj['lastInvoiceDueDate'] != null) {
                        var lastInvoiceDate = obj['lastInvoiceDueDate'];
                        $('#hidden_invoiced_due_date').val(lastInvoiceDate);
                    }
                    $('#hidden_course_start').val(obj['studentDetail'].start_date);

                    var hiddenInvoice = $('#hidden_invoiced_due_date').val();

                    if (hiddenInvoice == '') {

                        var dateInvoiceDue = obj['studentDetail'].start_date;
                        var startDueDate = new Date(dateInvoiceDue);

                        $("#invoiced_due_date").datepicker({
                            showOn: 'both',
                            changeMonth: true,
                            changeYear: true,
                            dateFormat: 'dd-mm-yyyy'
                        }).datepicker('setDate', startDueDate);

                    } else {
                        var dateInvoiceDue = hiddenInvoice;
                        var startDueDate = new Date(dateInvoiceDue);

                        $("#invoiced_due_date").datepicker({
                            showOn: 'both',
                            changeMonth: true,
                            changeYear: true,
                            dateFormat: 'dd-mm-yyyy'
                        }).datepicker('setDate', startDueDate);
                    }

                    var dateInvoiceStart = obj['studentDetail'].start_date;
                    var startDate = new Date(dateInvoiceStart);
                    $("#invoiced_start_date").datepicker({
                        dateFormat: 'dd-mm-yyyy'
                    }).datepicker('setDate', startDate);
                    $('#commission_period').trigger('change');
                }

            });
        });

        dateFormate('#invoiced_due_date , #credit_date, #invoiced_start_date, #invoiced_due_date_schedule, #invoiced_start_date_schedule');

        $("#invoiced_due_date_schedule").datepicker("setDate", new Date());

        $("#invoiced_start_date_schedule").datepicker("setDate", new Date());

        //get agent_commission_rate
        $('#commission_period').on('change', function () {
            var commissionPeriod = $('#commission_period').val();
            var courseId = $('#course_id').val();
            var agentId = $('#agent_id').val();
            var courseStart = $('#hidden_course_start').val();
            if (courseId != '') {
                if (commissionPeriod == '5') {
                    $('#commission_rate').find('option').remove();
                    $('#commission_rate').append($('<option>', {value: '', text: '0% + No GST'}));
                    $('#commission_rate').attr('disabled', true);
                    $('#applied_commission').val('0');
                    $('#gst_no').prop('checked', true);
                } else {
                    $('#commission_rate').attr('disabled', false);
                    $.ajax({
                        url: site_url + "student-account/ajaxAction",
                        method: "POST",
                        headers: {
                            'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                        },
                        data: {'action': 'getAgentCommissionRate', 'data': {'commissionPeriod': commissionPeriod, 'courseId': courseId, 'agentId': agentId, 'courseStart': courseStart}},
                        success: function (data) {
                            var obj = jQuery.parseJSON(data);

                            $('#commission_rate').find('option').remove();
                            if (obj.length > 0) {
                                $.each(obj, function (i, item) {
                                    $('#commission_rate').append($('<option>', {
                                        value: item.commission + '%+' + item.gst,
                                        text: item.commission + '%+' + item.gst
                                    }));
                                });
                            } else {
                                $('#commission_rate').append($('<option>', {
                                    value: '', text: 'No record found'
                                }));
                            }
                            $('#commission_rate').selectric('refresh');
                            $('#commission_rate').trigger('change');
                        }
                    });
                }
            } else {
                alert('Please Select Course First');
                $("#commission_period").val($("#commission_period option:first").val());
            }
        });

        //fill applied commission text box based on commission_rate
        $('#commission_rate').on('change', function () {
            var commissionRate = $('#commission_rate').val();
            var newValue = commissionRate.split('%+');

            $('#applied_commission').val(newValue[0]);
            if (newValue[1] == 'GST') {
                $("#gst_yes").iCheck('check');
            } else {
                $("#gst_no").iCheck('check');
            }
        });
//        checkDateRange('.dateField', '#invoiced_start_date', '#invoiced_due_date', 'Invoice Start Date Must be after Due Date');
        $('body').on('click', '.modify-payment-schedule', function () {
            var paymentId = $(this).attr('id');
            $('#editModel').modal('show');
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "student-account/ajaxAction",
                data: {'action': 'modifyPaymentSchedule', 'data': {'id': paymentId}},
                success: function (data) {
                    var obj = jQuery.parseJSON(data);
                    //console.log(obj[0]);
                    $("#invoiced_start_date").datepicker('setDate', new Date(obj[0].invoiced_start_date));
                    $("#invoiced_due_date").datepicker('setDate', new Date(obj[0].due_date));
                    
                    
                    $('#agent_id').val(obj[0].agent_id);
                    if(obj[0].payment_status=='paid' || obj[0].upfront_fee_pay!=0){
                        $("#agent_id").attr("disabled", "disabled").selectric('refresh');
                        $("#commission_value").attr("disabled", "disabled");
                        
                    }else{
                        $("#agent_id").removeAttr("disabled").selectric('refresh');
                        $("#commission_value").removeAttr("disabled");
                    }
                    $('#payment_status').val(obj[0].payment_status);
//                    $('#invoiced_start_date').val(dateObject);
//                    $('#invoiced_due_date').val(dateObjectDueDate);
                    $('#commission_value').val(obj[0].commission_value);
                    $('#upfront_fee_to_pay').val(obj[0].upfront_fee_to_pay);
                    $('#paid_duration_text').val(obj[0].paid_duration_text);
                    $('#paid_duration_day').val(obj[0].paid_duration_day);
                    $('#remarks').val(obj[0].remarks);
                    $('#edit_id').val(obj[0].id);

                    if (obj[0].GST == 'GST') {
                        $("#GST").iCheck('check');
                        //$("#GST").prop('checked', true);
                    } else {
                        $("#NoGST").iCheck('check')
                        //$("#NoGST").prop('checked', true);
                    }
                    $('#editModel').modal('show');

                    $('#paid_duration_day').selectric('refresh');
                    $('#agent_id').selectric('refresh');
                },
                error: function (err) {

                }
            });
        });

        $('.modify-payment-service').on('click', function () {
            //var paymentId = $(this).attr('id');
            $('#modifyPaymentService').modal('show');
//            $.ajax({
//                type: 'POST',
//                headers: {
//                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
//                },
//                url: site_url + "student-account/ajaxAction",
//                data: {'action': 'modifyPaymentSchedule', 'data': {'id': paymentId}},
//                success: function(data) {
//                    var obj = jQuery.parseJSON(data);
//
//                    var dateParts = obj[0].invoiced_start_date.split("-");
//                    var dateObject = dateParts[2] + "-" + dateParts[1] + "-" + dateParts[0];
//                    var datePartsDue = obj[0].due_date.split("-");
//                    var dateObjectDueDate = datePartsDue[2] + "-" + datePartsDue[1] + "-" + datePartsDue[0];
//                    $('#agent_id').val(obj[0].agent_id);
//                    $('#invoiced_start_date').val(dateObject);
//                    $('#invoiced_due_date').val(dateObjectDueDate);
//                    $('#commission_value').val(obj[0].commission_value);
//                    $('#upfront_fee_to_pay').val(obj[0].upfront_fee_to_pay);
//                    $('#paid_duration_text').val(obj[0].paid_duration_text);
//                    $('#paid_duration_day').val(obj[0].paid_duration_day);
//                    $('#remarks').val(obj[0].remarks);
//                    $('#edit_id').val(obj[0].id);
//                    if (obj[0].GST == 'GST') {
//                        $("#GST").prop('checked', true);
//                    }
//                    else {
//                        $("#NoGST").prop('checked', true);
//                    }
//                    $('#editModel').modal('show');
//                },
//                error: function(err) {
//
//                }
//            });
        });

        $('body').on('click', '.modify-transaction', function () {
            var paymentId = $(this).attr('id');
            var student_id = $('#student_id').val();

            $.ajax({
                type: 'POST',
                headers: {'X-CSRF-TOKEN': $('input[name="_token"]').val()},
                url: site_url + "student-payment/ajaxAction",
                data: {'action': 'modifyTransaction', 'data': {'student_id': student_id, 'paymentId': paymentId}},
                success: function (data) {
                    var obj = jQuery.parseJSON(data);

                    dateFormate('#bonus_paid_date_mod, #payment_date_mod, #bank_deposite_date_mod, #receipt_sent_date');

                    $('#transactoionNumberMod').empty();
                    $('#scheduleFeeMod').empty();
                    $('#transactionAmountMod').empty();

                    $('#transactoionNumberMod').append(obj[0].transection_no);
                    $('#scheduleFeeMod').append('$' + obj[0].upfront_fee_to_pay.toFixed(2));
                    $('#transactionAmountMod').append('$' + obj[0].paid_amount.toFixed(2));
                    $('#transactionAmountModHid').val(obj[0].paid_amount);

                    $('#mod_edit_id').val(obj[0].id);
                    $('#agent_bonus_mod').val(obj[0].agent_bonus);

                    $('[name="GST_Mod"]:checked').prop("checked", false);
                    $('[name="GST_mod"][value="' + obj[0].bonus_gst + '"]').prop("checked", true);

                    $('#receiptSent_mod').prop("checked", (obj[0].receipt_sent == 1));

                    $('#bank_deposited_mod').val(obj[0].deposited_amount);
                    $('#remarks_mod').val(obj[0].remarks);

                    $('#payment_mode_mod').val(obj[0].payment_mode);
                    $('#bad_debt_mod').val(obj[0].bad_debt);
                    $('#receipt_no_mod').val(obj[0].receipt_no);

                    $('[name="afrterAgentBonus"]').prop('checked', false);
                    $('[name="afrterAgentBonus"]').prop('disabled', (obj[0].paidCommission > 0));


                    if (obj[0].bonus_paid_date != "" && obj[0].bonus_paid_date != null) {
                        $("#bonus_paid_date_mod").datepicker('setDate', new Date(obj[0].bonus_paid_date));
                    }
                    if (obj[0].payment_date != "" && obj[0].payment_date != null) {
                        $("#payment_date_mod").datepicker('setDate', new Date(obj[0].payment_date));
                    }
                    if (obj[0].bank_deposit_date != "" && obj[0].bank_deposit_date != null) {
                        $("#bank_deposite_date_mod").datepicker('setDate', new Date(obj[0].bank_deposit_date));
                    }
                    if (obj[0].receipt_sent_date != "" && obj[0].receipt_sent_date != null) {
                        $("#receipt_sent_date").datepicker('setDate', new Date(obj[0].receipt_sent_date));
                    }
                    $('#modifyTransaction').modal('show');
                    $('#payment_mode_mod').selectric('refresh');
                },
                error: function (err) {

                }
            });
        });

        $('.generateSchedule').on('click', function () {
            //var paymentId = $(this).attr('id');
            if ($('#course_id').val() == "") {
                alert("Please Select Course First");
            } else {
                $(this).attr('data-original-title', '');
                $('#generateSchedule').modal('show');
                //console.log('hi');
                setTimeout(function () {
                    //$('#NoGST_Schedule').trigger('click');
                    $("#invoice_number").focus();
                }, 500);
            }
        });

        $('.close').on('click', function () {
            $('.generateSchedule').attr('data-original-title', 'Create New Payment Schedule');
        });

        $('#combineLink').on('click', function () {
            //    var paymentId = $(this).attr('id');
            if ($('#course_id').val() == "") {
                alert("Please Select Course First")
            } else {
                $("#combineLink").attr("href", "/combine-payment-schedule/" + $('#course_id').val() + "/" + $('#student_id').val() + "/" + 0);
            }
        });

        $('.statementOfAccount').on('click', function () {
            //    var paymentId = $(this).attr('id');
            if ($('#course_id').val() == "") {
                alert("Please Select Course First");
            } else {
                $(".statementOfAccount").attr("href", "/statement-of-account/" + $('#course_id').val() + "/" + $('#student_id').val());
            }
        });

        $('.studentInvoice').on('click', function () {
            //    var paymentId = $(this).attr('id');
            if ($('#course_id').val() == "") {
                alert("Please Select Course First");
            } else {
                $(".studentInvoice").attr("href", "/student-payment-invoice-receipt-pdf/" + $('#student_id').val() + "/" + $('#course_id').val());
            }
        });

        $('body').on('click', '.reverseTransaction', function () {
            var paymentId = $(this).attr('id');
            $('#reverse_transaction_id').val(paymentId);
            $('#reverse_comment').val('');
            $('#reverseTransaction').modal('show');
        });

        $('body').on('click', '.viewSchedulePaymentInfo', function () {
            var paymentDetailId = $(this).attr('id');
            $('#paymentInfoData tbody tr').remove();
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "student-account/ajaxAction",
                data: {'action': 'getPaymentScheduleInfo', 'data': {'paymentDetailId': paymentDetailId}},
                success: function (data) {
                    var obj = jQuery.parseJSON(data);

                    if (obj.length == 0) {
                        $('#noRecords').show();
                    }
                    $.each(obj, function (i, item) {

                        var table = '<tr id="">' +
                                '<td>' + item.transection_no + '</td>' +
                                '<td>' + item.receipt_no + '</td>' +
                                '<td>' + item.payment_date + '</td>' +
                                '<td>' + item.paid_amount + '</td>' +
                                '<td>' + item.deposited_amount + '</td>' +
                                '<td>' + item.payment_mode_name + '</td>' +
                                '<td>' + item.reference_no + '</td>' +
                                '<td>' + (item.remarks == '' || item.remarks == null ? '' : item.remarks) + '</td>' +
                                '<td>' + item.bank_deposit_date + '</td>' +
                                '<td>' + item.amount_refund + '</td>' +
                                '</tr>';

                        $("#paymentInfoData tbody").append(table);
                        //$('#noRecords').hide();
                    });
                    noRecordFoundNew("#paymentInfoData tbody", obj.length, 10);

                    $('#viewScheduleInfo').modal('show');
                }
            });
        });

        function getAgentname(courseId, studentId) {
            ajaxCall(site_url + "student-account/ajaxAction", {'action': 'getAgentDropdownDataV2', 'data': {'courseId': courseId, 'studentId': studentId}}, function (data) {
                var data = jQuery.parseJSON(data);
                setOption('.agent_email', data);
            });
        }
        $('body').on('click', '.studentEmailSend', function () {
            $('#studentInfoEmail')[0].reset();
            $('#studentEmailSend').modal('show');
            $('#enrolled_course_id').val($('#course_id').val());
            $('#studentPaymentId').val($(this).attr('data-id'));
            $('#studentInvoiceNo').val($(this).attr('data-invoice-id'));
            getAgentname($('#course_id').val(), $(this).attr('student-id'));
        });
        $('body').on('click', '.studentEmailSendReceipt', function () {
            $('#studentInfoEmail')[0].reset();
            $('#studentReceiptEmailSend').modal('show');
            $('#enrolled_student_id').val($(this).attr('student-id'));
            $('#enrolled_course_id_receipt').val($('#course_id').val());
            $('#studentPaymentId_receipt').val($(this).attr('data-id'));
            $('#studentInvoiceNo_receipt').val($(this).attr('data-invoice-id'));
            getAgentname($('#course_id').val(), $(this).attr('student-id'));
        });


        $('body').on('click', '.invoiceCredit', function () {
            var paymentDetailId = $(this).attr('id');
            $('#schedule_amount_value').html('');
            $('#paid_amount_value').html('');
            $('#credit_amount').val('');
            $('#transaction_processing').html('');
            $('#invalid_amount_message').html('');

            $('#hidden_student_id').val('');
            $('#hidden_course_id').val('');
            $('#hidden_initial_payment_detail_id').val('');
            $('#hidden_credit_amount').val('');
            //$('#invoiceCreditInfoData tbody tr').remove();
            noRecordFoundNew('#invoiceCreditInfoData tbody', 0, 3);

            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "student-account/ajaxAction",
                data: {'action': 'getInvoiceCreditInfo', 'data': {'paymentDetailId': paymentDetailId}},
                success: function (data) {
                    var obj = jQuery.parseJSON(data);
                    var invoiceCredit = obj.objInvoiceCredit[0];

                    $('#schedule_amount_value').html(invoiceCredit.upfront_fee_to_pay);
                    $('#paid_amount_value').html(invoiceCredit.upfront_fee_pay);
                    $('#credit_amount').val(parseFloat(invoiceCredit.upfront_fee_to_pay) - parseFloat(invoiceCredit.upfront_fee_pay) - parseFloat(invoiceCredit.invoice_credit));

                    $('#hidden_student_id').val(invoiceCredit.student_id);
                    $('#hidden_course_id').val(invoiceCredit.course_id);
                    $('#hidden_initial_payment_detail_id').val(paymentDetailId);
                    $('#hidden_credit_amount').val($('#credit_amount').val());

                    $.each(obj.objInvoiceTransactionProcessing, function (i, item) {
                        var amount = (parseFloat(item.upfront_fee_to_pay) - parseFloat(item.upfront_fee_pay) - parseFloat(item.invoice_credit));
                        var startDate = '<input type="checkbox" class="trasnsaction_processing_checkbox" name="trasnsaction_processing[]" value="' + item.id + '-' + amount + '" id="' + 'processingType' + item.id + '">' + '<label for="' + 'processingType' + item.id + '">' + item.invoiced_start_date;
                        $('#transaction_processing').append(startDate + ' - $' + amount + '</label><br/>');
                    });
                    radioCheckboxClass();
                    if (obj.objInvoiceCreditDetail.length == 0) {
                        $('#noRecords').show();
                    }
                    var table = '';
                    $.each(obj.objInvoiceCreditDetail, function (i, item) {
                        var remarks = '';
                        if (item.remarks == null) {
                            remarks = '--';
                        } else {
                            remarks = item.remarks;
                        }
                        table += '<tr>' +
                                '<td>' +
                                '<div class="action-overlay">' +
                                '<ul class="icon-actions-set">' +
                                '<li><span data-toggle="modal" class="delete" data-id="' + item.id + '" data-target="#deleteModal"> <a class="link-black text-sm" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>' +
                                '</ul>' +
                                '</div>' +
                                item.credit_date +
                                '</td>' +
                                '<td>$' + item.credit_amount + '</td>' +
                                '<td>' + (remarks == '' || remarks == null ? '' : remarks) + '</td>' +
                                '</tr>';
                    });
                    $("#invoiceCreditInfoData tbody").html(table);
                    noRecordFoundNew('#invoiceCreditInfoData tbody', obj.objInvoiceCreditDetail.length, 3);

                    $('.invoiceCredit').tooltip('hide');
                    $('#invoiceCredit').modal('show');
                }
            });
            $('#invoiceCredit').modal('show');
        });

        deleteSingleData(site_url + 'delete-student-invoice-credit/');

        $('#email_template').on('change', function () {
            var that = $(this);
            emailTemplateTypeChange(that);
        });
        $('#email_template_receipt').on('change', function () {
            var that = $(this);
            emailTemplateTypeChangeReceipt(that);
        });

        function emailTemplateTypeChange(that) {
            var EmailContentId = $("#email_template").val();
            var courseId = $("#course_id").val();
            var studentId = $("#student_id").val();
            $('#email_subject').val("");
            CKEDITOR.instances['editor1'].setData("");
            if (EmailContentId != "") {
                loadingStart(that);
                $.ajax({
                    url: site_url + "studentsController/ajaxAction",
                    method: "POST",
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    data: {'action': 'getEmailContent', 'data': {'EmailContentId': EmailContentId, 'courseId': courseId, 'studentId': studentId}},
                    success: function (data) {
                        loadingEnd(that);
                        var obj = jQuery.parseJSON(data);
                        $.each(obj, function (i, item) {
                            //console(item.content);
                            $('#email_subject').val(item.email_subject);
                            CKEDITOR.instances['editor1'].setData(item.content);
                        });
                    }
                });
            }
        }
        function emailTemplateTypeChangeReceipt(that) {
            var EmailContentId = $("#email_template_receipt").val();
            $('#email_subject').val("");
            CKEDITOR.instances['editor1'].setData("");
            if (EmailContentId != "") {
                loadingStart(that);
                $.ajax({
                    url: site_url + "offerManage/ajaxAction",
                    method: "POST",
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    data: {'action': 'getEmailContent', 'data': {'EmailContentId': EmailContentId}},
                    success: function (data) {
                        loadingEnd(that);
                        var obj = jQuery.parseJSON(data);
                        $.each(obj, function (i, item) {
                            //console(item.content);
                            $('#email_subject_receipt').val(item.email_subject);
                            CKEDITOR.instances['editor1_receipt'].setData(item.content);
                        });
                    }
                });
            }
        }
        $("#flip").click(function () {
            $("#panel").slideToggle("slow");
        });

//        $('body').on('click', '.delete', function() {
//            var dataid = $(this).attr('data-id');
//            $('.yes-sure').attr('data-id', dataid);
//        });
//
//        $('.yes-sure').click(function() {
//            var dataid = $(this).attr('data-id');
//            window.location = (site_url + 'delete-student-invoice-credit/' + dataid);
//        });
    };

    var initialPayment = function () {

        dateFormate('#invoiced_due_date, #invoiced_start_date');

//        $('#invoiced_due_date, #invoiced_start_date').datepicker({
//            autoclose: true,
//            //format: 'yyyy-mm-dd'
//            format: 'dd-mm-yyyy'
//        });

    };

    var studentRecord = function () {

        $('#recordPaymentButton').on('click', function () {
            $("#recordInitialPaymentForm").valid();
        });


        //validation
        $("#recordInitialPaymentForm").validate({
            rules: {
                payment_mode: {
                    required: true
                },
                refrence_number_payment: {
                    required: true
                }
            },
            errorPlacement: function (error, element) {
                //$('#frequencyDatePopup').modal('hide');
                $(window).scrollTop(0);

            },
            success: function (element) {
                //$('#frequencyDatePopup').modal('show');
                // alert(element.length);
                $('#recordPaymentButton').attr('data-target', '#frequencyDatePopup');
            }
        });

        $('body').on('ifChanged', '#agent_commission_deduct', function () {
            initPaymentCalculation();
        });

        $('body').on('ifChanged', '#provide_agent_bonus', function () {
            var isChecked = $("#provide_agent_bonus").is(":checked");
            if (isChecked) {
                $('#agent_bonus').show();
            } else {
                $('#agent_bonus').hide();
                $('#applied_commission_text').val(0);
            }
            initPaymentCalculation();
        });
        $('body').on('ifChanged', '#enrollment_fee', function () {
            var isChecked = $("#enrollment_fee").is(":checked");
            if (isChecked) {
                $('#enrollment_fee_span').show();
                $('#enrollment_fee_text').val($('#enrollment_fee_value_hidden').val());
            } else {
                $('#enrollment_fee_span').hide();
                $('#enrollment_fee_text').val(0);
            }
            initPaymentCalculation();
        });
        $('body').on('ifChanged', '#material_fee', function () {
            var isChecked = $("#material_fee").is(":checked");
            if (isChecked) {
                $('#material_fee_span').show();
                $('#material_fee_text').val($('#material_fee_value_hidden').val());
            } else {
                $('#material_fee_span').hide();
                $('#material_fee_text').val(0);
            }
            initPaymentCalculation();
        });

        $('body').on('ifChanged', '#oshc_fee', function () {
            var isChecked = $("#oshc_fee").is(":checked");
            if (isChecked) {
                $('#oshc_span').show();
                $('#oshc_fees').val($('#oshc_fees_hidden').val());
            } else {
                $('#oshc_span').hide();
                //$('#oshc_fees').val(0);
            }
            initPaymentCalculation();
        });

        $('body').on('ifChanged', '#other_service_fee', function () {
            var isChecked = $("#other_service_fee").is(":checked");
            if (isChecked) {
                $('#other_service_fee_span').show();
            } else {
                $('#other_service_fee_span').hide();
            }
            initPaymentCalculation();
        });
        $('body').on('ifChanged', '#apply_surcharge', function () {
            var isChecked = $("#apply_surcharge").is(":checked");
            if (isChecked) {
                $('#surcharge_amount_span').show();
            } else {
                $('#surcharge_amount_span').hide();
                $('#surcharge_amount').val(0);
            }
            initPaymentCalculation();
        });
        $('body').on('ifChanged', '#add_debt_amount', function () {
            var isChecked = $("#add_debt_amount").is(":checked");
            if (isChecked) {
                $('#add_debt_amount_span').show();
            } else {
                $('#add_debt_amount_span').hide();
            }
            initPaymentCalculation();
        });

        dateFormate('#payment_date, #bank_deposite_date, #schedual_start_date');

//        $('#payment_date').datepicker({
//            autoclose: true,
//            //format: 'yyyy-mm-dd'
//            format: 'dd-mm-yyyy'
//        });
//        $('#bank_deposite_date').datepicker({
//            autoclose: true,
//            //format: 'yyyy-mm-dd'
//            format: 'dd-mm-yyyy'
//        });
        $('#upfront_fee_to_pay').on('change', function () {
                
            var upValue = this.value;
            var total_fee = $('#total_course_fee').val();
            if(upValue=="")
            {
                $('#upfront_fee_to_pay').val(0);
            }
            if (parseInt(upValue) > parseInt(total_fee))
            {
                $('#upfront_fee_to_pay').val($('#upfront_fee_to_pay_hidden').val());
            }
            $('#upfront_fee_pay').val($('#upfront_fee_to_pay').val());
            var total_course_fee = parseInt($('#total_course_fee').val()) - parseInt($('#upfront_fee_to_pay').val());
            $('#amount').val(total_course_fee);
            dayCalculate();
            schedualDateCalculation()
        });

        $('#amount').on('change', function () {
            var total_fee = $('#total_course_fee').val();
            var changeAmount = parseInt(total_fee) - parseInt(this.value);
            $('#upfront_fee_to_pay').val(changeAmount);
            //   schedualDateCalculation();
        });
        $('#paid_duration_text').on('change', function () {
            schedualDateCalculation()
        });
        $('#paid_duration_day').on('change', function () {
            schedualDateCalculation()
        });
        $('#payment_date').on('change', function () {
            schedualDateCalculation()
        });
        $('#payment_mode').on('change', function () {
            $('#refrence_number').show();
        });


        restrictNumeric('.amountCal');
        dayCalculate();
        schedualDateCalculation();
        function dayCalculate() {
            var nuOfWeeks = $('#total_course_weeks').val()
            var courseFee = $('#total_course_fee').val()
            var upfrontFeeToPay = $('#upfront_fee_to_pay').val()
            
//            if(upfrontFeeToPay!="")
//            {
                var dayCount = ((parseInt(nuOfWeeks) * 7) * parseInt(upfrontFeeToPay)) / parseInt(courseFee)
                var finalAmount = Math.round(dayCount);
                var weekCount = Math.round(parseInt(finalAmount) / 7);
//            }else{
//                var weekCount=0;
//            }
            $('#paid_duration_text').val(weekCount);
        }
        function schedualDateCalculation() {

            var paymentDate = $('#payment_date').val();
            var paid_duration_text = $('#paid_duration_text').val();
            var noOfInstallment = $('#no_of_installment').val();
            var SchedualAmount = $('#amount').val();
            if ($('#agent_applied_commission').val() == undefined)
            {
                var applied_commission_text = '0';
            } else {
                var applied_commission_text = $('#agent_applied_commission').val();
            }
            var agent_applied_gst = $('#agent_applied_gst').val();

            var paid_duration_day = $('#paid_duration_day').val();
//            var paid_duration_text = $('#paid_duration_text').val();
            if (paid_duration_day == '2') {
                var weekDays = 7 * parseInt(paid_duration_text);
                var monthCount = 0;
                var yearCount = 0;
            }
            if (paid_duration_day == '3') {
                var weekDays = 0;
                var monthCount = 1 * parseInt(paid_duration_text);
                var yearCount = 0;
            }
            if (paid_duration_day == '4') {
                var weekDays = 0;
                var monthCount = 0;
                var yearCount = 1 * parseInt(paid_duration_text);
            }
            if (paid_duration_day == '1') {
                var weekDays = 1 * parseInt(paid_duration_text);
                var monthCount = 0;
                var yearCount = 0;
            }
            if (typeof paid_duration_day === "undefined") {
                var weekDays = 7 * parseInt(paid_duration_text);;
                var monthCount = 0 ;
                var yearCount = 0;
            }
            var calFianlIns = parseInt(SchedualAmount) / parseInt(noOfInstallment)
//           
            var formattedDate = paymentDate,
                    dateTokens = formattedDate.split('-'),
                    dt = new Date(parseInt(dateTokens[2]) + yearCount, parseInt(dateTokens[1], 10) - 1 + monthCount, parseInt(dateTokens[0]) + weekDays), // months are 0 based, so need to add 1
//                    dt = new Date(dateTokens[2], parseInt(dateTokens[1], 10) - 1, parseInt(dateTokens[0]) + weekDays), // months are 0 based, so need to add 1
                    countDate = new Date(dt.getTime());
            countDate.setDate(countDate.getDate());   // 36 week * 7 day = 252
            var getFinalDate = countDate.getDate() + "-" + (parseInt(countDate.getMonth()) + 1) + "-" + countDate.getFullYear();
            $('#schedualDate').empty();
            $('#dataAppend').empty();

            var paymentType= $('#payment_type').val();
            if(paymentType==3){
                var getFinalDate = $('#payment_date').val();
            }
            

            $("#schedual_start_date").datepicker({
                dateFormat: 'dd-mm-yyyy'
            }).datepicker('setDate', getFinalDate);
            $('#schedualDate').text(getFinalDate);
            $('#schedual_Date_value').val(getFinalDate);
// Modal Popup Append Field Start Here
            var formattedDate = getFinalDate,
                    dateTokens = formattedDate.split('-'),
                    startDateCount = new Date(parseInt(dateTokens[2]), parseInt(dateTokens[1], 10) - 1, parseInt(dateTokens[0])), //TODO::GNG-2893 (remove -59)
                    //startDateCount = new Date(parseInt(dateTokens[2]), parseInt(dateTokens[1], 10) - 1, parseInt(dateTokens[0]) - 59), // months are 0 based, so need to add 1
                    countDateStart = new Date(startDateCount.getTime());
            startDateCount.setDate(startDateCount.getDate());   // 36 week * 7 day = 252
            var getFinalDateStart = startDateCount.getDate() + "-" + (parseInt(startDateCount.getMonth()) + 1) + "-" + startDateCount.getFullYear();
            var table = '<tr id="dataAppend"><td>' +
                    '<input class="form-control" type="text" value="' + getFinalDateStart + '" name="start_date_cal[]" style="  text-indent: 10px;">' + '</td><td>' +
                    '<input class="form-control" type="text" value="' + getFinalDate + '" name="due_date[]"  style="  text-indent: 10px;">' + '</td><td>' +
                    '<input class="form-control" type="text" value="' + parseFloat(Math.round(calFianlIns * 100) / 100).toFixed(2) + '" name="calFianlIns[]" style="  text-indent: 10px;" class="finalAmount">' + '</td><td>' +
                    '<input class="form-control" type="text" value="' + applied_commission_text + '" name="agentCommissionCal[]" style="  text-indent: 10px;">' + '</td><td>';
            if (agent_applied_gst == 'GST') {
                table +=
                        '<input type="radio" class="gst" name="gst_cal' + 1 + '" style="text-indent: 10px;" value="GST" checked="checked">' + '<label style="margin: 0 10px;font-weight: 400;" for="default_time_yes">GST</label>' +
                        '<input type="radio" class="gst" name="gst_cal' + 1 + '" style="text-indent: 10px;" value="No GST">' + '<label style="margin: 0 10px;font-weight: 400;" for="default_time_yes">No GST</label>';
            } else {
                table +=
                        '<input type="radio" class="gst" name="gst_cal' + 1 + '" style="text-indent: 10px;" value="GST" >' + '<label style="margin: 0 10px;font-weight: 400;" for="default_time_yes">GST</label>' +
                        '<input type="radio" class="gst"  name="gst_cal' + 1 + '" style="text-indent: 10px;" value="No GST" checked="checked">' + '<label style="margin: 0 10px;font-weight: 400;" for="default_time_yes">No GST</label>';
            }
            table += '</td></tr>';
            $("#dataAppend").html(table);
            radioCheckboxClass();
            // Modal Popup Append Field End Here

            scheduleDateCalculationV2();    //TODO::GNG-2893 (Call new implement function for date arrangement)
        }

        //TODO::GNG-2893 (Start)
        $(document).on('change', '.frequencyCount', function () {
            let tempVar = ($(this).attr('id') == 'schedual_start_date') ? $('#schedual_start_date').val() : $('#schedual_Date_value').val();
            $('#schedual_Date_value').val(tempVar);
            scheduleDateCalculationV2();
        });

        function scheduleDateCalculationV2(){
            $('#schedualDate').html('');
            $('#errorDate').text('');

            let frequency = $('#no_frequency').val();
            let frequencyType = $('#day_frequency').val();
            let noOfInstallment = $('#no_of_installment').val();
            let course_finish_date = $('#course_finish_date').val();
            let appliedCommissionText = ($('#agent_applied_commission').val() == undefined) ? 0 : $('#agent_applied_commission').val();
            let tempVar = ($(this).attr('id') == 'schedual_start_date') ? $('#schedual_start_date').val() : $('#schedual_Date_value').val();
            let agentAppliedGST = $('#agent_applied_gst').val();
            let scheduleAmount = $('#amount').val();
            let calFianlIns = parseInt(scheduleAmount) / parseInt(noOfInstallment)
            let errorFlag = false;

            let tempStartDateString = $('#schedual_start_date').val();
            let tempStartDateArr = tempStartDateString.split('-');
            let tempStartDate = new Date(parseInt(tempStartDateArr[2]), parseInt(tempStartDateArr[1]) - 1, parseInt(tempStartDateArr[0]));
            let dueDateArr = [];
            let tableHtml = '';
            let remainingBalance = parseFloat(scheduleAmount);

            let radioLabelStyle = "margin: 0 10px;font-weight: 400;";
            for (var i=1; i<=noOfInstallment; i++) {
                let tempEndDate = getEndDate(tempStartDate, 1, parseInt(frequency), frequencyType);

                if ((Date.parse(tempEndDate) <= Date.parse(course_finish_date))) {
                    let viewTempStartDate = formatDate(tempStartDate);
                    let viewTempEndDate = formatDate(tempEndDate);

                    dueDateArr.push(viewTempEndDate);

                    let instAmount = (i == noOfInstallment) ? remainingBalance.toFixed(2) : calFianlIns.toFixed(2);
                    remainingBalance -= calFianlIns.toFixed(2);

                    // Modal Popup Append Field Start Here
                    let isCheckedGST = agentAppliedGST === 'GST';
                    tableHtml += `<tr id="dataAppend">
                                    <td><input type="text" name="start_date_cal[]" class="form-control" value="${viewTempStartDate}" style="text-indent: 10px;"></td>
                                    <td><input type="text" name="due_date[]" class="form-control" value="${viewTempEndDate}" style="text-indent: 10px;"></td>
                                    <td><input type="text" name="calFianlIns[]" class="form-control finalAmount" value="${instAmount}" style="text-indent: 10px;"></td>
                                    <td><input type="text" name="agentCommissionCal[]" class="form-control" value="${appliedCommissionText}" style="text-indent: 10px;"></td>
                                    <td>
                                        <input type="radio" class="gst" name="gst_cal${i}" style="text-indent: 10px;" value="GST" ${isCheckedGST ? 'checked' : ''} > 
                                        <label style="${radioLabelStyle}" for="default_time_yes">GST</label>
                                        <input type="radio" class="gst" name="gst_cal${i}" style="text-indent: 10px;" value="No GST" ${isCheckedGST ? '' : 'checked'} > 
                                        <label style="${radioLabelStyle}" for="default_time_no">No GST</label>
                                    </td>
                                </tr>`;

                    tempStartDate = tempEndDate;
                } else {
                    errorFlag = true;
                    break;
                }
            }

            if(errorFlag){
                $('#errorDate').text("Schedule Date is out of Course Date. Please insert valid date range.");
                $('#recordPaymentButton').prop('disabled', true);
            }else{
                $('#recordPaymentButton').prop('disabled', false);
                $("#dataAppend").html(tableHtml);
                $('#schedualDate').text(dueDateArr.join(", "));
                radioCheckboxClass();
            }
        }

        function getEndDate(startDate, installment = 1, frequency = 1, duration = 'day') {
            const startDateObj = new Date(startDate);
            // Calculate the new date based on the provided parameters
            switch (duration) {
                case 'day':
                case '1':
                    startDateObj.setDate(startDateObj.getDate() + (frequency * installment));
                    break;
                case 'week':
                case '2':
                    startDateObj.setDate(startDateObj.getDate() + 7 + (frequency * installment));
                    break;
                case 'month':
                case '3':
                    startDateObj.setMonth(startDateObj.getMonth() + (frequency * installment));
                    break;
                case 'year':
                case '4':
                    startDateObj.setFullYear(startDateObj.getFullYear() + (frequency * installment));
                    break;
                // Add more cases if needed for other durations

                default:
                    // Unsupported duration
                    throw new Error('Unsupported duration');
            }
            // Format the result as "YYYY-MM-DD"
            //const endDate = startDateObj.toISOString().split('T')[0];
            const endDateObj = startDateObj;
            return endDateObj;
        }

        function formatDate(date) {
            let day = date.getDate().toString().padStart(2, '0');
            let month = (date.getMonth() + 1).toString().padStart(2, '0');
            let year = date.getFullYear();

            return `${day}-${month}-${year}`;
        }
        //TODO::GNG-2893 (End)
    };



    var studentScholarship = function () {

        dateFormate('#scholarship_date');
        deleteSingleData(site_url + 'delete-student-scholarship/');

        $("#studentScholarshipForm").validate({
            rules: {
                course_id: {
                    required: true,
                },
                scholarship_date: {
                    required: true,
                },
                scholarship_amount: {
                    required: true, number: true
                },
                comment: {
                    required: true,
                },
            },
            errorPlacement: function (error, element) {
            }
        });

//        $('#scholarship_date').datepicker({
//            autoclose: true,
//            format: 'dd-mm-yyyy'
//        });

//        $('body').on('click', '.delete', function() {
//            var dataid = $(this).attr('data-id');
//            $('.yes-sure').attr('data-id', dataid);
//        });
//
//        $('.yes-sure').click(function() {
//            var dataid = $(this).attr('data-id');
//            window.location = (site_url + 'delete-student-scholarship/' + dataid);
//        });

    }

    var studentAgentCommission = function () {

        dateFormate('.dateField');
        setTimeout(function () {
            $('#course_id').trigger('change');
        }, 100)
        $('#course_id').on('change', function () {

            var courseId = $('#course_id').val();
            var studentId = $('#student_id').val();
            $("#student_refund_href").attr('href', site_url + "student-refund-history/" + studentId + "/" + courseId);
            var dataArr = {'courseId': courseId, 'studentId': studentId};

            //$('#arrAgentCommissionList tbody tr').remove();
            //$('#arrAgentBonusDetailList tbody tr').remove();

            noRecordFoundNew('#arrAgentCommissionList tbody', 0, 13);
            noRecordFoundNew('#arrAgentBonusDetailList tbody', 0, 6);

            $.ajax({
                url: site_url + "student-account/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'getAgentCommissionRecord', 'data': dataArr},
                success: function (data) {

                    $('#total_pay_ammount').html(0);
                    $('#total_agent_commission').html(0);
                    $('#total_commission_paid').html(0);

                    var obj = jQuery.parseJSON(data);
                    $('#total_pay_ammount').html('$' + obj.totalPayAmount.toFixed(2));
                    $('#total_agent_commission').html('$' + obj.agentCommissionPayable.toFixed(2));
                    $('#total_commission_paid').html('$' + obj.agentCommissionPaid.toFixed(2));

                    //get Agent Commission List in Table
                    if (obj.arrStudentAgentCommission.length > 0) {
                        var table1 = '';
                        $.each(obj.arrStudentAgentCommission, function (i, item) {

                            var paymentMode = '';
                            if (item.mode_name == '- - Select Commission Period - -') {
                                paymentMode = 'NA';
                            } else {
                                paymentMode = item.mode_name;
                            }

                            var editAction = '-';
                            if (item.is_reversed == 1) {
                                editAction = '';
                            } else {
                                editAction = '<li>' +
                                        '<span data-toggle="modal" class="" data-target="#agentCommissionModel">' +
                                        '<a class="link-black text-sm modify_agent_commission" data-id="' + item.id + '" data-toggle="tooltip" data-original-title="Edit" href="javascript:;"><i class="fa fa-edit"></i></a>' +
                                        '</span>' +
                                        '</li>';
                            }

                            table1 += '<tr id="">' +
                                    '<td>' +
                                    '<div class="action-overlay"><ul class="icon-actions-set">' + editAction + '</ul></div>' +
                                    item.invoice_no +
                                    '</td>' +
                                    '<td>' + item.agency_name + '</td>' +
                                    '<td>' + item.commission_payable + '</td>' +
                                    '<td>' + item.gst_amount + '</td>' +
                                    '<td>' + item.commission_paid + '</td> ' +
                                    '<td>' + paymentMode + '</td>' +
                                    '<td>' + item.CHQ_NO + '</td>' +
                                    '<td>' + item.paid_date + '</td>' +
                                    '<td>' + item.comm_to_refund + '</td>' +
                                    '<td>' + item.GST_to_refund + '</td>' +
                                    '<td>' + item.refund_amount + '</td>' +
                                    '<td>' + (item.remarks == '' || item.remarks == null ? '' : item.remarks) + '</td>' +
                                    '</tr>';
                            //$("#arrAgentCommissionList tbody").append(table);
                            //$('#noRecordAgentList').hide();
                        });
                        $("#arrAgentCommissionList tbody").html(table1);
                    } else {
                        noRecordFoundNew('#arrAgentCommissionList tbody', 0, 13);
                        //$('#noRecordAgentList').show();
                    }

                    //get Agent Bonus List in Table
                    if (obj.arrStudentAgentBonus.length > 0) {
                        var table2 = '';
                        $.each(obj.arrStudentAgentBonus, function (i, bonus) {
                            //var bonusPaidDate = bonus.paid_date.split('-');
                            //var bonusDateFormate = parseInt(bonusPaidDate[2]) + '/'+ parseInt(bonusPaidDate[1]) + '/' + parseInt(bonusPaidDate[0]); 
                            table2 += '<tr id="">' +
                                    '<td>' + bonus.invoice_no + '</td>' +
                                    '<td>' + bonus.agency_name + '</td>' +
                                    '<td>' + (bonus.bonus_type == null ? '' : bonus.bonus_type) + '</td>' +
                                    '<td>' + bonus.paid_date + '</td>' +
                                    '<td>' + bonus.bonus_amount + '</td>' +
                                    '<td>' + bonus.bonus_gst + '</td> ' +
                                    '</tr>';
                            //$('#noRecordBonusList').hide();
                        });
                        $("#arrAgentBonusDetailList tbody").html(table2);
                    } else {
                        //$('#noRecordBonusList').show();
                        noRecordFoundNew('#arrAgentBonusDetailList tbody', 0, 6);
                    }
                },
                error: function (err) {

                }
            });
        });

        $('body').on('click', '.modify_agent_commission', function (e) {
            e.preventDefault();
            var agentCommissionID = $(this).attr('data-id');
            var dataArr = {'agentCommissionID': agentCommissionID};

            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                url: site_url + "student-account/ajaxAction",
                data: {'action': 'modifyAgentCommission', 'data': dataArr},
                success: function (data) {
                    var obj = jQuery.parseJSON(data);

                    var commissionRate = obj.commission_value + '% + ' + obj.commission_gst;

                    $('.agent_commission_id').val(obj.id);
                    $('#transaction_no').text(obj.transaction_no);
                    $('#agent_name').text(obj.agency_name);
                    $('#commission_rate').text(commissionRate);

                    $('#commission_payable').val(obj.commission_payable);
                    $('#gst_amount').text(obj.gst_amount);
                    $('#commission_paid').val(obj.commission_paid);
                    $('#payment_mode').val(obj.mode);
                    $('#invoice_no').val(obj.invoice_no);
                    $('#payment_dt').val(reverseDateV2(obj.paid_date));
                    $('#remarks').val(obj.remarks);

                    $('#bonus_amount').val(obj.bonus_amont);
                    $('#bonus_paid_dt').val(reverseDateV2(obj.paid_date));
                    if (obj.GST === 'GST') {
                        $('#GST').prop("checked", true);
                    } else {
                        $('#NoGST').prop("checked", true);
                    }
                },
                error: function (err) {

                }
            });
        });

        $("#studentAgentCommissionInfo").validate({
            rules: {
                commission_payable: {required: true},
                commission_paid: {required: true},
                payment_mode: {required: true},
                invoice_no: {required: true},
                payment_dt: {required: true},
//                remarks: {required: true}
            },
            errorPlacement: function (error, element) {
            }
        });

        $("#agentBonusInfo").validate({
            rules: {
                bonus_amount: {required: true},
                bonus_paid_dt: {required: true}
            },
            errorPlacement: function (error, element) {
            }
        });
    };

    var studentPayScheduleFee = function () {
        //not use need to remove
    };

    var studentPaymentSummary = function () {
        $('body').on('keyup', '#credit_amount', function () {
            $(".has-error").removeClass('has-error');

            var numberRegex = /^[+-]?\d+(\.\d+)?([eE][+-]?\d+)?$/;
            if (!numberRegex.test($('#credit_amount').val())) {
                $('#credit_amount').closest('.form-group').addClass('has-error');
            }
        });



        $('#submit_invoice_credit').closest('form').on("submit", function (e) {
            e.preventDefault();
            $(".has-error").removeClass('has-error');

            var studentId = $('#hidden_student_id').val();
            var courseId = $('#hidden_course_id').val();
            var paymentDetailId = $('#hidden_initial_payment_detail_id').val();
            var hiddenCreditAmount = $('#hidden_credit_amount').val();

            //get value from form
            var creditAmount = $('#credit_amount').val();
            var creditDate = $('#credit_date').val();
            var remarks = $('#remarks_invoice').val();
            $('#invalid_amount_message').html('');

            var numberRegex = /^[+-]?\d+(\.\d+)?([eE][+-]?\d+)?$/;
            if (!numberRegex.test(creditAmount) || creditAmount < 0 || creditAmount == '') {
                $('#credit_amount').closest('.form-group').addClass('has-error');
                if (creditDate == '') {
                    $('#credit_date').closest('.form-group').addClass('has-error');
                }
                return false;
            }
            if (creditDate == '') {
                $('#credit_date').closest('.form-group').addClass('has-error');
                return false;
            }

            var processingTrasnsaction = new Array();
            $(".trasnsaction_processing_checkbox").each(function () {
                if ($(this).is(":checked")) {
                    var newVal = $(this).val().split('-');
                    //alert(newVal[0]);
                    //alert(newVal[1]);
                    processingTrasnsaction.push({
                        'id': newVal[0],
                        'amount': newVal[1],
                    });
                }
            });

            var formData = {'studentId': studentId, 'courseId': courseId, 'paymentDetailId': paymentDetailId, 'creditAmount': creditAmount, 'creditDate': creditDate, 'remarks': remarks, 'hiddenCreditAmount': hiddenCreditAmount, 'processingTrasnsaction': processingTrasnsaction};
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                url: site_url + "student-account/ajaxAction",
                data: {'action': 'saveInvoiceCreditInfo', 'data': {'formData': formData}},
                success: function (data) {
                    if (data == "invalid") {
                        $('#invalid_amount_message').html('Invalid Amount');
                        return false;
                    }
                    if (data == "valid") {
                        $('#invoiceCredit').modal('hide');
                        sessionDisplayMessage('alert-success', 'Student Invoice Credit Inserted Successfully');
                    }
                }
            });
        });
    };

    return{
        initFilterStudentList: function () {
            studentFilterList();
        },
        initListStudent: function () {
            studentList();
            studentFilterList();
            radioCheckboxClass();
        },
        initStudentPayment: function () {
            studentPayment();
            radioCheckboxClass();
        },
        initInitialPayment: function () {
            initialPayment();
            radioCheckboxClass();
        },
        initStudentRecord: function () {
            studentRecord();
            radioCheckboxClass();
        },
        initStudentScholarship: function () {
            studentScholarship();
            radioCheckboxClass();
        },
        initStudentAgentCommission: function () {
            studentAgentCommission();
            radioCheckboxClass();
        },
        initStudentPayScheduleFee: function () {
            studentPayScheduleFee();
        },
        initStudentPaymentSummary: function () {
            studentPaymentSummary();
        },
        initStudentTransfer: function () {
            studentTransfer();
        }
    };
}();
