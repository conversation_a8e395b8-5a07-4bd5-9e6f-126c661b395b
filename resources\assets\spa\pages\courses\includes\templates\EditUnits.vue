<template>
    <SidebarDrawer
        :visibleDialog="props.visible"
        :hideOnOverlayClick="false"
        :fixedActionBar="isSubjectSelected"
        :width="'60%'"
        @drawerclose="cancelProcess"
        @drawersaved="addSelectedSubjects"
        :wrapperClass="'tw-sticky-footer'"
        :isDisabled="false"
        :isSubmitting="savingUnit"
        :pt="{ content: 'pb-0' }"
    >
        <template #title>
            <div class="text-lg font-medium">{{ modelTitle }}</div>
        </template>
        <template #content>
            <div v-if="!formInitialValues.id">
                <label class="tw-form__label mb-1 font-medium leading-5 text-gray-700" for="active"
                    >Choose an option</label
                >
                <RadioGroup
                    :id="'active'"
                    :name="'active'"
                    :data-items="subjectSoureceItems"
                    v-model="subjectOption"
                    :layout="'horizontal'"
                />
            </div>
            <div id="courses-spa" v-if="subjectOption == 'new'">
                <Form
                    @submit="handleSubmit"
                    :initial-values="formInitialValues"
                    v-if="!props.loading"
                    :key="JSON.stringify(formInitialValues)"
                >
                    <FormContent
                        :saving="savingUnit"
                        :willclose="savingUnit && !preventWindowClose"
                        :syncing="syncingUnit"
                        :modelTitle="modelTitle"
                        :subjects="props.courseSubjects"
                        @cancel="cancelProcess"
                        @sync="syncUnitInformation"
                        @preventclose="preventDrawerClose"
                        :course="props.course"
                        :custom="props.custom"
                        :multiplebuttons="!props.temporary && formInitialValues.id > 0"
                        :nominalhours="nominalHoursData"
                    />
                </Form>
            </div>
            <div v-else>
                <div class="space-y-4">
                    <div class="coursesSearchInputField relative w-full">
                        <span class="absolute left-3 top-1/2 -translate-y-1/2">
                            <icon :name="iconName" width="16" height="16" />
                        </span>
                        <input
                            type="text"
                            id="search_criteria"
                            ref="searchKeyword"
                            v-model.lazy="search_criteria"
                            v-debounce="300"
                            class="tw-input-text h-[2.32rem] pl-8"
                            placeholder="Start Searching"
                            autocomplete="off"
                        />
                    </div>
                    <GlobalContextLoader
                        :context="'searching-subjects'"
                        :type="'skeleton-list'"
                        :size="'single'"
                    >
                        <div
                            v-if="foundSubjects.length > 0"
                            v-for="subject in foundSubjects"
                            class="flex items-center justify-between space-x-2 border-b py-4 text-sm"
                        >
                            <div>
                                <Checkbox
                                    v-model="subject.selected"
                                    :id="`subject-${subject.id}`"
                                />
                            </div>
                            <div>
                                <DropDownList
                                    :data-items="unitTypesData"
                                    v-model="subject.subject_type"
                                    :data-item-key="'value'"
                                    :text-field="'label'"
                                    :value-field="'value'"
                                    :value-primitive="true"
                                />
                            </div>
                            <label
                                class="flex flex-grow cursor-pointer items-center space-x-2"
                                :for="`subject-${subject.id}`"
                            >
                                <div class="text-gray-700" :title="subject.subject_name">
                                    <div>
                                        {{ subject.subject_code }} -
                                        {{ trim(subject.subject_name, 50, '...') }}
                                    </div>
                                    <div
                                        class="text-xs text-gray-500"
                                        v-if="!isCourseHigherEd(course.course_type_id)"
                                    >
                                        has {{ subject.subject_units_count }}
                                        {{ subject.subject_units_count > 1 ? 'units' : 'unit' }}
                                    </div>
                                </div>
                            </label>
                        </div>
                        <div v-else>
                            <div class="text-center text-sm text-gray-500">No subjects found</div>
                        </div>
                    </GlobalContextLoader>
                </div>
            </div>
        </template>
    </SidebarDrawer>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import axios from 'axios';
import { Form } from '@progress/kendo-vue-form';
import FormContent from '@spa/pages/courses/includes/forms/UnitEditFormContent.vue';
import { useCoursesStore } from '@spa/stores/modules/courses';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import ApplicationNotification from '@spa/components/ApplicationNotification.vue';
import globalHelper from '@spa/plugins/global-helper';
import { Input, Checkbox, RadioGroup } from '@progress/kendo-vue-inputs';
import { useLoaderStore } from '@spa/stores/modules/global-loader';

const savingUnit = ref(false);
const syncingUnit = ref(false);
const preventWindowClose = ref(false);

const subjectOption = ref('new');
const iconName = ref('lens');
const search_criteria = ref('');
const foundSubjects = ref([]);
const savingSubject = ref(false);

const cancelSource = axios.CancelToken.source();
const props = defineProps({
    courseSubjects: { default: [] },
    course: { default: [] },
    custom: { type: Boolean, default: false },
    unit: { default: [] },
    visible: { default: false },
    loading: { default: false },
    template: { type: [Array, Object], default: [] },
    nominalhours: { type: Object, default: {} },
    temporary: { type: Boolean, default: false },
});

const emit = defineEmits(['closed']);

const store = useCoursesStore();
const globalLoaderStore = useLoaderStore();

const modelTitle = computed(() => {
    const unitTitle = props.unit?.id > 0 ? `Edit unit: ${props.unit?.unit_name}` : 'Add new unit';
    return savingUnit.value ? 'Saving unit information...' : unitTitle;
});
const formInitialValues = ref(props.unit); // Use a ref to hold initial form values
const nominalHoursData = ref(props.nominalhours); // Use a ref to hold initial form values
const ignoreModified = computed(() => {
    return store.operationMode === 'update';
});
const subjectSoureceItems = [
    { label: 'New ' + (props.course.course_type_id == 17 ? 'Subject' : 'Unit'), value: 'new' },
    {
        label: 'Existing ' + (props.course.course_type_id == 17 ? 'Subject' : 'Unit'),
        value: 'existing',
    },
];
const cancelProcess = () => {
    cancelSource.cancel('Request cancel');
    emit('closed');
};
const syncUnitInformation = (code) => {
    if (syncingUnit.value) {
        return;
    }
    const courseCode = props.course?.course_code || '';
    syncingUnit.value = true;
    $http
        .get(route('spa.courses.syncunitdata', [courseCode, code]))
        .then((resp) => {
            if (resp['success']) {
                formInitialValues.value = resp.unit;
                nominalHoursData.value = {};
            }
            syncingUnit.value = false;
        })
        .catch((error) => {
            syncingUnit.value = false;
        });
    return false;
};
const handleSubmit = (formData) => {
    if (props.temporary) {
        formData['temporary'] = true;
        emit('saved', formData);
        return;
    }
    if (savingUnit.value == true) {
        return;
    }
    savingUnit.value = true;
    const actionRouteName = props.custom ? 'spa.courses.addunit' : 'spa.courses.saveunit';
    let sendRequest = null;
    /*
    Append the current template data to the form data
    */
    formData['template'] = props.template || [];
    if (props.custom) {
        sendRequest = $http.post(route(actionRouteName), formData, {
            cancelToken: cancelSource.token,
        });
    } else {
        sendRequest = $http.put(route(actionRouteName), formData, {
            cancelToken: cancelSource.token,
        });
    }
    sendRequest
        .then((resp) => {
            if (resp['success']) {
                globalHelper.methods.showPopupSuccess(resp.message, 'Success');
                store.setCourseProgress(resp.progress);
                store.setUnits(resp.units);
                store.setCourseSubjects(resp.subjects);
                if (preventWindowClose.value != true) {
                    emit('closed');
                }
            }
            savingUnit.value = false;
            preventWindowClose.value = false;
        })
        .catch((error) => {
            savingUnit.value = false;
            preventWindowClose.value = false;
        });
    return false;
};
const preventDrawerClose = () => {
    preventWindowClose.value = formInitialValues.value.id > 0;
};

const isSubjectSelected = computed(() => {
    if (
        subjectOption.value == 'existing' &&
        foundSubjects.value.find((subject) => subject.selected)
    ) {
        return true;
    }
    return false;
});
const addSelectedSubjects = () => {
    savingSubject.value = true;
    $http
        .post(
            route('spa.courses.savecoursesubjects'),
            {
                course_id: store.course.id,
                template_id: store.currentTemplate.id || null,
                subjects: foundSubjects.value.filter((subject) => subject.selected),
            },
            {
                cancelToken: cancelSource.token,
            }
        )
        .then((resp) => {
            if (resp['success']) {
                store.setCourseProgress(resp.progress);
                store.setUnits(resp.units);
                store.setCourseSubjects(resp.subjects);
            }
            emit('closed');
        })
        .catch((error) => {
            savingSubject.value = true;
        })
        .finally(() => {
            savingSubject.value = false;
            emit('closed');
        });
};
watch(search_criteria, (newVal) => {
    globalLoaderStore.startContextLoading('searching-subjects');
    iconName.value = 'loading';
    foundSubjects.value = [];
    $http
        .post(
            route('spa.courses.searchsubjects'),
            {
                search_criteria: newVal,
                course_id: store.course.id,
                course_type: store.course.course_type_id,
            },
            {
                cancelToken: cancelSource.token,
            }
        )
        .then((resp) => {
            if (resp['success']) {
                foundSubjects.value = resp.subjects;
            }
        })
        .catch((error) => {
            globalLoaderStore.stopContextLoading('searching-subjects');
            iconName.value = 'lens';
        })
        .finally(() => {
            globalLoaderStore.stopContextLoading('searching-subjects');
            iconName.value = 'lens';
        });
    return false;
});
</script>
