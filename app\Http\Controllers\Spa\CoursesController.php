<?php

namespace App\Http\Controllers\Spa;

use App\Classes\CoursesApiController;
use App\Classes\SiteConstants;
use App\DTO\Courses\IntakesFilterDTO;
use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Http\Requests\AssignUnitsToSubjectRequest;
use App\Http\Requests\CompetencyCriteriaRequest;
use App\Http\Requests\CourseFilterRequest;
use App\Http\Requests\CourseQuickAddRequest;
use App\Http\Requests\IntakesFilterRequest;
use App\Http\Requests\ShortCourseIntakeRequest;
use App\Http\Requests\StoreAvetmissRequest;
use App\Http\Requests\StoreCourseGeneralRequest;
use App\Http\Requests\StoreCourseSubjectRequest;
use App\Http\Requests\StoreFacultyRequest;
use App\Http\Requests\StoreHigeredInfoRequest;
use App\Http\Requests\StoreHoursFeesRequest;
use App\Http\Requests\StoreUnitRequest;
use App\Http\Resources\CourseIntakeDetailResource;
use App\Http\Resources\CourseIntakesSummaryResource;
use App\Http\Resources\CourseShortCourseIntakesResource;
use App\Http\Resources\CourseStructureResource;
use App\Http\Resources\CourseSummaryResource;
use App\Http\Resources\CourseTemplatesResource;
use App\Http\Resources\HigheredCourseResource;
use App\Http\Resources\SubjectUnitsResource;
use App\Model\v2\AgreedNominalHours;
use App\Model\v2\BroadType;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSubjects;
use App\Model\v2\CourseTemplate;
use App\Model\v2\CourseTemplateStructure;
use App\Model\v2\CourseType;
use App\Model\v2\ElementOfUnitCompetency;
use App\Model\v2\Faculties;
use App\Model\v2\GradingType;
use App\Model\v2\HigheredCourseDetail;
use App\Model\v2\Student;
use App\Model\v2\Subject;
use App\Model\v2\SubjectUnits;
use App\Model\v2\Timetable;
use App\Model\v2\UnitModule;
use App\Services\CourseSetupService;
use App\Services\CourseUnitsService;
use App\Services\SyncUnitsSetupService;
use App\Traits\CommonTrait;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Jobs\SyncAllCoursesFromMoodle;
use Domains\Moodle\Jobs\SyncCourseToMoodle;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Support\Services\UploadService;

class CoursesController
{
    use CommonTrait;
    use MoodleStatusTrait;

    public $service;

    public $unitsservice;

    private $templatesLists = null;

    public function __construct(
        CourseSetupService $service,
        CourseUnitsService $unitsservice,
    ) {
        $this->service = $service;
        $this->unitsservice = $unitsservice;
    }

    public function index(CourseFilterRequest $request)
    {
        // UnitModule::CleanDuplicateCourseUnits();
        // return;
        $college_id = Auth::user()->college_id;
        /* search for the courses on the basis of params */
        /* prepare the data to render */
        $isMoodleConnect = $this->isMoodleConnected();
        $facultiesData = Courses::getFaculties($college_id, false)->toArray();
        $facultiesData = array_map(function ($item) {
            $item['id'] = (string) $item['id'];

            return $item;
        }, $facultiesData);
        $pageData = [
            'tenant_id' => tenancy('id'),
            'isMoodleConnect' => $isMoodleConnect,
            /* format the course collection */
            'courses' => CourseSummaryResource::collection(Courses::SearchCoursesList($request->DTO())),
            /* Send the filter parameters to the renderer to render the course filter inputs */
            'filterScopes' => [
                'types' => $this->service->getCourseTypes($college_id), // types of courses
                'faculties' => $facultiesData, // faculties here
                'status' => config('constants.courseStatusFilter'), // course status filters
                'duration' => config('constants.courseDurationFilter'), // course durations filters
            ],
            'data' => [
                'mainmenu' => 'settings',
            ],
        ];

        // dd($pageData['courses'][0]['moodleItem']);
        /* render the results */
        return Inertia::render('courses/List', $pageData);
    }

    public function forceSyncLegacyData(Request $request)
    {
        $courseId = $request->input('course');
        $course = ($courseId) ? Courses::find($courseId) : null;
        if ($course) {
            SyncUnitsSetupService::SyncUnitsFromOldSetupToNew($course, true);
        }

        return response()->json(['message' => 'Synced successfully']);
    }

    public function searchUnits(Request $request)
    {
        // function not required for now
    }

    private function searchCourses($searchTerm = '', $page = 1, $show = 10)
    {
        if (empty($searchTerm)) {
            return [];
        }
        $page = $page > 0 ? $page : 1;
        $show = $show > 0 ? $show : 10;
        $college_id = Auth::user()->college_id;
        $coursesApi = new CoursesApiController;
        $resArray = [];
        $foundCourses = $coursesApi->searchForCourses($searchTerm, $page, $show);
        $coursesCodes = ($foundCourses) ? Arr::pluck($foundCourses['Results'], ['Code']) : [];
        $existingCourses = Courses::where('college_id', $college_id)
            ->whereIn('course_code', $coursesCodes)
            ->withTrashed()->get();
        $existingCourseCodes = $deletedCoursesCodes = [];
        $existingCourses->each(function ($course) use (&$existingCourseCodes, &$deletedCoursesCodes) {
            $existingCourseCodes[] = $course->course_code;
            if ($course->trashed()) {
                $deletedCoursesCodes[] = $course->course_code;
            }
        });
        $existingCourseCodes = Arr::pluck($existingCourses, 'course_code');
        $courseTypes = $this->service->getCourseTypes($college_id)->toArray();
        $courseTypes = Arr::pluck($courseTypes, 'label', 'value');
        /* check for the courses if they are already taken */
        foreach ($foundCourses['Results'] as &$val) {
            $val['CourseType'] = 0;
            if (in_array($val['Code'], $existingCourseCodes)) {
                $course = $existingCourses->where('course_code', $val['Code'])->first();
                $val['Added'] = true;
                $val['Deleted'] = in_array($val['Code'], $deletedCoursesCodes);
                $val['Complete'] = $course->isCourseComplete();
                $courseTypeId = $course->course_type_id ?? 0;
                $val['CourseType'] = $courseTypes[$courseTypeId] ?? '';
            } else {
                // component TrainingPackage
                // qualification= course,
                // Unit of competency= Shor course,
                // Skill Set is group of Unit of competency and considered a course,
                // Accredited course/unit  is qualification/Unit of competency
            }
        }

        return $foundCourses;
    }

    public function add(Request $request)
    {
        $college_id = Auth::user()->college_id;
        $searchTerm = strtolower(trim($request->input('search')));
        $page = (int) $request->input('page');
        $show = (int) $request->input('show');
        $courseCode = strtolower(trim($request->input('course')));
        $templateData = CourseTemplate::getTemplateDataByCode($courseCode, null);
        if (! $templateData) {
            $templateData = new CourseTemplate;
        }

        return Inertia::render('courses/Add', [
            'tenant_id' => tenancy('id'),
            'courseTypes' => fn () => $this->service->getCourseTypes($college_id),
            'filters' => ['search' => $searchTerm, 'page' => $page, 'show' => $show, 'course' => $courseCode],
            'courses' => fn () => $this->searchCourses($searchTerm, $page, $show), // found courses list
            /*
            for particular course detail;
            $courseCode should have some value
            used to quick add course
            */
            'course' => fn () => $this->service->getCourseUnitDetails($templateData, $courseCode), // particular course detail
        ])->with('message', []);
    }

    public function quickadd(CourseQuickAddRequest $request)
    {
        // course details
        $searchTerm = $request->input('searchText');
        $page = (int) $request->input('page');
        $take = (int) $request->input('take');
        $searchTerm = $request->input('searchText');
        $existingCourse = $customCourse = null;
        DB::beginTransaction();
        try {
            $validatedData = $request->validated();
            $customCourse = $validatedData['custom'] ?? false;
            $saveData = $validatedData['course'] ?? [];
            $unitsData = $validatedData['course']['units'] ?? [];
            $courseType = $validatedData['type'] ?? 0;
            $vetSynced = $validatedData['is_vet_synced'] ?? 0;
            $custom = $validatedData['custom'] ?? true;
            $courseCode = $saveData['Code'] ?? '';
            $saveData['course_code'] = $courseCode;
            $saveData['national_code'] = (! $customCourse) ? $courseCode : '';
            $saveData['course_name'] = $saveData['Title'] ?? '';
            $saveData['course_type_id'] = $courseType;
            $saveData['ANZSCO_code'] = $saveData['ANZSCO_code'] ?? '';
            Arr::forget($saveData, 'Code');
            Arr::forget($saveData, 'Title');
            Arr::forget($saveData, 'CourseTypeId');
            Arr::forget($saveData, 'CourseType');
            Arr::forget($saveData, 'units');
            $coreUnits = $unitsData['core'] ?? [];
            $electiveUnits = $unitsData['elective'] ?? [];
            $electiveUnitsAdded = array_filter($electiveUnits, function ($item) {
                return isset($item['Added']) && $item['Added'] === true;
            });
            /* set the primary quota to be total number of units availalbe */
            $saveData['core_units_number'] = count($coreUnits) ?? 1; // preassumed that a unit must have at least one core unit
            $saveData['elective_units_number'] = count($electiveUnitsAdded) ?? 0;
            // $allUnits = [...$coreUnits, ...$electiveUnits];

            $allUnits = array_merge($coreUnits, $electiveUnits);
            // dd($allUnits);
            if ($courseType == SiteConstants::SHORT_COURSE_TYPE_ID && empty($allUnits)) {
                /*
                if the course is a short course, no extra units will be added from the UI,
                a default unit and subject will be added automatically with the same course code
                */
                $allUnits = [];
                $saveData['core_units_number'] = 1;
            }
            $hasUnits = count($allUnits) > 0;
            $existingCourse = Courses::QuickAddCourse($saveData);
            if ($existingCourse->course_type_id == SiteConstants::SHORT_COURSE_TYPE_ID && ! $hasUnits) {
                $allUnits[] = $existingCourse->setUpSubjectUnitForShortCourse();
            }
            // dd($allUnits);
            if ($existingCourse && $existingCourse->id && ! empty($allUnits)) {
                // save the units
                $unitData = $this->unitsservice->saveUnits($allUnits, $existingCourse);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
        // redirect to the previous page search list page
        $addLink = route('spa.courses.detailadd', ['course' => $existingCourse->course_code]);

        $params = ['search' => $searchTerm];
        if ($page > 0) {
            $params['page'] = $page;
        }
        if ($take > 0) {
            $params['show'] = $take;
        }

        $backLink = route('spa.courses.add', $params);

        return redirect()->route('spa.courses.detailadd', ['course' => $existingCourse->course_code])->with('message', [
            'type' => 'success',
            'title' => $existingCourse->course_code.' - '.$existingCourse->course_name.' Added Successfully.',
            'subtitle' => 'Continue adding other details to the course.',
            'dismisstext' => 'Dismiss',
            'backlink' => $backLink,
        ]);
        /*
        if ($customCourse) {
            return redirect()->route('spa.courses.detailadd', ['course' => $existingCourse->course_code])->with('message', array(
                "type" => 'success',
                "title" => $existingCourse->course_code . ' - ' . $existingCourse->course_name . ' Added Successfully.',
                "subtitle" => "Continue adding other details to the course.",
                "dismisstext" => "Dismiss",
            ));
        }

        return redirect()->route('spa.courses.add', $params)->with('message', array(
            "type" => 'success',
            "title" => $existingCourse->course_code . ' - ' . $existingCourse->course_name . ' Added Successfully.',
            "subtitle" => "You need to fill more details before you can activate this course.",
            "followlink" => $addLink,
            "followtext" => "Fill More Details",
            "dismisstext" => "Dismiss",
        ));
        */
    }

    public function types()
    {

        return Inertia::render('courses/Types', [
            'tenant_id' => tenancy('id'),
            'types' => CourseType::all(),

        ]);
    }

    public function templates()
    {

        return Inertia::render('courses/templates/Index', [
            'tenant_id' => tenancy('id'),
            'templates' => fn () => CourseTemplate::filter(request()->all())->paginate(5),

        ]);
    }

    private function courseFullAddPrepare($courseCode = '', $requestedFrom = 'profile')
    {
        $college_id = Auth::user()->college_id;
        // load all the form build data to add course
        // courses prerequisites
        $arrQualifictionLevelList = config('constants.arrQualifictionLevelList');
        // courses prerequisites can be any of the existing courses in the particular college
        $arrCourses = Courses::getCoursesList($college_id, true);
        // format the list so that it is indexed with the course code
        $arrCourses = Arr::pluck($arrCourses, 'title', 'code');
        // combine the config and existing courses
        // $prerequisites = [...$arrQualifictionLevelList, ...$arrCourses];
        $prerequisites = array_merge($arrQualifictionLevelList, $arrCourses);

        // get the form ints values from the tables
        $arrDepartments = Courses::getDepartments($college_id);
        $arrFaculties = Courses::getFaculties($college_id, false);
        // get the form init constants
        $arrResultsCalculationMethod = config('constants.arrResultsCalculationMethod');
        Arr::forget($arrResultsCalculationMethod, '');
        $gradingTypes = GradingType::with(['resultgrade' => function ($query) {
            $query->select('id', 'grading_type', 'use_marks');
        }])
            ->where('college_id', $college_id)
            ->select('id', 'grading_name')
            ->get()
            ->map(function ($gradingType) {
                return [
                    'id' => $gradingType->id,
                    'text' => $gradingType->grading_name,
                    'marks' => optional($gradingType->resultgrade)->use_marks,
                ];
            });
        $targets = config('constants.arrDeliveryTarget');
        Arr::forget($targets, '');
        $arrCourseDurationType = config('constants.arrCourseDurationType');
        Arr::forget($arrCourseDurationType, '');
        $studyHelpTypes = config('constants.freeHelpStudyType');
        Arr::forget($studyHelpTypes, '');
        $faculty = Faculties::select('id', 'name')->where(['college_id' => $college_id, 'status' => 1])->get();
        $disciplineData = BroadType::with([
            'narrowtypes' => function ($query) {
                $query->select('id', 'broad_id', 'narrow_key', 'title');
            },
            'narrowtypes.subnarrowtypes' => function ($query) {
                $query->select('id', 'broad_id', 'narrow_id', 'sub_narrow_key', 'title');
            },
        ])
            ->where('college_id', $college_id)
            ->select('id', 'broad_key', 'title')
            ->get();
        /* format the data for kendo dropdowns or options inputs */
        $formSeeders = [
            'course_types' => $this->service->getCourseTypes($college_id),
            'campuses' => Courses::getCampusList($college_id, true),
            'work_placement_component_types' => kendify(config('constants.arrWorkPlacementComponentTypes')),
            'placement_officers' => Courses::getStaffAndTeacher($college_id, true),
            'delivery_targets' => kendify($targets),
            'prerequisites' => kendify($prerequisites),
            'result_calculation_methods' => kendify($arrResultsCalculationMethod, 'value', 'label'),
            'course_recognitions' => kendify(Courses::getCourseRecognition()),
            'education_level' => Courses::getLevelEducation(),
            'education_field' => Courses::getFieldOfEducation(true),
            'anzsco_codes' => kendify(Courses::getANZSCOCodesList()),
            'faculties' => $arrFaculties,
            'departments' => $arrDepartments,
            'course_delivery_types' => kendify(config('constants.arrCourseDeliveryTypeNew')),
            'fee_help_study_types' => kendify($studyHelpTypes),
            'course_completion_types' => kendify(config('constants.arrCourseCompletionType')),
            'course_duration_types' => kendify($arrCourseDurationType),
            'grading_types' => $gradingTypes,
            'discipline_data' => $disciplineData,
            'course_fee_types' => kendify(config('constants.arrCourseFeeType')),
            'study_types' => kendify(config('constants.arrStudyType')),
            'type_of_operation' => kendify(config('constants.arrTypeOfOperation')),
            'mod_offshore_courses' => kendify(config('constants.arrPrincipalModeOfDeliveryOffshoreCourse')),
            'offshore_delivery_indicator' => kendify(config('constants.arrOffshoreDeliveryIndicator')),
            'additional_criteria' => kendify(config('constants.arrAdditionalEntranceCriteria')),
            'special_course_type' => kendify(config('constants.arrSpecialCourseType')),
            'combined_course' => kendify(config('constants.arrCombinedCourseOfStudy')),
            'course_recurring_types' => kendify(config('constants.arrCourseRecurringTypes')),
        ];

        $template = $this->loadCourseTemplates($courseCode, $requestedFrom);

        // $units = $this->service->getCourseUnitDetails($defaultTemplate);
        // dd($units);
        return [
            'tenant_id' => tenancy('id'),
            'course' => fn () => $this->service->getCourseEditDetails($courseCode),
            'units' => fn () => $this->service->getCourseUnitDetails($template['default'] ?? []),
            'templates' => fn () => CourseTemplatesResource::collection($template['list'] ?? []),
            'formSeeds' => $formSeeders,
        ];
    }

    public function loadCourseTemplates($courseCode = '', $requestedFrom = '')
    {
        if (! $this->templatesLists) {
            $this->templatesLists = CourseTemplate::getTemplatesList($courseCode);
        }
        //
        $defaultTemplate = collect($this->templatesLists)->where('set_default', 1)->first();
        /* if not default template is found, then pick the first template */
        if (! $defaultTemplate) {
            $defaultTemplate = $this->templatesLists[0] ?? null;
        }

        /*
        if requestedFrom is from details
        if there are templates created and the picked default was the master template,
        pick the first template in the list (Treating first template as the default)
        This logic is not implemented any more
        */
        /*
        This logic is not implemented any more because all the templates will be created even the major,
        so first template will be the default one
        the query will sort the templates to first if any template set to default
        $isdefaultMaster = $defaultTemplate->is_master_template ?? 0;
        $areTemplatesCreated = count($this->templatesLists) > 1 ? 1 : 0;
        $requestedFrom = "details";
        if($requestedFrom == "details" && $isdefaultMaster && $areTemplatesCreated){
            $defaultTemplate = array_slice($this->templatesLists, 1, 1);
            if($defaultTemplate){
                $defaultTemplate = reset($defaultTemplate);
            }
        }
        */
        return ['default' => $defaultTemplate, 'list' => $this->templatesLists];
    }

    public function loadTemplateUnits($course = '', $templateId = null)
    {
        try {
            $templateData = CourseTemplate::getTemplateData($course, $templateId);
            if (empty($templateData)) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }
            $units = ($templateData) ? $this->service->getCourseUnitDetails($templateData) : [];

            return ajaxSuccess($units, '');
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), $e->getCode() ?: 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function addDetails($courseCode = '')
    {
        $college_id = Auth::user()->college_id;
        $courseData = Courses::Where(['college_id' => $college_id, 'course_code' => $courseCode])->first();
        if (! $courseData) {
            return redirect()->route('spa.courses.index')->with('message', [
                'type' => 'error',
                'title' => 'Course Not Availalbe.',
                'subtitle' => 'Course you are trying to edit is not availalbe.',
                'followlink' => null,
                'followtext' => null,
                'dismisstext' => 'Dismiss',
            ]);
        }
        $fullAddData = $this->courseFullAddPrepare($courseCode, 'details');

        return Inertia::render('courses/FullAdd', $fullAddData);
    }

    public function courseProfile($courseCode = '')
    {
        $college_id = Auth::user()->college_id;
        // before callinng course data
        // check if the course is added
        $courseData = Courses::Where(['college_id' => $college_id, 'course_code' => $courseCode])->first();
        if (! $courseData) {
            return redirect()->route('spa.courses.index')->with('message', [
                'type' => 'error',
                'title' => 'Course Not Availalbe.',
                'subtitle' => 'Profile of the course doesnot exist.',
            ]);
        }
        $fullAddData = $this->courseFullAddPrepare($courseCode, 'profile');
        // get the enrollments data
        // disable enrollments as it is not being used currently [7th May 2024]
        // $enrollments = $this->getEnrollments($courseData->id);
        $enrollments = [];
        $structure = ['course_structure' => $this->getCourseStructure($courseCode)];
        $outcomeData = config('constants.arrSelectFinalOutcome');
        Arr::forget($outcomeData, '');
        $outcomeData = kendify($outcomeData);
        $fullAddData['formSeeds']['outcomes'] = $outcomeData;
        $activeMenu = ['data' => [
            'mainmenu' => 'settings',
        ]];

        $fullAddData = [...$fullAddData, ...$enrollments, ...$structure, ...$activeMenu];

        return Inertia::render('courses/CourseProfile', $fullAddData);
    }

    public function saveRequests($validatedData, $createNew = false)
    {
        $college_id = Auth::user()->college_id;
        DB::beginTransaction();
        try {
            $courseData = Courses::where(['id' => $validatedData['id'], 'college_id' => $college_id])->first();
            $activateCourse = $validatedData['activate_course'] ?? 0;
            if ($createNew && ! $courseData) {
                // create a new course data
                $validatedData['college_id'] = $college_id;
                $courseData = new Courses;
                $courseData->college_id = $college_id;
                $courseData->save();
                Arr::forget($validatedData, 'id');
                Arr::forget($validatedData, 'college_id');
            }
            if (! $courseData) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }
            if (isset($validatedData['new_cover_image'])) {
                if ($validatedData['new_cover_image'] && ! empty($courseData->cover_image)) {
                    $filePath = config('constants.uploadFilePath.CourseCoverImages');
                    $filePath = Helpers::changeRootPath($filePath);
                    $this->deleteThumbImages($filePath['default'], $courseData->cover_image, true);
                }
                if ($validatedData['new_cover_image']) {
                    $validatedData['cover_image'] = $validatedData['new_cover_image'];
                }
            }
            // if course packaging data has not been saved; make it default to 1
            // to remove deadlock condition in old courses while adding details of the course
            $courseData->update($validatedData);
            if ($activateCourse) {
                $activated = $courseData->updateStatus($activateCourse);
                $courseData->activated = ($activated !== true) ? $activated : ($activateCourse ? 'activated' : 'deactivated');
            }
            DB::commit();

            return $courseData;
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function saveCourseGeneralDetails(StoreCourseGeneralRequest $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;
            $validatedData = $request->validated();
            $filePath = config('constants.uploadFilePath.CourseCoverImages');
            $coverImageUploaded = null;
            if ($request->hasFile('cover_photo')) {
                $file = $request->file('cover_photo');
                $originalName = $file->getClientOriginalName();
                $fileName = hashFileName($originalName);
                $fileName = preg_replace('/[^A-Za-z0-9.\-]/', '', str_replace(' ', '-', $fileName));

                $destinationPath = Helpers::changeRootPath($filePath);

                // Skip thumbnail generation for SVG files
                if ($file->getClientOriginalExtension() !== 'svg') {
                    $this->generateThumbImageS3($file, $destinationPath, $fileName);
                }

                // $upload_success = $file->move($destinationPath['default'], $fileName);
                $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $fileName);
                if ($upload_success) {
                    $coverImageUploaded = $fileName;
                }
            }
            $validatedData['new_cover_image'] = $coverImageUploaded;

            $savedData = $this->saveRequests($validatedData, true);

            if ($savedData) {
                DB::commit();

                return $this->service->prepareResponseMessage($savedData, 'general');
            }
            throw new \Exception(config_locale('messages.courses.general_detail_fail'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function syncCourseInformation($code = '')
    {
        $coursesApi = new CoursesApiController;
        $data = $coursesApi->getCourseSyncData($code);
        $synced = $data['national_code'] ?? false;
        $msg = config_locale('messages.courses.course_not_in_tga');
        if ($synced) {
            $msg = 'Course synced';

            return ajaxSuccess(['course' => $data], $msg);
        }

        return ajaxError($msg);
    }

    public function saveHoursFeesDetails(StoreHoursFeesRequest $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;
            $validatedData = $request->validated();
            $savedData = $this->saveRequests($validatedData);
            if ($savedData) {
                DB::commit();

                return $this->service->prepareResponseMessage($savedData, 'hourfees');
            }
            throw new \Exception(config_locale('messages.courses.hourfees_detail_fail'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function saveFacultyDetails(StoreFacultyRequest $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;
            $validatedData = $request->validated();
            $savedData = $this->saveRequests($validatedData);
            if ($savedData) {
                $this->service->assignCourseToCampuses($validatedData);
                DB::commit();

                return $this->service->prepareResponseMessage($savedData, 'faculty');
            }
            throw new \Exception(config_locale('messages.courses.faculty_detail_fail'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function updatePackage(Request $request)
    {
        try {
            $college_id = Auth::user()->college_id;
            $id = (int) $request->input('id') ?? 0;
            $core = (int) $request->input('core') ?? 0;
            $elective = (int) $request->input('elective') ?? 0;
            $core = $core < 0 ? 0 : $core;
            $elective = $elective < 0 ? 0 : $elective;
            // get the course data and find already added units;
            // do not let package to be uncompatible to already added units

            // if ($this->service->hasNewCourseSetup()) {
            //     $course = Courses::with('units_normalized')->find($id);
            //     $addedUnits = $course->units_normalized ?? [];
            // } else {
            //     $course = Courses::with('courseunits')->find($id);
            //     $addedUnits = $course->courseunits ?? [];
            // }

            $course = Courses::with('units_normalized')->find($id);
            $addedUnits = $course->units_normalized ?? [];

            $templateData = CourseTemplate::getTemplateData($course, 0);

            // $addedCore = $addedUnits->where('unit_type', SiteConstants::CORETEXT)->where('status', 1)->count();
            // $addedElective = $addedUnits->where('unit_type', SiteConstants::ELECTIVETEXT)->where('status', 1)->count();

            $addedCore = $templateData->templateUnits()->where([
                'unit_type' => SiteConstants::CORETEXT,
                'is_active' => 1,
            ])->count();
            $addedElective = $templateData->templateUnits()->where([
                'unit_type' => SiteConstants::ELECTIVETEXT,
                'is_active' => 1,
            ])->count();

            $warning = false;
            $msg = null;
            if ($core < $addedCore || $elective < $addedElective) {
                $msg = [];
                if ($core < $addedCore) {
                    $msg[] = config_locale('messages.courses.hascoreunitsadded');
                }
                if ($elective < $addedElective) {
                    $msg[] = config_locale('messages.courses.haselectiveunitsadded');
                }
                $msg = str_replace(['{{corecount}}', '{{electivecount}}'], [$addedCore, $addedElective], implode(' ', $msg));
                $core = $addedCore;
                $elective = $addedElective;
                $warning = true;
            }
            $saveData = ['id' => $id, 'college_id' => $college_id, 'core_units_number' => $core, 'elective_units_number' => $elective];
            $savedData = $this->saveRequests($saveData);
            if ($savedData) {
                $templateData->update(['no_of_core_subject' => $core, 'no_of_elective_subject' => $elective]);
                $progress = $this->service->getProgressStatus($savedData);

                $data['units'] = $this->service->getCourseUnitDetails($templateData);
                $data['template'] = new CourseTemplatesResource($templateData);
                $data['progress'] = $progress;
                $data['packaging'] = ['core' => $core, 'elective' => $elective];

                return ajaxSuccess($data, null);
            }
            throw new \Exception(config_locale('messages.courses.packaging_notsaved'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function saveAvetmissDetails(StoreAvetmissRequest $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;
            $validatedData = $request->validated();
            $savedData = $this->saveRequests($validatedData);
            if ($savedData) {
                DB::commit();

                return $this->service->prepareResponseMessage($savedData, 'avetmiss');
            }
            throw new \Exception(config_locale('messages.courses.avetmiss_notsaved'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function saveHigheredInfo(StoreHigeredInfoRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $higheredInfo = HigheredCourseDetail::where(['college_id' => $validatedData['college_id'], 'course_id' => $validatedData['course_id']])->first();
            if (! $higheredInfo) {
                $higheredInfo = HigheredCourseDetail::create($validatedData);
            } else {
                $higheredInfo->update($validatedData);
            }
            if (! $higheredInfo) {
                throw new \Exception(config_locale('messages.courses.higheredinfo_notsaved'));
            }
            $higheredInfo = new HigheredCourseResource($higheredInfo);
            $course = Courses::find($validatedData['course_id']);
            // need to activate the course if activate request received
            $activateCourse = $validatedData['activate_course'] ?? 0;
            if ($activateCourse) {
                $activated = $course->updateStatus($activateCourse);
                $course->activated = ($activated !== true) ? $activated : ($activateCourse ? 'activated' : 'deactivated');
            }
            $progress = $this->service->getProgressStatus($course);
            $message = $this->service->prepareResponseMessage($course, 'highered');

            return ajaxSuccess(['highered' => $higheredInfo, 'progress' => $progress], $message);
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    /*
    this method is to add units to the vet courses
    */
    public function addUnits(Request $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;

            $templateData = $request->input('template');
            $templateId = $templateData['id'] ?? null;
            $templateCourseId = $templateData['course_id'] ?? null;

            $unitData = $request->input('units');

            $courseCode = $unitData['course_code'] ?? null;
            $courseId = (int) $unitData['course_id'] ?? null;

            $coreUnits = $unitData['core'] ?? [];
            $electiveUnits = $unitData['elective'] ?? [];

            $allUnits = array_merge($coreUnits, $electiveUnits);
            // get the course code if exists

            $fld = ($courseId) ? 'id' : 'course_code';

            $checkVal = ($courseId) ? $courseId : $courseCode;
            $filterArray = ['college_id' => $college_id, $fld => $checkVal];

            $existingCourse = Courses::Where($filterArray)->first();

            if (! $existingCourse) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }
            $templateData = CourseTemplate::find($templateData['id']);
            if ($templateData['id'] > 0 && ! $templateData) {
                throw new \Exception(config_locale('messages.courses.templatenotfound'));
            }
            // save the units
            $added = $this->unitsservice->saveUnits($allUnits, $existingCourse, $templateData);

            $succeededUnits = $added['saved'] ?? 0;
            $failedUnits = $added['failed'] ?? 0;
            $allUnitsCount = collect($succeededUnits)->where('status', '=', 1)->count();
            $addedCount = collect($succeededUnits)->where('process_result', '=', 'added')->count();
            $removedCount = collect($succeededUnits)->where('process_result', '=', 'removed')->count();
            $intactCount = collect($succeededUnits)->where('process_result', '=', 'notchanged')->count();
            $failedCount = collect($failedUnits)->where('process_result', '=', 'failed')->count();
            $updateFailedCount = collect($failedUnits)->where('process_result', '=', 'updatefailed')->count();
            // echo "TotalUnits:: $allUnitsCount, Added :: $addedCount, Removed:: $removedCount, Notchanged :: $intactCount, Failed:: $failedCount";
            // dd($succeededUnits);
            // dd($added);
            $templateData = CourseTemplate::getTemplateData($existingCourse, $templateId);
            $data = $this->service->getCourseUnitDetails($templateData);
            $progress = $this->service->getProgressStatus($existingCourse);
            $data['progress'] = $progress;
            $data['failed'] = $added['failed'] ?? [];
            // $msg = config_locale('messages.courses.unitaddedremoved')." ".config_locale('messages.courses.allunitscount');
            $msg = '';
            if ($addedCount > 0 || $removedCount > 0) {
                $msg = config_locale('messages.courses.unitaddedremoved');
                $msg = str_replace(['{{totalcount}}', '{{addedcount}}', '{{removedcount}}'], [$allUnitsCount, $addedCount, $removedCount], $msg);
            }
            // DB::rollBack();
            DB::commit();
            if ($failedCount > 0 || $updateFailedCount) {
                $msgfailed = '';
                if ($failedCount > 0) {
                    $msgfailed = config_locale('messages.courses.notalladded');
                    $msgfailed = str_replace('{{count}}', $failedCount, $msgfailed);
                }
                if ($updateFailedCount > 0) {
                    $updatefailed = config_locale('messages.courses.updatefailed');
                    $updatefailed = str_replace('{{count}}', $updateFailedCount, $updatefailed);
                    $msgfailed = $msgfailed.' '.$updatefailed;
                }
                $msg = $msg.' '.$msgfailed;

                return ajaxSuccessWithWarning($data, $msg);
            }

            return ajaxSuccess($data, $msg);
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function sortUnits(CourseSetupService $service, Request $request)
    {
        $course_id = $request->input('course');
        $idsQueue = $request->input('map_ids');
        $templateId = $request->input('template') ?? 0;
        if (! $templateId) {
            return ajaxError('Template not set', 500);
        }
        $sorted = $service->sortUnits($course_id, $idsQueue, $templateId);
        $templateData = CourseTemplate::getTemplateData($course_id, $templateId);
        $data = $this->service->getCourseUnitDetails($templateData);

        return ajaxSuccess($data, config_locale('messages.courses.units_sorted'));
    }

    public function destroyCourse(CourseSetupService $service, $id)
    {
        $service->deleteCourseTemplate($id);

        return ajaxSuccess([], 'Course Template Delete Successfully');
    }

    public function getCourseStructure($courseCode)
    {
        $college_id = Auth::user()->college_id;
        $result = Courses::with([
            'courseSubjects.subject.subjectUnits.assessmentTasksUnits.assessmentTask',
        ])->where(['college_id' => $college_id, 'course_code' => $courseCode])->first();
        $struc = ($result->courseSubjects) ? CourseStructureResource::collection($result->courseSubjects) : [];

        return $struc;
    }

    public function saveCourseSubject(StoreCourseSubjectRequest $request)
    {
        DB::beginTransaction();
        try {
            $validatedData = $request->validated();

            $college_id = Auth::user()->college_id;
            $templateData = $validatedData['template'] ?? [];

            Arr::forget($validatedData, 'template');
            $templateId = $templateData['id'] ?? null;
            /* this is not required as it has been validated earlier */
            $courseData = Courses::where('college_id', $college_id)->find($request->course_id);
            if (! $courseData) {
                $errorMessage = config_locale('messages.courses.nocourse', 'Could not save subject! Unknown error occurred');
                throw new \Exception($errorMessage);
                // returrn the erro message
            }

            $templateData = CourseTemplate::getTemplateData($courseData, $templateId);

            $saved = CourseSubjects::SaveSubject($validatedData, $courseData, $templateData);

            if ($saved !== true && $saved !== 'hasenrollments') {
                // return the error message
                $errorMessage = config_locale('messages.courses.'.$saved, 'Could not save subject! Unknown error occurred');
                throw new \Exception($errorMessage);
            }

            DB::commit();
            // need to send all the units data and subject data
            $data = $this->service->getCourseUnitDetails($templateData);
            $progress = $this->service->getProgressStatus($courseData);
            $data['progress'] = $progress;
            if ($saved === 'hasenrollments') {
                $message = config_locale('messages.courses.subjectupdatedpratially');
            } else {
                $message = config_locale('messages.courses.subjectupdated');
            }

            return ajaxSuccess($data, $message);
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function syncUnitDetail($course = '', $unit = '')
    {
        try {
            $coursesApi = new CoursesApiController;
            // get details form the code given
            $unitCodeDetails = $coursesApi->nationCodeApiCall($unit, true);
            $unitDetails = $unitCodeDetails['GetDetailsResult'] ?? [];
            $unitCode = $unitDetails['Code'] ?? '';
            // load all the units that are in the given course
            // only the details of unit in the course selected will be returned
            // if any other unit code that is not in the course will return empty
            $courseUnits = $coursesApi->loadCourseUnits($course);
            $savedCourseInfo = Courses::with([
                'units_normalized' => function ($query) use ($unit) {
                    return $query->where('unit_code', $unit);
                },
            ])->where('course_code', $course)->first();
            $unitsFound = $savedCourseInfo->units_normalized ?? [];
            $unitData = $unitsFound[0] ?? [];
            if (empty($unitData)) {
                $unitData = new SubjectUnits;
            }
            if ($unitDetails && $unitDetails['ComponentType'] == 'Unit') {
                // check for the course units from the units list
                $courseUnit = array_filter($courseUnits->units, function ($unit) use ($unitCode) {
                    return $unit['Code'] === $unitCode;
                });
                $courseUnit = array_values($courseUnit)[0] ?? null;
                $agreedNominalHrs = AgreedNominalHours::where('status', 1)->where('code', $unitCode)->value('agreed_hours');
                // if the $courseUnit is found the unit is inside the course if not the unit is not there
                $unit_type = (($courseUnit) && $courseUnit['IsEssential'] == true) ? SiteConstants::CORETEXT : SiteConstants::ELECTIVETEXT;
                $classification = $unitDetails['Classifications']['Classification'] ?? [];
                $fieldOfEducation = $classification[0]['ValueCode'] ?? null;
                $nominalHours = $agreedNominalHrs ?? null;
                $unitData->unit_code = $unitCode;
                $unitData->unit_type = $unit_type;
                $unitData->unit_name = $unitDetails['Title'] ?? '';
                $unitData->description = $unitDetails['Description'] ?? '';
                $unitData->field_education = (int) $fieldOfEducation;
                $unitData->field_education_text = $fieldOfEducation;
                $unitData->nominal_hours = $nominalHours ? (int) $nominalHours : $unitData->nominal_hours;
                $unitData->unit_source = 'api';

                return ajaxSuccess([
                    'unit' => new SubjectUnitsResource($unitData),
                ], null);
            } else {
                $errorMessage = config_locale('messages.courses.unitnotfound', 'Unit not available for edit');
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function addCustomUnit(StoreUnitRequest $request)
    {
        DB::beginTransaction();
        try {
            $validatedData = $request->validated();

            $templateData = $validatedData['template'] ?? [];
            Arr::forget($validatedData, 'template');
            $courseId = $validatedData['course_id'] ?? null;
            $templateId = $templateData['id'] ?? null;
            $courseId = $validatedData['course_id'];
            $templateData = CourseTemplate::getTemplateData($courseId, $templateId);

            $added = $this->unitsservice->saveCustomUnit($validatedData, $templateData);

            DB::commit();

            if (@$added['quota_exceeded']) {
                $errorMessage = config_locale(['messages.courses.nomoreunits', ['type' => $added['unit_type']]], 'Unit not available for edit');
                throw new \Exception($errorMessage);
            }
            // we need to refetch the course template data as the unit structure might have been updated
            $templateData = CourseTemplate::getTemplateData($courseId, $templateId);

            $data = $this->service->getCourseUnitDetails($templateData);
            $course = Courses::find($validatedData['course_id']);
            $templates = $this->loadCourseTemplates($course->course_code);
            $data['progress'] = $this->service->getProgressStatus($course);
            $data['templates'] = CourseTemplatesResource::collection($templates['list'] ?? []);

            return ajaxSuccess($data, config_locale('messages.courses.unitupdated'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function updateUnit(StoreUnitRequest $request)
    {
        DB::beginTransaction();
        try {
            $validatedData = $request->validated();
            $templateData = $validatedData['template'] ?? [];
            Arr::forget($validatedData, 'template');
            $courseId = $validatedData['course_id'] ?? null;
            $templateId = $templateData['id'] ?? null;
            $templateData = CourseTemplate::getTemplateData($courseId, $templateId);

            $updatedUnit = $this->unitsservice->updateUnit($validatedData, $templateData);

            if (@$updatedUnit['quota_exceeded']) {
                $errorMessage = config_locale(['messages.courses.nomoreunits', ['type' => $validatedData['unit_type']]], 'Unit not available for edit');
                throw new \Exception($errorMessage);
            }

            if (empty($updatedUnit)) {
                $defaultErrorMsg = 'Could not save unit! Unknown error occurred';
                $errorMessage = config_locale('messages.courses.cannotupdateunit', $defaultErrorMsg);
                throw new \Exception($errorMessage);
            }
            DB::commit();
            // we need to refetch the course template data as the unit structure might have been updated
            $templateData = CourseTemplate::getTemplateData($courseId, $templateId);

            $data = $this->service->getCourseUnitDetails($templateData);

            $course = Courses::find($validatedData['course_id']);

            $templates = $this->loadCourseTemplates($course->course_code);
            $data['progress'] = $this->service->getProgressStatus($course);
            $data['templates'] = CourseTemplatesResource::collection($templates['list'] ?? []);

            return ajaxSuccess($data, config_locale('messages.courses.unitupdated'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function assignSoloSubject(AssignUnitsToSubjectRequest $request)
    {
        $validatedData = $request->validated();
        DB::beginTransaction();
        try {
            $courseId = (int) $validatedData['course_id'] ?? null;
            $templateId = (int) $validatedData['template']['id'] ?? null;
            $course = Courses::find($courseId);
            if (! $course) {
                $errorMessage = config_locale('messages.courses.nocourse', '');
                throw new \Exception($errorMessage);
            }
            $subjectData = Arr::only($validatedData, [
                'subject_type',
                'grading_type',
                'contact_hours',
                'level',
                'is_long_indicator',
                'is_assessment',
                'subject_fee',
                'domestic_subject_fee',
                'delivery_mode',
                'EFTSL_study_load',
                'is_active',
                'inc_in_certificate',
            ]);
            $templateData = CourseTemplate::getTemplateData($courseId, $templateId);

            foreach ($validatedData['units'] as $unit) {
                $unitId = (int) $unit['unit_details']['id'] ?? $unit['ID'] ?? null;
                $unit = SubjectUnits::find($unitId);
                if (! $unit) {
                    continue;
                    // $errorMessage = config_locale('messages.courses.unitnotfound', '');
                    // throw new \Exception($errorMessage);
                }
                $this->service->assignSoloSubjectToUnit($unit, $course, $subjectData, $templateData);
            }
            DB::commit();
            $data = $this->service->getCourseUnitDetails($templateData);
            $templates = $this->loadCourseTemplates($course->course_code);
            $data['progress'] = $this->service->getProgressStatus($course);
            $data['templates'] = CourseTemplatesResource::collection($templates['list'] ?? []);

            return ajaxSuccess($data, config_locale('messages.courses.unitupdated'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
        }
    }

    private function getEnrollments($courseId)
    {
        $currentDate = Carbon::now();
        $data = Timetable::select(
            'rto_timetable.batch',
            'rto_timetable.start_week',
            'rto_timetable.end_week',
            'rto_subject.subject_code',
            'rto_subject.subject_name',
            DB::raw('count(distinct rto_student_subject_enrolment.student_id) as total_students'),
            DB::raw('group_concat(distinct rto_student_subject_enrolment.student_id) as students_list'),
            DB::raw('count(distinct rto_student_subject_enrolment.unit_id) as total_units'),
            DB::raw('group_concat(distinct rto_student_subject_enrolment.unit_id) as units_list'),
            DB::raw("CASE WHEN rto_timetable.end_week < '$currentDate' THEN 'Completed' ELSE 'Ongoing' END as status")
        )
            ->join('rto_course_subject', 'rto_timetable.subject_id', '=', 'rto_course_subject.subject_id')
            ->join('rto_subject', 'rto_timetable.subject_id', '=', 'rto_subject.id')
            ->join('rto_student_subject_enrolment', 'rto_timetable.batch', '=', 'rto_student_subject_enrolment.batch')
            ->where([
                'rto_course_subject.course_id' => $courseId,
            ])
            ->groupBy('rto_timetable.batch')
            ->get();
        // load the subjects details, unit details and student details for each enrollments
        $dataAvailableYears = [];
        $minYear = $maxYear = null;
        foreach ($data as $batch) {
            $batch->subject_name = $batch->subject_code.' - '.$batch->subject_name;
            $studentsList = ($batch->total_students > 0) ? $batch->students_list : '';
            $studentsList = explode(',', $studentsList);
            $unitsLists = ($batch->total_units > 0) ? $batch->units_list : '';
            $unitsLists = explode(',', $unitsLists);
            $startWeek = $batch->start_week ?? null;
            $endWeek = $batch->end_week ?? null;
            $startYear = ($startWeek) ? Carbon::parse($startWeek)->year : null;
            $endYear = ($endWeek) ? Carbon::parse($endWeek)->year : null;
            $minYear = ($minYear === null) ? $startYear : $minYear;
            $minYear = ($startYear < $minYear) ? $startYear : $minYear;
            $maxYear = ($endYear > $maxYear) ? $endYear : $maxYear;
            $batch->start_year = $startYear;
            $batch->end_year = $endYear;
            // Extract the year
            $year =

                // get students
                $students = Student::select(
                    'rto_students.id',
                    'generated_stud_id',
                    'profile_picture',
                    'student_type',
                    'name_title',
                    'first_name',
                    'middel_name',
                    'family_name',
                    DB::raw("CONCAT_WS(' ', rto_students.name_title, rto_students.first_name, rto_students.middel_name, rto_students.family_name) as student_name"),
                    'nickname',
                    'gender',
                    'DOB',
                    'email',
                    DB::raw('group_concat(rto_student_subject_enrolment.final_outcome) as final_outcome')
                )->Join('rto_student_subject_enrolment', function ($join) use ($batch) {
                    $join->on('rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                        ->where('rto_student_subject_enrolment.batch', '=', $batch->batch);
                    // $join->on('rto_student_subject_enrolment.batch', '=', DB::raw("'" . $batch->batch . "'"));
                })
                    ->Wherein('rto_students.id', $studentsList)
                    ->groupBy('rto_students.id')
                    ->get();
            foreach ($students as $student) {
                $filePath = config('constants.uploadFilePath.Users');
                $thumbText = config('constants.defaultThumbText');
                $destinationPath = Helpers::changeRootPath($filePath, null, Auth::user()->college_id);
                $profile_picture = $destinationPath['default'].$thumbText.$student->profile_picture;
                if (file_exists($profile_picture) && ! empty($student->profile_picture)) {
                    $picture_url = asset($destinationPath['view'].$thumbText.$student->profile_picture);
                    $original_picture_url = asset($destinationPath['view'].$student->profile_picture);
                } else {
                    $picture_url = $original_picture_url = asset('v2/img/user.png');
                }
                $student->profile_picture = $picture_url;
            }
            $units = UnitModule::select(
                'id',
                'unit_code',
                'subject_id',
                'vet_unit_code',
                'unit_name',
                'unit_type',
            )->Wherein('id', $unitsLists)->get();
            $batch->students = $students;
            $batch->units = $units;
        }
        $maxYear += 2;
        $dataAvailableYears = [];
        for ($i = $minYear; $i <= $maxYear; $i++) {
            $dataAvailableYears[] = ['value' => $i, 'label' => $i];
        }

        return ['enrollments' => $data, 'years' => $dataAvailableYears];
    }

    /* methods for intakes */
    public function intakes(IntakesFilterRequest $request)
    {
        $filterArray = $request->DTO();

        $yearsList = CoursesIntakeDate::selectRaw('YEAR(intake_start) as year')
            ->distinct()
            ->get()->pluck('year')->toArray();
        $currentYear = date('Y');
        $startYear = ($yearsList) ? min($yearsList) : $currentYear;
        $endYear = ($yearsList) ? max($yearsList) : $currentYear;
        $allYears = range($startYear, ($endYear + 5));
        $yearsList = array_map(function ($year) {
            return ['id' => $year, 'text' => (string) $year];
        }, $allYears);
        $facultiesData = Courses::getFaculties($request->college_id, false)->toArray();
        $facultiesData = array_map(function ($item) {
            $item['id'] = (string) $item['id'];

            return $item;
        }, $facultiesData);
        $campusesList = Courses::getCampusList($filterArray->college_id, true);
        // get the first college as current campus
        if (empty($filterArray->campus)) {
            $filterArray->campus = $campusesList[0]['id'] ?? null;
        }
        $yearsData = [];
        // $coursesList = CourseIntakesSummaryResource::collection($coursesList);
        /* prepare the data to render */
        $pageData = [
            'tenant_id' => tenancy('id'),
            /* format the course collection */
            'intakes' => CourseIntakesSummaryResource::collection($this->service->getIntakesData($filterArray)),
            /* Send the filter parameters to the renderer to render the course filter inputs */
            'filters' => [
                'campuses' => $campusesList, // get list of campuses
                'allcourses' => Courses::getCoursesForDropdown($filterArray->college_id, true, $filterArray->campus), // get list of campuses
                'types' => $this->service->getCourseTypes($filterArray->college_id), // types of courses
                'faculties' => $facultiesData, // faculties here
                'years' => $yearsList, // course durations filters
                'receivers' => kendify(config('constants.arrIntakeReceiver')),
                'trace' => $filterArray,
            ],
        ];

        /* render the results */
        return Inertia::render('intakes/List', $pageData);
    }

    /* to get intake details of a single course */
    public function getCourseIntakes($id)
    {
        $college_id = Auth::user()->college_id;
        /* search for the courses on the basis of params */
        $courseData = Courses::With(['campuses.campus', 'coursetype', 'intakes.campusIntakes.campus'])
            ->where('college_id', $college_id)
            ->where('id', $id)
            ->first();
        $course = new CourseIntakesSummaryResource($courseData);
        $data = ['intakes' => $course];

        return ajaxSuccess($data, '');
    }

    public function bulkAddIntakes(Request $request)
    {
        $college_id = Auth::user()->college_id;
        $dateParameters = $request->all();
        $filterParams = IntakesFilterDTO::LazyFromArray($request->query());
        $filterParams->college_id = Auth::user()->college_id;
        // save the intakes
        $addedIntakes = $this->service->bulkAddDates($dateParameters);

        // fetch all data ro return back
        $coursesList = $this->service->getIntakesData($filterParams);

        $data = ['intakes' => CourseIntakesSummaryResource::collection($coursesList)];
        $message = "Total {$addedIntakes['total']} intakes saved; ";
        $message .= "{$addedIntakes['inserted']} intakes newly inserted and ";
        $message .= "{$addedIntakes['updated']} intakes updated.";

        return ajaxSuccess($data, $message);
    }

    public function getIntakeDetail($id)
    {
        // get the intake details
        $college_id = Auth::user()->college_id;
        $intakeDetail = CoursesIntakeDate::with(['course.campuses', 'campusIntakes.campus'])->where('college_id', $college_id)->find($id);
        $intakeDetail = new CourseIntakeDetailResource($intakeDetail);
        $data = ['data' => $intakeDetail];

        return ajaxSuccess($data, '');
    }

    public function updateCourseIntakes(Request $request)
    {
        $college_id = Auth::user()->college_id;
        $dateParameters = $request->all();

        $filterParams = IntakesFilterDTO::LazyFromArray($request->query());
        $filterParams->college_id = $college_id;

        $addedIntakes = $this->service->updateCourseIntakes($dateParameters);

        $data = ['intakes' => CourseIntakesSummaryResource::collection($this->service->getIntakesData($filterParams))];
        $message = '';
        if (isset($addedIntakes['deleted'])) {
            $message = $addedIntakes['deleted'].' intakes successfully deleted.';
        } else {
            $total = $addedIntakes['updated'];
            $en = $addedIntakes['enabled'];
            $dis = $total - $en;
            $message = $addedIntakes['updated'].' intakes successfully updated.';
            if ($dis > 0) {
                $message .= ' Out of which '.$dis.' intake'.($dis == 1 ? ' is' : 's are').' disabled.';
            }
        }

        return ajaxSuccess($data, $message);
    }

    public function updateIntakes(Request $request)
    {
        $dateParameters = $request->all();
        $filterParams = IntakesFilterDTO::LazyFromArray($request->query());
        $filterParams->college_id = Auth::user()->college_id;
        $saved = $this->service->saveIntake($dateParameters);
        $courseId = $saved['course'] ?? false;

        $data = ['intakes' => CourseIntakesSummaryResource::collection($this->service->getIntakesData($filterParams))];
        // dd($data);
        $message = config_locale('messages.intakes.saved');
        if (isset($saved['deleted']) && ($saved['deleted'] || $saved['campuses_deleted'])) {
            if ($saved['deleted']) {
                $message = config_locale('messages.courses.deleted');
            } elseif ($saved['campuses_deleted']) {
                $message = str_replace('{{campus}}', $saved['campuses_deleted'], config_locale('messages.courses.deleted'));
            } else {
                $message = config_locale('messages.courses.notdeleted');
            }
        }

        return ajaxSuccess($data, $message);
    }

    public function destroySubject(Request $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;
            $subjectId = (int) $request->input('id') ?? 0;
            $courseId = (int) $request->input('course') ?? 0;
            $templateId = (int) $request->input('template') ?? 0;
            $scope = $request->input('scope');
            $scope = ($scope == 'course') ? 'course' : 'template';

            $filterArray = ['college_id' => $college_id, 'id' => $courseId];
            $existingCourse = Courses::Where($filterArray)->first();
            if (! $existingCourse) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }

            $templateData = CourseTemplate::getTemplateData($existingCourse, $templateId);

            // get the subject
            $subject = CourseSubjects::with(['subject_units'])->Where([
                'id' => $subjectId,
            ])->first();
            if (! $subject) {
                throw new \Exception(config_locale('messages.courses.subjectnotfound', 'Error!!! subject not found!'));
            }
            /* if the delete scope is for course or the selected template is a master template, then delete the subject from the course */
            if ($scope == 'template') {
                // now remove from the course
                $deleted = $subject->DeleteFromCourse();
            } else {
                /* remove the subject from the template */
                $deleted = $templateData->removeSubject($subject);
                // now remove from the course
                $deleted = $subject->DeleteFromCourse();
            }
            if ($deleted !== true) {
                $message = config_locale('messages.courses.'.$deleted, 'Error!!! could not delete this subject!');
                throw new \Exception($message);
            }
            DB::commit();
            $data = $this->service->getCourseUnitDetails($templateData);
            $progress = $this->service->getProgressStatus($existingCourse);
            $data['progress'] = $progress;

            return ajaxSuccess($data, config_locale('messages.courses.subject_deleted'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function destroyUnit(Request $request)
    {
        DB::beginTransaction();
        try {

            $college_id = Auth::user()->college_id;
            $subjectId = (int) $request->input('id') ?? 0;
            $courseId = (int) $request->input('course') ?? 0;
            $templateId = (int) $request->input('template') ?? 0;
            $filterArray = ['college_id' => $college_id, 'id' => $courseId];
            $existingCourse = Courses::Where($filterArray)->first();
            if (! $existingCourse) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }
            if (! in_array($existingCourse->course_type_id, [2, 17]) && empty($templateId)) {
                $templates = CourseTemplate::getTemplatesList($existingCourse->id, 'id');
                $templateData = $templates[0] ?? null;
            } else {
                // get the template
                $templateData = CourseTemplate::Where(['id' => $templateId, 'course_id' => $courseId])->first();
            }
            if (! $templateData) {
                $errorMessage = config_locale('messages.courses.templatenotfound', 'Course major/specialization is not available');
                throw new \Exception($errorMessage);
            }
            // get the subject
            $templateData = CourseTemplate::Where(['id' => $templateId, 'course_id' => $courseId])->first();
            if (! $templateData) {
                $errorMessage = config_locale('messages.courses.templatenotfound', 'Course major/specialization is not available');
                throw new \Exception($errorMessage);
            }
            // get the subject
            $unit = SubjectUnits::with(['subject.subject_units'])->Where(['id' => $subjectId, 'course_id' => $courseId])->first();

            if (! $unit) {
                $errorMessage = config_locale('messages.courses.unitnotfound', 'Unit not available for deletion');
                throw new \Exception($errorMessage);
            }
            if ($templateId) {
                $courseTemplateStructureObj = new CourseTemplateStructure;
                $deleted = $courseTemplateStructureObj->unlinkUnit($templateId, $unit->id);
                if ($deleted !== true) {
                    $message = config_locale('messages.courses.unit_not_unlinked');
                    throw new \Exception($message);
                }
            }
            if ($templateData->is_master_template) {
                $deleted = $unit->deleteUnit($existingCourse);
                if ($deleted !== true) {
                    $message = config_locale('messages.courses.unit_not_deleted');
                    throw new \Exception($message);
                }
            }
            DB::commit();
            $templateData = CourseTemplate::getTemplateData($existingCourse, $templateId);
            $data = $this->service->getCourseUnitDetails($templateData);
            $progress = $this->service->getProgressStatus($existingCourse);
            $data['progress'] = $progress;
            $successMessage = ($templateId) ? config_locale('messages.courses.unit_deleted_fromtemplate') : config_locale('messages.courses.unit_deleted');

            return ajaxSuccess($data, $successMessage);
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function updateCourseStatus(Request $request)
    {
        DB::beginTransaction();
        try {
            $college_id = Auth::user()->college_id;
            $courseId = (int) $request->input('id') ?? 0;
            $courseStatus = (int) $request->input('status') ?? 0;
            $filterArray = ['college_id' => $college_id, 'id' => $courseId];
            $existingCourse = Courses::Where($filterArray)->first();
            if (! $existingCourse) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }
            $currentStatus = $existingCourse->activated_now ?? 0;
            if ($courseStatus == $currentStatus) {
                $message = $currentStatus ? config_locale('messages.courses.already_active') : config_locale('messages.courses.already_inactive');
                throw new \Exception($message);
            }
            $updated = $existingCourse->updateStatus($courseStatus);
            if ($updated !== true) {
                $message = config_locale('messages.courses.'.$updated);
                throw new \Exception($message);
            }
            DB::commit();
            $message = $courseStatus ? config_locale('messages.courses.activated') : config_locale('messages.courses.deactivated');

            return ajaxSuccess([], $message);
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function destroyTemplate($templateId = null)
    {
        try {
            $templateData = CourseTemplate::where('id', $templateId)->first();
            $courseId = $templateData->course_id ?? null;
            $templateData->delete();
            $existingCourse = Courses::find($courseId);
            $template = $this->loadCourseTemplates($existingCourse->course_code, 'profile');
            $data = $this->service->getCourseUnitDetails($template['default']);
            $progress = $this->service->getProgressStatus($existingCourse);
            $data['progress'] = $progress;
            $data['templates'] = ['data' => CourseTemplatesResource::collection($template['list'] ?? [])];

            return ajaxSuccess(['data' => $data], config_locale('messages.courses.templatedeleted'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function duplicateTemplate($templateId = null)
    {
        DB::beginTransaction();
        try {
            $templateData = CourseTemplate::with(['templateunits'])->where('id', $templateId)->first();
            // get replicated count
            $replicateName = $templateData->template_name.' COPY';
            $replicated = CourseTemplate::where('template_name', 'like', $replicateName.'%')->count();
            $replicateName .= ($replicated > 0) ? '-'.($replicated + 1) : '';
            $newTemplate = $templateData->replicate(['created_by', 'updated_by', 'created_at', 'updated_at']);
            $newTemplate->template_name = $replicateName;
            $newTemplate->save();
            $courseId = $templateData->course_id ?? null;
            // Duplicate the comments
            foreach ($templateData->templateunits as $comment) {
                $newUnit = $comment->replicate(['created_by', 'updated_by', 'created_at', 'updated_at']);
                $newUnit->course_template_id = $newTemplate->id;
                $newUnit->save();
            }
            DB::commit();
            $existingCourse = Courses::find($courseId);
            $template = $this->loadCourseTemplates($existingCourse->course_code, 'profile');
            $thisTemplate = CourseTemplate::with(['templateunits'])->where('id', $newTemplate->id)->first();
            $data = $this->service->getCourseUnitDetails($thisTemplate);
            $progress = $this->service->getProgressStatus($existingCourse);
            $data['progress'] = $progress;
            $data['templates'] = ['data' => CourseTemplatesResource::collection($template['list'] ?? [])];
            $data['newtemplate'] = new CourseTemplatesResource($thisTemplate);

            return ajaxSuccess(['data' => $data], config_locale('messages.courses.templatecopied'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function getSubjectCompetencyElements(Request $request)
    {
        $courseId = $request->input('course_id') ?? null;
        $subjectId = $request->input('subject_id') ?? null;
        $template_id = $request->input('template_id') ?? null;
        $college_id = auth()->user()->college_id ?? null;
        $competencyModel = new ElementOfUnitCompetency;
        $competencyElements = $competencyModel->getElementOfUnitCompetency($college_id, $subjectId);

        return ajaxSuccess(['data' => $competencyElements], '');
    }

    public function saveSubjectCompetencyElements(CompetencyCriteriaRequest $request)
    {
        $data = $request->DTO();
        $saved = $this->service->saveCompetencyElements($data);
        if ($saved === true) {
            $competencyModel = new ElementOfUnitCompetency;
            $competencyElements = $competencyModel->getElementOfUnitCompetency($data->college_id, $data->subject_id);

            return ajaxSuccess(['data' => $competencyElements], config_locale('messages.courses.savedcelement', 'Competency element saved.'));
        } else {
            return ajaxError(config_locale("messages.courses.{$saved}", 'Error while saving competency element.'), 500);
        }
    }

    public function deleteSubjectCompetencyElements(Request $request)
    {
        $data = [
            'id' => (int) $request->input('id') ?? null,
            'subject_id' => (int) $request->input('subject_id') ?? null,
            'unit_id' => (int) $request->input('unit_id') ?? null,
        ];
        $deleted = $this->service->deleteCompetencyElements($data);
        if ($deleted === true) {
            $competencyModel = new ElementOfUnitCompetency;
            $competencyElements = $competencyModel->getElementOfUnitCompetency(Auth::user()->college_id, $data['subject_id']);

            return ajaxSuccess(['data' => $competencyElements], config_locale('messages.courses.elementdeleted', 'Competency element deleted.'));
        } else {
            return ajaxError(config_locale('messages.courses.elementnotdeleted', 'Error while deleting competency element.'), 500);
        }
    }

    public function courseSyncToMoodle(Request $request)
    {
        if (! $this->isMoodleConnected()) {
            return ajaxError(config_locale('messages.moodle.not_connect'), 500);
            // throw new \Exception(config_locale('messages.moodle.not_connect'));
        }

        // $college_id = Auth::user()->college_id;
        $type = $request->input('type', 'sync');
        $courseId = (int) $request->input('id', 0);
        $courseIds = (array) $request->input('ids', []);

        $validationError = $this->validateSyncRequest($type, $courseId, $courseIds);
        if ($validationError) {
            return $validationError;
        }

        DB::beginTransaction();
        try {
            switch ($type) {
                case 'bulk-sync':
                    $this->syncMultipleCourses($courseIds);
                    break;
                case 'sync':
                    $this->syncSingleCourse($courseId);
                    break;
                case 're-sync':
                    $this->reSyncSingleCourse($courseId);
                    break;
                default:
                    return ajaxError('Invalid sync type.', 400);
            }
            DB::commit();

            return ajaxSuccess([], config_locale('messages.moodle.syncing_to'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    private function validateSyncRequest(string $type, int $courseId, array $courseIds)
    {
        // Check for invalid bulk-sync request (no courses selected)
        if ($type == 'bulk-sync' && count($courseIds) == 0) {
            return ajaxError(config_locale('messages.moodle.select_any_course'), 500);
        }

        // Check for invalid sync/re-sync request (no course selected)
        if (in_array($type, ['sync', 're-sync'], true) && $courseId === 0) {
            return ajaxError(config_locale('messages.courses.nocourse'), 500);
        }

        return null;  // No error, validation passed
    }

    private function syncSingleCourse(int $courseId)
    {
        $courseData = Courses::find($courseId);
        if (! $courseData) {
            throw new \Exception(config_locale('messages.courses.nocourse'));
        }

        /*$course = Courses::where('rto_courses.id', $courseId)
            ->has('courseUnits.assessmentUnitTasks')
            ->selectRaw("rto_courses.*")
            ->notSyncedWithMoodle()
            ->first();*/

        /*$moodleItem = $courseData->moodleItem()->first();
        if (!$moodleItem || !$moodleItem->getSyncId()) {
            dispatch_sync(new SyncCourseToMoodle($courseId));
        }*/

        $courseMoodleItem = $this->getMoodleItem($courseData);
        if (! $courseMoodleItem || ! $courseMoodleItem->getSyncId()) {
            dispatch_sync(new SyncCourseToMoodle($courseId));
        }
    }

    private function reSyncSingleCourse(int $courseId)
    {
        $courseData = Courses::find($courseId);
        if (! $courseData) {
            throw new \Exception(config_locale('messages.courses.nocourse'));
        }

        $courseMoodleItem = $this->getMoodleItem($courseData);
        if ($courseMoodleItem && ! $courseMoodleItem->getSyncId()) {
            dispatch_sync(new SyncCourseToMoodle($courseId));
        }
    }

    private function syncMultipleCourses(array $courseIds)
    {
        /*$jobs = array_map(function($courseId) {
            return new SyncCourseToMoodle($courseId);
        }, $courseIds);*/

        $jobs = [];
        foreach ($courseIds as $courseId) {
            $courseData = Courses::find($courseId);

            if ($courseData) {
                /*$isSynced = $courseData->moodleItem()->first()->getSyncId();
                if(!$isSynced){
                    $jobs[] = new SyncCourseToMoodle($courseId);
                    //dispatch_sync(new SyncCourseToMoodle($courseId));
                }*/

                $courseMoodleItem = $this->getMoodleItem($courseData);
                if (! $courseMoodleItem || ! $courseMoodleItem->getSyncId()) {
                    $jobs[] = new SyncCourseToMoodle($courseId);
                    // dispatch_sync(new SyncCourseToMoodle($courseId));
                }
            }
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }
    }

    public function courseSyncFromMoodle(Request $request)
    {
        if (! $this->isMoodleConnected()) {
            return ajaxError(config_locale('messages.moodle.not_connect'), 500);
        }
        // $college_id = Auth::user()->college_id;

        DB::beginTransaction();
        try {
            dispatch_sync(new SyncAllCoursesFromMoodle);
            DB::commit();

            return ajaxSuccess([], config_locale('messages.moodle.syncing_from'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    private function getMoodleItem($dataObject)
    {
        return $dataObject->moodleItem()->first();
    }

    public function saveShortCourseIntake(ShortCourseIntakeRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->DTO();
            $saved = $this->service->saveShortCourseIntake($data);
            DB::commit();
            $intakes = $this->service->getShortCourseIntakes($data->course_id, 'id', $data->filters ?? []);
            if ($intakes == 'nocourse') {
                return ajaxError(config_locale('messages.courses.nocourse'), 404);
            }

            return ajaxSuccess([
                'intakes' => CourseShortCourseIntakesResource::collection($intakes),
                'meta' => [
                    'current_page' => $intakes->currentPage(),
                    'from' => $intakes->firstItem(),
                    'last_page' => $intakes->lastPage(),
                    'path' => $intakes->path(),
                    'per_page' => $intakes->perPage(),
                    'to' => $intakes->lastItem(),
                    'total' => $intakes->total(),
                ],
            ], config_locale('messages.courses.savedshortcourseintake', 'Short course intake saved.'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
        }
    }

    public function getShortCourseIntakes(Request $request, $code = '')
    {
        $courseCode = $code ?? $request->input('code', '');
        $type = $request->input('type', 'all');
        $status = $request->input('status', 'all');
        $search = $request->input('search', '');
        $take = $request->input('take', SiteConstants::DEFAULT_PER_PAGE);
        $page = $request->input('page', 1);
        $perpage = $request->input('perpage', SiteConstants::DEFAULT_PER_PAGE);

        $filters = [
            'type' => $type,
            'status' => $status,
            'search' => $search,
            'perpage' => $perpage,
            'page' => $page,
            'take' => $take,
        ];
        $intakes = $this->service->getShortCourseIntakes($courseCode, 'code', $filters);
        if ($intakes == 'nocourse') {
            return ajaxError(config_locale('messages.courses.nocourse'), 404);
        }

        return ajaxSuccess([
            'intakes' => CourseShortCourseIntakesResource::collection(resource: $intakes),
            'meta' => [
                'current_page' => $intakes->currentPage(),
                'from' => $intakes->firstItem(),
                'last_page' => $intakes->lastPage(),
                'path' => $intakes->path(),
                'per_page' => $intakes->perPage(),
                'to' => $intakes->lastItem(),
                'total' => $intakes->total(),
            ],
        ],
            config_locale('messages.courses.savedshortcourseintake',
                'Short course intake saved.'
            ));
    }

    public function processShortCourseIntakesActions(Request $request, $code = '', $action = '')
    {
        $data = $request->input('intakes', []);

        $courseId = Courses::where('course_code', $code)->value('id');
        if (! $courseId) {
            return ajaxError(config_locale('messages.courses.nocourse'), 404);
        }
        $intakes = CoursesIntakeDate::where('course_id', $courseId)->whereIn('id', $data);
        if ($action == 'delete') {
            $intakes->delete();
        } else {
            $action = $action == 'active' ? 1 : 0;
            $intakes->update(['active' => $action]);
        }
        /* get the intakes again */

        $filters = $request->input('filters', []);

        $intakes = $this->service->getShortCourseIntakes(
            $courseId,
            'id',
            [
                'type' => $filters['type'] ?? 'all',
                'status' => $filters['status'] ?? 'all',
                'search' => $filters['search'] ?? '',
                'perpage' => $filters['perpage'] ?? SiteConstants::DEFAULT_PER_PAGE,
            ]);

        return ajaxSuccess([
            'intakes' => CourseShortCourseIntakesResource::collection(resource: $intakes),
            'meta' => [
                'current_page' => $intakes->currentPage(),
                'from' => $intakes->firstItem(),
                'last_page' => $intakes->lastPage(),
                'path' => $intakes->path(),
                'per_page' => $intakes->perPage(),
                'to' => $intakes->lastItem(),
                'total' => $intakes->total(),
            ],
        ],
            config_locale('messages.courses.savedshortcourseintake',
                'Short course intake saved.'
            ));

        return ajaxSuccess([], config_locale('messages.courses.savedshortcourseintake', 'Short course intake saved.'));
    }

    public function searchSubjects(Request $request)
    {
        $text = $request->input('search_criteria', '');
        $courseId = $request->input('course_id', null);
        $type = $request->input('course_type', null);
        $subjects = [];
        if ($text) {
            $subjects = $this->service->searchSubjects($text, $type, $courseId);
        }

        return ajaxSuccess(['subjects' => $subjects->map(function ($subject) {
            return [
                'id' => $subject->id,
                'subject_code' => $subject->subject_code,
                'subject_name' => $subject->subject_name,
                'subject_type' => ($subject->subject_type == SiteConstants::CORETEXT) ? 'Core' : 'Elective',
                'grading_type' => $subject->grading_type,
                'contact_hours' => $subject->contact_hours,
                'subject_units_count' => $subject->subject_units_count,
            ];
        })], 'Subjects searched.');
    }

    public function saveCourseSubjects(Request $request)
    {
        DB::beginTransaction();
        try {
            $templateId = $request->input('template_id', null);
            $courseId = $request->input('course_id', null);
            $subjects = $request->input('subjects', null);

            $existingCourse = Courses::find($courseId);
            if (! $existingCourse) {
                throw new \Exception(config_locale('messages.courses.nocourse'));
            }
            $templateData = CourseTemplate::getTemplateData($existingCourse, $templateId);

            $subjects = $this->service->addSubjectsToCourse($existingCourse, $subjects, $templateData);

            if (empty($subjects)) {
                $message = config_locale('messages.courses.subjectaddederror', 'Error!!! could not add the subjects!');
                throw new \Exception($message);
            }
            $data = $this->service->getCourseUnitDetails($templateData);
            $progress = $this->service->getProgressStatus($existingCourse);
            $data['progress'] = $progress;
            DB::commit();

            return ajaxSuccess($data, config_locale('messages.courses.subject_deleted'));
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }

    }
}
