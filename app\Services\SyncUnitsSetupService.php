<?php

namespace App\Services;

use App\Classes\SiteConstants;
use App\Model\CourseSubject as oldCourseSubject;
use App\Model\Subject as oldSubject;
use App\Model\UnitModule as oldUnitModule;
use App\Model\v2\Courses;
use App\Model\v2\CourseSubject;
use App\Model\v2\CourseSubjects;
use App\Model\v2\CourseTemplate;
use App\Model\v2\Subject;
use App\Model\v2\SubjectUnits;
use App\Model\v2\UnitModule;
use App\Model\v2\Units;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class SyncUnitsSetupService
{
    /*
    method to sync the units from old setup to new setup
    This method will
    1. Check if there are any unmigrated units in the course
    2. Get the course data with course subjects -> subjects -> subject_units from old setup
    3. The units will be saved to the rto_units table
    4. The units will be saved to the rto_subject_units table
    5. The subject mapping will be saved to the rto_course_subjects table
    6. The units will be mapped to the course template
    7. Remove any extra course subjects that is in new setup but have been removed from legacy side
    */
    public static function SyncUnitsFromOldSetupToNew(CourseTemplate|Courses $data, $force = false)
    {
        $courseId = $template = null;
        if ($data instanceof CourseTemplate) {
            $courseId = $data->course_id ?? null;
            $template = $data;
        } elseif ($data instanceof Courses) {
            $courseId = $data->id ?? null;
        }

        if (empty($courseId)) {
            return;
        }

        DB::beginTransaction();

        try {
            /*
            Step 1:
            check if there are any unmigrated units in the course
            */
            $unmigratedQry = CourseSubject::select(DB::Raw('COUNT(rto_subject_unit.id) AS unmigrated'))
                ->join('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_course_subject.subject_id')
                ->join('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
                ->leftjoin('rto_subject_units', function ($join) use ($courseId) {
                    $join->on('rto_subject_units.synced_for', '=', 'rto_subject_unit.id')
                        ->where('rto_subject_units.course_id', '=', $courseId);
                    // $join->on('rto_student_subject_enrolment.batch', '=', DB::raw("'" . $batch->batch . "'"));
                })
                ->leftjoin('rto_course_subjects', function ($join) use ($courseId) {
                    $join->on('rto_course_subjects.synced_subject', '=', 'rto_subject.id')
                        ->on('rto_course_subjects.synced_for', '=', 'rto_course_subject.id')
                        ->where('rto_course_subjects.course_id', '=', $courseId);
                })
                ->where('rto_course_subject.course_id', $courseId);
            if (! $force) {
                $unmigratedQry->where(function ($query) {
                    $query->whereNull('rto_subject_units.synced_for')
                        ->orWhereNull('rto_course_subjects.synced_for')
                        ->orWhereNull('rto_course_subjects.synced_subject');
                });
            }
            // dd(getParsedSql($unmigratedQry));
            $unmigrated = $unmigratedQry->value('unmigrated');
            info('unmigrated', ['unmigrated' => $unmigrated]);

            return null;
            /*
            if there are no unmigrated units then return
            */
            if (! $unmigrated) {
                return null;
            }
            /*
            Step 2:
            now get the units and insert it to units table
            */
            $course = Courses::with(['courseSubjects.subject.subjectUnits'])->find($courseId);

            if (! $course) {
                return null;
            }

            $courseSubjects = $course->courseSubjects ?? collect([]);
            /*
            get all subject ids from the course subjects
            */
            $subjectIds = $courseSubjects->pluck('subject_id');
            $unitsArr = [];
            foreach ($courseSubjects as $courseSubject) {
                // get the subject of the courseSubject
                // dd($courseSubject);
                $subject = $courseSubject->subject ?? null;
                // if no subject found then continue to next courseSubject
                if (! $subject) {
                    continue;
                }
                // get the units in the subject
                $units = $subject->subjectUnits ?? collect([]);
                // proceed only if there are units in the subject
                if ($units->count() > 0) {
                    /*
                    Step 3:
                    add the units to the master units collection table
                    SaveUnitsToMasterUnitsCollectionTable will add a new record if the unit is not found
                    otherwise it will update the existing record in rto_units table
                    */
                    // dd($units);
                    $savedUnits = self::SaveUnitsToMasterUnitsCollectionTable($units);
                    /*
                    prepare the saved units data for rto_subject units table
                    */
                    $unitsToMigrate = [];
                    foreach ($savedUnits as $unit) {
                        /*
                        unit module data is the unit data from old units setup rto_subject_unit
                        this is the data to be migrated
                        */
                        /*
                        This will give the unit data from the old setup that have been successfully saved to
                        rto_units table
                        */

                        $unitModuleDataArr = $units->filter(function ($item) use ($unit) {
                            return $item->unit_code == $unit->unit_code;
                        });
                        /*

                        */
                        foreach ($unitModuleDataArr as $unitModuleData) {
                            $additionalInfo = [
                                'unit_id' => $unit->id ?? null,
                                'course_id' => $course->id,
                                'course_subject_id' => null,
                                'display_order' => 0,
                                'synced_for' => $unitModuleData->id ?? null,
                            ];
                            $unitModuleData = [...$unitModuleData->toArray(), ...$additionalInfo];
                            unset($unitModuleData['id']);
                            $unitsToMigrate[] = $unitModuleData;
                            $unitsArr[] = $unitModuleData;
                        }

                    }
                    $subject->setRelation('units', $unitsToMigrate);
                }
                /*
                Step 4: & 5:
                Now save the subject mapping from old setup to new setup
                */
                // dump($courseSubject);
                self::SaveSubjectMappingFromOldSetupToNewSetup($subject, $courseSubject);
            }
            // dd("finished");
            /*
            All the units have been moved to the new units setup table,
            Now prepare the template/major data for the units

            For that get the fresh data of the course subjects and units
            */
            $newCourseSubjects = Courses::with(['subjects.subject_units'])->find($courseId)->subjects ?? [];

            /*
            Step 6:
            Now map the units to the course template
            Get the template data will create a default if template does not exist for the course
            */

            $template = CourseTemplate::getTemplateData($courseId, null, true);
            // dd($newCourseSubjects);
            foreach ($newCourseSubjects as $newCourseSubject) {
                $units = ($newCourseSubject->subject_units) ? $newCourseSubject->subject_units : [];
                foreach ($units as $unit) {
                    $template->mapUnitToTemplate($unit);
                }
            }

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        } finally {
            DB::rollBack();
        }
    }

    /*
    This method will save the units to the rto_units table
    rto_units table will have the unique collection of units used throughout the application courses
    Any units added to any course/subject will be saved to the rto_units table
    */
    public static function SaveUnitsToMasterUnitsCollectionTable($subjectUnits)
    {
        /*
        these are the fields to be inserted to the rto_units table
        */
        $insertFields = ['college_id', 'unit_code', 'vet_unit_code', 'unit_name', 'description', 'unit_type', 'field_education', 'delivery_mode', 'internal', 'external', 'workplace_based_delivery', 'nominal_hours', 'tution_fees', 'domestic_tution_fees', 'module_unit_flag', 'vet_flag', 'work_placement', 'AVETMISS_Report', 'status', 'unit_type_basic', 'unit_source', 'unit_scope', 'active_from', 'is_superseded', 'superseded_date', 'superseded_by', 'created_by', 'updated_by', 'created_at', 'updated_at'];
        $updateFields = [];
        /*
        default values for the fields
        */
        $defaultValues = [
            'college_id' => auth()->user()->college_id,
            'unit_type' => 'None',
            'internal' => 'N',
            'external' => 'N',
            'workplace_based_delivery' => 'N',
            'vet_flag' => 'No',
            'work_placement' => 'No',
            'unit_source' => 'custom',
            'unit_scope' => 'public',
            'is_superseded' => 0,
        ];
        /*
        from the subjectUnits, get the fields to be inserted to the rto_units table
        */
        $saveData = $subjectUnits->map(function ($item) use ($insertFields, $defaultValues) {
            $itmDta = Arr::only($item->toArray(), $insertFields);
            /*
            Assign the default values to the fields that have no values
            */
            foreach ($defaultValues as $key => $value) {
                if (array_key_exists($key, $itmDta) && empty($itmDta[$key])) {
                    $itmDta[$key] = $value;
                }
            }
            $itmDta['status'] = 1;

            // foreach($updateFields)
            return $itmDta;
        });

        /*
        Insert or the update the units in the rto_units table with the formated data
        */
        $savedUnits = Units::upsert($saveData->toArray(), ['unit_code'], $updateFields);

        /*
        get the unit codes that are to be saved
        */
        $unitCodes = $saveData->map(function ($unit) {
            return $unit['unit_code'];
        })->toArray();

        /*
        Get the data from the rto_units table for the saved units
        */
        return Units::whereIn('unit_code', $unitCodes)->get();
    }

    /*
    This method will move the subject mapping from the old setup to the new setup
    Parameters:

    $subject: Subject object from the old setup (rto_subject)
    no data will be moved from rto_subject to new setup as rto_subject is common for both new and old setup

    $courseSubject: CourseSubject object from the old setup

    Ultimately this method will move the data from rto_course_subject to rto_course_subjects table
    */
    public static function SaveSubjectMappingFromOldSetupToNewSetup(Subject|oldSubject|null $subject = null, CourseSubject|oldCourseSubject|null $courseSubject = null)
    {
        /*
        id (Primary Key) of the rto_subject table
        */
        $syncedSubject = $subject->id ?? null;
        /*
        id (Primary Key) of the rto_course_subject table
        */
        $syncedCourseSubject = $courseSubject->id ?? null;
        /*
        id (Primary Key) of the rto_course_subject table
        */
        $syncedCourseSubjectCode = $subject->subject_code ?? null;
        /*
        default values for the fields
        */
        $defaultValues = [
            'subject_type' => 'None',
            'internal' => 'N',
            'external' => 'N',
            'workplace_based_delivery' => 'N',
            'is_active' => 1,
            'inc_in_certificate' => 0,
            'display_order' => 1,
        ];

        $syncIds = ['synced_subject' => $syncedSubject, 'synced_for' => $syncedCourseSubject];

        $units = $subject['units'] ?? [];

        /*
        data to be saved to rto_course_subjects table
        */
        $courseSubjectType = $courseSubject->unit_type ?? null;
        $courseSubject = [...$subject->toArray(), ...$courseSubject->toArray(), ...$syncIds];
        $courseSubject['unit_type'] = $courseSubjectType;

        // dd($courseSubject);
        if (isset($courseSubject['units'])) {
            unset($courseSubject['units']);
        }
        if (isset($courseSubject['subject_units'])) {
            unset($courseSubject['subject_units']);
        }
        if (isset($courseSubject['subject'])) {
            unset($courseSubject['subject']);
        }

        // overwrite the default values to the empty fields
        foreach ($defaultValues as $key => $value) {
            if (array_key_exists($key, $courseSubject) && empty($courseSubject[$key])) {
                $courseSubject[$key] = $value;
            }
        }
        $courseSubject['subject_type'] = $courseSubject['unit_type'] ?? 'None';
        $courseSubjectSaved = CourseSubjects::where($syncIds)->first();
        // dd($courseSubject, $courseSubjectSaved);
        if (! $courseSubjectSaved) {
            $courseSubjectSaved = new CourseSubjects($courseSubject);
            $courseSubjectSaved->saveQuietly();
        } else {
            $courseSubjectSaved->updateQuietly($courseSubject);
        }
        if ($units) {
            /* sync the subject units */
            $units = array_map(function ($item) use ($courseSubjectSaved, $courseSubjectType) {
                // $item["unit_type"] = empty($item["unit_type"]) ? 'None' : $item["unit_type"];
                $item['unit_type'] = (! empty($courseSubjectType)) ? $courseSubjectType : (empty($item['unit_type']) ? SiteConstants::ELECTIVETEXT : $item['unit_type']);
                $item['internal'] = empty($item['internal']) ? 'N' : $item['internal'];
                $item['external'] = empty($item['external']) ? 'N' : $item['external'];
                $item['workplace_based_delivery'] = empty($item['workplace_based_delivery']) ? 'N' : $item['workplace_based_delivery'];
                $item['vet_flag'] = empty($item['vet_flag']) ? 'No' : $item['vet_flag'];
                $item['work_placement'] = empty($item['work_placement']) ? 'No' : $item['work_placement'];
                $item['status'] = empty($item['status']) ? 1 : $item['status'];
                $item['unit_source'] = empty($item['unit_source']) ? 'custom' : $item['unit_source'];
                $item['course_subject_id'] = $courseSubjectSaved->id ?? null;
                unset($item['college_id']);
                unset($item['subject_id']);

                return $item;
            }, $units);
            foreach ($units as $unit) {
                $filter = ['unit_code' => $unit['unit_code'], 'course_subject_id' => $unit['course_subject_id'], 'synced_for' => $unit['synced_for']];
                $savedUnit = SubjectUnits::where($filter)->first();
                if (! $savedUnit) {
                    unset($unit['updated_at']);
                    $savedUnit = new SubjectUnits($unit);
                    $savedUnit->saveQuietly();
                } else {
                    unset($unit['created_at']);
                    $savedUnit->updateQuietly($unit);
                }
            }
        }
        $courseSubjectSaved->updateTemplateStructure();

        return $courseSubjectSaved;
    }

    public static function DeleteCourseSubjectFromOldSetup($subject)
    {
        // if($subject->synced_subject) Subject::where("id", $subject->synced_subject)->delete();
        if ($subject->synced_for) {
            CourseSubject::where('id', $subject->synced_for)->delete();
        }
    }

    public static function DeleteUnitFromOldSetup(SubjectUnits $unit)
    {

        // if($unit->synced_for) UnitModule::where("id", $unit->synced_for)->delete();
    }

    public function syncFromNewUnitsToOld() {}

    /*
    This method will sync the changes made to the units to the old setup
    This function will be triggered on the created/updated event hook on Subject Units model (rto_subject_units)
    */
    public static function SyncSubjectUnitsToOldSetup(SubjectUnits $unit)
    {

        $college_id = auth()->user()->college_id;
        /*
        $unit should sync with rto_subject_unit table
        $unit will have course_subject_id which links to CourseSubjects
        CourseSubjects will have to sync with rto_subject and rto_course_subject
        */
        $courseSubjectId = $unit->course_subject_id;
        /*
        only sync the units that has been assigned to subject
        */
        $courseSubject = null;
        if ($courseSubjectId) {
            $courseSubject = CourseSubjects::find($courseSubjectId);
        } else {
            // if the courseSubjectId is null the unit is de-associtated with the subject so the unit can be removed from the old setup
            if ($unit->synced_for) {
                UnitModule::find($unit->synced_for)?->delete();

                return;
            }
        }
        /*
        if there is no courseSubject no need to sync so return
        It is because unit having no subject are nor usable from old course setup so
        unless the subject is assigned to unit; no need to sync
        */
        if (empty($courseSubject)) {
            return;
        }

        $courseSubject = self::SyncCourseSubjectToOldSetup($courseSubject, true);
        /* now the course subject has been synced to the old setup */

        $syncedSubjectId = $courseSubject->synced_subject ?? null;
        $syncedCourseSubjectId = $courseSubject->synced_for ?? null;
        /*
        now sync the units
        The unit will be synced with the unit_module table
        */
        if (! $syncedSubjectId) {
            return;
        }

        return self::syncUnitToOld($unit, $syncedSubjectId);
    }

    public static function syncUnitToOld(SubjectUnits $unit, $syncedSubjectId)
    {
        if (! $unit) {
            return null;
        }
        $college_id = auth()->user()->college_id;
        $userId = auth()->user()->id ?? 0;

        $unitSyncedId = $unit->synced_for ?? null;
        $unitModule = UnitModule::where(['id' => $unitSyncedId])->orWhere(function ($query) use ($unit, $syncedSubjectId) {
            $query->where(['unit_code' => $unit->unit_code, 'subject_id' => $syncedSubjectId]);
        })->first();

        $unitData = $unit->toArray();
        $unitData['subject_id'] = $syncedSubjectId;
        $unitData['college_id'] = $college_id;
        $unitModuleId = $unitModule->id ?? null;
        if (! $unitModuleId) {
            $unitData['created_by'] = $userId;
            $unitData['updated_by'] = $userId;
            $unitModule = new UnitModule($unitData);
            $unitModule->saveQuietly();
        }
        if ($unitModule && $unitModule->id) {
            $unit->updateQuietly(['synced_for' => $unitModule->id]);
        }

        return $unit;
    }

    public static function syncUnitToNew(CourseSubjects $courseSubject, UnitModule|oldUnitModule $unitModule, $action = null)
    {
        if ($action == 'deleted') {
            $courseSubject->subject_units()->where('synced_for', $unitModule->id)->delete();
        } else {
            $courseSubjectUnit = $courseSubject->subject_units()->where('synced_for', $unitModule->id)->first();
            $saveData = Arr::only(
                $unitModule->toArray(),
                [
                    'unit_id',
                    'unit_code',
                    'vet_unit_code',
                    'unit_name',
                    'unit_name',
                    'description',
                    'unit_type',
                    'field_education',
                    'delivery_mode',
                    'internal',
                    'external',
                    'workplace_based_delivery',
                    'nominal_hours',
                    'tution_fees',
                    'domestic_tution_fees',
                    'module_unit_flag',
                    'vet_flag',
                    'work_placement',
                    'AVETMISS_Report',
                    'status',
                ]);
            $saveData['course_id'] = $courseSubject->course_id;
            $saveData['course_subject_id'] = $courseSubject->id;
            $saveData['synced_for'] = $unitModule->id;
            if ($courseSubjectUnit) {
                $courseSubjectUnit->updateQuietly($saveData);
            } else {
                $courseSubject->subject_units()->createQuietly($saveData);
            }
        }
    }

    public static function SyncUnitModuleToNewSetup(UnitModule|oldUnitModule $unitModule, Subject|oldSubject $subject, $action)
    {
        /* get all the sync course subjects */
        $courseSubjects = CourseSubjects::where(['synced_subject' => $subject->id])->get();
        if ($courseSubjects->count() > 0) {
            $units = self::SaveUnitsToMasterUnitsCollectionTable(collect([$unitModule]));
            $unitModule->unit_id = $units->first()->id ?? null;
            foreach ($courseSubjects as $courseSubject) {
                self::syncUnitToNew($courseSubject, $unitModule, $action);
            }
        }

    }

    /*
    Methods to syncronize the changes made to units/subject setup to reflect to the old setup
    This function will be triggered on the created/updated event hook on Course Subjects model
    */
    public static function SyncCourseSubjectToOldSetup(CourseSubjects $newCourseSubject, $insert = false, $syncUnits = false)
    {
        $college_id = auth()->user()->college_id;
        $courseTypeId = Courses::where('id', $newCourseSubject->course_id)->value('course_type_id');

        $syncedFor = $newCourseSubject->synced_for ?? null;
        $syncedSubject = $newCourseSubject->synced_subject ?? null;

        $subjectUnitType = $newCourseSubject->subject_type ?? SiteConstants::ELECTIVETEXT;

        $subjectLegacyData = $newCourseSubject->toArray();
        $subjectLegacyData['college_id'] = $college_id;
        $subjectLegacyData['course_type'] = $courseTypeId;
        $subjectLegacyData['subject_type'] = $courseTypeId;
        $subjectLegacyData['unit_type'] = $subjectUnitType;

        $syncedSubjectUpdated = $syncedForUpdated = null;
        $subjectData = $legacyCourseSubject = null;
        /*
        Sync/Update the subject data (rto_subjects)
        */
        if ($syncedSubject) {
            $subjectData = Subject::find($syncedSubject);
            if ($subjectData) {
                $subjectData->updateQuietly($subjectLegacyData);
            }
            $subjectLegacyData['subject_id'] = $syncedSubject;
        }
        if (! $subjectData && $insert) {
            $subjectData = Subject::where(['subject_code' => $newCourseSubject->subject_code])->first();
            if ($subjectData && $subjectData->id) {
                $subjectData->updateQuietly($subjectLegacyData);
            } else {
                $subjectData = new Subject($subjectLegacyData);
                $subjectData->saveQuietly();
            }
            $syncedSubjectUpdated = $subjectData->id ?? null;
            $subjectLegacyData['subject_id'] = $syncedSubjectUpdated;
        }
        // dd($subjectData->subjectUnits);
        if ($syncUnits && $syncedSubjectUpdated) {
            $subjectUnits = $newCourseSubject->subject_units ?? [];
            foreach ($subjectUnits as $unit) {
                self::syncUnitToOld($unit, $syncedSubjectUpdated);
            }
        }

        if ($syncedFor) {
            $legacyCourseSubject = CourseSubject::find($syncedFor);
            if ($legacyCourseSubject) {
                $legacyCourseSubject->updateQuietly($subjectLegacyData);
            }
        }
        if (! $legacyCourseSubject && $insert) {
            $legacyCourseSubject = CourseSubject::where(['course_id' => $subjectLegacyData['course_id'], 'subject_id' => $subjectLegacyData['subject_id']])->first();
            if ($legacyCourseSubject && $legacyCourseSubject->id) {
                $legacyCourseSubject->updateQuietly($subjectLegacyData);
            } else {
                $legacyCourseSubject = new CourseSubject($subjectLegacyData);
                $legacyCourseSubject->saveQuietly();
            }
            $syncedForUpdated = $legacyCourseSubject->id;
        }

        if ($syncedSubjectUpdated || $syncedForUpdated) {
            if ($syncedSubjectUpdated) {
                $newCourseSubject->synced_subject = $syncedSubjectUpdated;
            }
            if ($syncedForUpdated) {
                $newCourseSubject->synced_for = $syncedForUpdated;
            }
            $newCourseSubject->saveQuietly();
        }

        return $newCourseSubject;
    }

    public static function updateSubjectDataFromOld(Subject|oldSubject $subject)
    {
        $syncedSubject = CourseSubjects::where(['synced_subject' => $subject->id])->first();
        $changes = $subject->getChanges();
        $allowedKeys = [
            'subject_code',
            'subject_name',
            'grading_type',
            'max_marks_allow',
            'level',
            'credit_point',
            'is_long_indicator',
            'is_assessment',
            'discipline_broad_type',
            'discipline_narrow_type',
            'discipline_narrow_sub_type',
        ];
        $updatableChanges = array_intersect_key($changes, array_flip($allowedKeys));
        if (! empty($updatableChanges) && $syncedSubject) {
            $syncedSubject->updateQuietly($updatableChanges);
        }
    }

    public static function DeleteCourseSubjectFromNewSetup(CourseSubjects $courseSubject)
    {
        // dd($courseSubject);
        return false;
    }
}
