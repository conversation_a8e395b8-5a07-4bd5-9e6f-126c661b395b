<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CourseGeneralResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'course_type_id' => $this->course_type_id,
            'is_vet_synced' => $this->is_vet_synced ?? 0,
            'module_delivery' => (bool) $this->module_delivery,
            'is_superseded' => (bool) $this->is_superseded,
            'superseded_date' => ($this->superseded_date) ? date('m/d/Y', strtotime($this->superseded_date)) : null,
            'is_super_code' => $this->is_super_code,
            'national_code' => $this->national_code,
            'core_units' => $this->core_units ?? 0,
            'elective_units' => $this->elective_units ?? 0,
            'course_code' => $this->course_code,
            'cricos_code' => $this->cricos_code,
            'is_cricos_code' => $this->is_cricos_code,
            'course_name' => $this->course_name,
            'delivery_target' => $this->delivery_target,
            'qualification_prerequisite_id' => $this->qualification_prerequisite_id,
            'course_recognition_id' => $this->course_recognition_id,
            'level_of_education_id' => $this->level_of_education_id,
            'field_of_education_id' => $this->field_of_education_id,
            'ANZSCO_code' => (int) $this->ANZSCO_code,
            'total_nominal_hours' => $this->total_nominal_hours,
            'AVETMISS_Report' => $this->AVETMISS_Report,
            'qtac_course_id' => $this->qtac_course_id,
            'course_description' => $this->course_description ?? null,
            'course_summary' => $this->course_summary ?? null,
            'entry_requirements' => $this->entry_requirements ?? null,
            'identity_proof' => $this->identity_proof ?? null,
            'superseded_course' => $this->superseded_course ?? null,
            'core_units_number' => $this->core_units_number ?? 0,
            'elective_units_number' => $this->elective_units_number ?? 0,
            'activated_now' => $this->activated_now ?? 0,
            'major' => $this->major,
            'course_tags' => $this->getTags(),
            'cover_image' => ($this->id) ? $this->getCoverImageUrl() : '',
        ];
    }
    public function getTags(){
        $course_tags = $this->course_tags ?? "";
        $course_tags = explode(",", $course_tags);
        $course_tags = array_filter($course_tags, function ($tag) {
            return trim($tag) !== '';
        });
        return implode(", ", $course_tags);
    }
}
