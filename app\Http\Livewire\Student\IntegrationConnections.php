<?php

namespace App\Http\Livewire\Student;

use App\Model\v2\Agent;
use App\Model\v2\Courses;
use App\Model\v2\Student;
use App\Users;
use Domains\Moodle\DTO\SyncParams;
use Domains\Moodle\Jobs\SyncCourseToMoodle;
use Domains\Moodle\Jobs\SyncUserInfoFromMoodle;
use Domains\Moodle\Jobs\SyncUserToMoodle;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Domains\Xero\Facades\Xero;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Integrations\Base\Models\IntegrationConfig;
use Integrations\Base\Models\IntegrationItem;
use Integrations\Zoho\Facades\Zoho;
use Integrations\Zoho\Jobs\SyncStudentDocumentToZoho;
use Integrations\Zoho\Traits\ZohoTrait;
use Livewire\Component;
use SSO\Events\IdentityCreated;
use SSO\Jobs\SyncUserToKeycloak;
use SSO\Traits\SSOStatusTrait;
use Support\Traits\LivewireAlert;

class IntegrationConnections extends Component
{
    use LivewireAlert;
    use MoodleStatusTrait;
    use SSOStatusTrait;
    use ZohoTrait;

    public $studentId;

    public $courseId;

    public $agentId;

    // External service connection statuses
    public $xeroConnect = false;

    public $zohoConnect = false;

    public $moodleConnect = false;

    public $ssoConnect = false;

    // External service data
    public $xeroData = [];

    public $zohoData = [];

    public $moodleData = [];

    public $ssoData = [];

    // Currently selected integration for details view
    public $activeIntegration = null;

    // Modal state
    public $showConfirmModal = false;

    public $confirmModalTitle = 'Confirmation';

    public $confirmModalMessage = '';

    public $confirmModalAction = '';

    protected $listeners = [
        // Confirmation actions
        'confirmSyncStudentToXero' => 'confirmSyncStudentToXero',
        'confirmReSyncStudentToXero' => 'confirmReSyncStudentToXero',
        'confirmSyncAgentToXero' => 'confirmSyncAgentToXero',
        'confirmReSyncAgentToXero' => 'confirmReSyncAgentToXero',
        'confirmSyncStudentToZoho' => 'confirmSyncStudentToZoho',
        'confirmSyncAgentToZoho' => 'confirmSyncAgentToZoho',
        'confirmSyncStudentToMoodle' => 'confirmSyncStudentToMoodle',
        'confirmSyncCourseToMoodle' => 'confirmSyncCourseToMoodle',
        'confirmConnectStudentToSSO' => 'confirmConnectStudentToSSO',

        // Modal open methods
        'openSyncStudentToXeroModal' => 'openSyncStudentToXeroModal',
        'openReSyncStudentToXeroModal' => 'openReSyncStudentToXeroModal',
        'openSyncAgentToXeroModal' => 'openSyncAgentToXeroModal',
        'openReSyncAgentToXeroModal' => 'openReSyncAgentToXeroModal',
        'openSyncStudentToZohoModal' => 'openSyncStudentToZohoModal',
        'openSyncAgentToZohoModal' => 'openSyncAgentToZohoModal',
        'openSyncStudentToMoodleModal' => 'openSyncStudentToMoodleModal',
        'openSyncCourseToMoodleModal' => 'openSyncCourseToMoodleModal',
        'openSsoConfirmModal' => 'openSsoConfirmModal',
    ];

    public function toggleIntegration($integration)
    {
        if ($this->activeIntegration === $integration) {
            $this->activeIntegration = null;
        } else {
            $this->activeIntegration = $integration;
        }
    }

    public function toggleModal($show = null)
    {
        $this->showConfirmModal = $show !== null ? $show : ! $this->showConfirmModal;
    }

    public function openConfirmModal($title, $message, $action)
    {
        $this->confirmModalTitle = $title;
        $this->confirmModalMessage = $message;
        $this->confirmModalAction = $action;
        $this->toggleModal(true);
    }

    public function mount($studentId, $courseId = null, $agentId = null)
    {
        $this->studentId = $studentId;
        $this->courseId = $courseId;
        $this->agentId = $agentId;

        $this->loadExternalServiceData();
    }

    public function loadExternalServiceData()
    {
        $student = Student::find($this->studentId);
        $agent = $this->agentId ? Agent::find($this->agentId) : ($student->agent_id ? Agent::find($student->agent_id) : null);
        $course = $this->courseId ? Courses::find($this->courseId) : null;

        // Check connections
        $this->xeroConnect = Xero::isConnected();
        $this->zohoConnect = $this->isZohoConnected();
        $this->moodleConnect = $this->isMoodleConnected();
        $this->ssoConnect = $this->isSSOConnected();

        // Load data for connected services
        if ($this->xeroConnect) {
            $this->loadXeroData($student, $agent);
        }

        if ($this->zohoConnect) {
            $this->loadZohoData($student, $agent);
        }

        if ($this->moodleConnect) {
            $this->loadMoodleData($student, $course);
        }

        if ($this->ssoConnect) {
            $this->loadSsoData($student);
        }
    }

    public function loadXeroData($student, $agent = null)
    {
        $this->xeroData = $this->getXeroData($student, $agent);
    }

    public function loadXeroStudentData($student)
    {
        $xeroData = $this->xeroData ?? [];
        $studentXeroContact = $student->xeroContact;
        $xeroData['student_xero_id'] = $studentXeroContact ? $studentXeroContact->xero_contact_id : null;
        $xeroData['student_xero_synced_at'] = $studentXeroContact ? $this->dateConvert($studentXeroContact->updated_at) : '';
        $xeroData['student_xero_failed_at'] = $studentXeroContact ? $this->dateConvert($studentXeroContact->xero_failed_at) : '';
        $xeroData['student_xero_failed_message'] = $studentXeroContact ? $studentXeroContact->xero_failed_message : '';

        $this->xeroData = $xeroData;
    }

    public function loadXeroAgentData($agent)
    {
        if (! $agent) {
            return;
        }

        $xeroData = $this->xeroData ?? [];
        $agentXeroContact = $agent->xeroContact;
        $xeroData['agent_xero_id'] = $agentXeroContact ? $agentXeroContact->xero_contact_id : null;
        $xeroData['agent_xero_synced_at'] = $agentXeroContact ? $this->dateConvert($agentXeroContact->updated_at) : '';
        $xeroData['agent_xero_failed_at'] = $agentXeroContact ? $this->dateConvert($agentXeroContact->xero_failed_at) : '';
        $xeroData['agent_xero_failed_message'] = $agentXeroContact ? $agentXeroContact->xero_failed_message : '';

        $this->xeroData = $xeroData;
    }

    public function loadZohoData($student, $agent = null)
    {
        $this->zohoData = $this->getZohoData($student, $agent);
    }

    public function loadZohoStudentData($student)
    {
        $zohoData = $this->zohoData ?? [];

        // Keep organization data
        if (! isset($zohoData['zoho_organization_name'])) {
            $orgData = IntegrationConfig::first();
            $zohoData['zoho_organization_name'] = $orgData ? $orgData->name : '';
            $zohoData['zoho_time_zone'] = $orgData ? $orgData->timezone : '';
            $zohoData['zoho_employee_count'] = $orgData ? $orgData->employee_count : '';
        }

        // Update student data
        $studentZohoItem = $student->zohoItem;
        $zohoData['student_zoho_id'] = $studentZohoItem ? $studentZohoItem->zoho_id : null;
        $zohoData['student_zoho_synced_at'] = $studentZohoItem && $studentZohoItem->synced_at ? $this->dateConvert($studentZohoItem->synced_at) : '';
        $zohoData['student_zoho_failed_at'] = $studentZohoItem && $studentZohoItem->sync_failed_at ? $this->dateConvert($studentZohoItem->sync_failed_at) : '';
        $zohoData['student_zoho_failed_message'] = $studentZohoItem && $studentZohoItem->sync_failed_message ? $studentZohoItem->sync_failed_message : '';

        $this->zohoData = $zohoData;
    }

    public function loadZohoAgentData($agent)
    {
        if (! $agent) {
            return;
        }

        $zohoData = $this->zohoData ?? [];

        // Keep organization data
        if (! isset($zohoData['zoho_organization_name'])) {
            $orgData = IntegrationConfig::first();
            $zohoData['zoho_organization_name'] = $orgData ? $orgData->name : '';
            $zohoData['zoho_time_zone'] = $orgData ? $orgData->timezone : '';
            $zohoData['zoho_employee_count'] = $orgData ? $orgData->employee_count : '';
        }

        // Update agent data
        $agentZohoItem = $agent->zohoItem;
        $zohoData['agent_zoho_id'] = $agentZohoItem ? $agentZohoItem->zoho_id : null;
        $zohoData['agent_zoho_synced_at'] = $agentZohoItem && $agentZohoItem->synced_at ? $this->dateConvert($agentZohoItem->synced_at) : '';
        $zohoData['agent_zoho_failed_at'] = $agentZohoItem && $agentZohoItem->sync_failed_at ? $this->dateConvert($agentZohoItem->sync_failed_at) : '';
        $zohoData['agent_zoho_failed_message'] = $agentZohoItem && $agentZohoItem->sync_failed_message ? $agentZohoItem->sync_failed_message : '';

        $this->zohoData = $zohoData;
    }

    public function loadMoodleData($student, $course = null)
    {
        $this->moodleData = $this->getMoodleData($student, $course);
    }

    public function loadMoodleStudentData($student)
    {
        $moodleData = $this->moodleData ?? [];

        if ($student?->associatedUserAccount?->id) {
            $user = Users::findOrFail($student->associatedUserAccount->id);
            $studentData = $this->setMoodleSyncedResponseData('student', $user->moodleItem);

            // Update only student-related data in moodleData
            foreach ($studentData as $key => $value) {
                $moodleData[$key] = $value;
            }
        }

        $this->moodleData = $moodleData;
    }

    public function loadMoodleCourseData()
    {
        $moodleData = $this->moodleData ?? [];

        if (! empty($this->courseId)) {
            $course = Courses::findOrFail($this->courseId);
            $courseData = $this->setMoodleSyncedResponseData('course', $course->moodleItem);

            // Update only course-related data in moodleData
            foreach ($courseData as $key => $value) {
                $moodleData[$key] = $value;
            }
        }

        $this->moodleData = $moodleData;
    }

    public function loadSsoData($student)
    {
        $this->ssoData = $this->getSSOData($student);
    }

    // Modal open methods for different integrations
    public function openSyncStudentToXeroModal()
    {
        $this->openConfirmModal('Sync Student', 'Are you sure you want to sync this student to Xero?', 'confirmSyncStudentToXero');
    }

    public function openReSyncStudentToXeroModal()
    {
        $this->openConfirmModal('Re-Sync Student', 'Are you sure you want to Re-sync this student to Xero?', 'confirmReSyncStudentToXero');
    }

    public function openSyncAgentToXeroModal()
    {
        $this->openConfirmModal('Sync Agent', 'Are you sure you want to sync this agent to Xero?', 'confirmSyncAgentToXero');
    }

    public function openReSyncAgentToXeroModal()
    {
        $this->openConfirmModal('Re-Sync Agent', 'Are you sure you want to Re-sync this agent to Xero?', 'confirmReSyncAgentToXero');
    }

    public function openSyncStudentToZohoModal()
    {
        $this->openConfirmModal('Sync Student', 'Are you sure you want to sync this student to Zoho?', 'confirmSyncStudentToZoho');
    }

    public function openSyncAgentToZohoModal()
    {
        $this->openConfirmModal('Sync Agent', 'Are you sure you want to sync this agent to Zoho?', 'confirmSyncAgentToZoho');
    }

    public function openSyncStudentToMoodleModal()
    {
        $this->openConfirmModal('Sync Student', 'Are you sure you want to sync this student to Moodle?', 'confirmSyncStudentToMoodle');
    }

    public function openSyncCourseToMoodleModal()
    {
        $this->openConfirmModal('Sync Course', 'Are you sure you want to sync this course to Moodle?', 'confirmSyncCourseToMoodle');
    }

    public function openSsoConfirmModal()
    {
        $this->openConfirmModal('Connect with SSO', 'Are you sure you want to connect via SSO?', 'confirmConnectStudentToSSO');
    }

    /* Sync to XERO */
    public function confirmSyncStudentToXero()
    {
        if (! $this->xeroConnect) {
            $this->alert('error', 'Xero not connected');
            $this->toggleModal(false);

            return;
        }

        $student = Student::find($this->studentId);
        if (! $student) {
            $this->alert('error', 'No student found to sync');
            $this->toggleModal(false);

            return;
        }

        try {
            $student->asXeroContact();
            $this->loadXeroStudentData($student);
            $this->alert('success', 'Student successfully synced to Xero');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync student to Xero: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    public function confirmReSyncStudentToXero()
    {
        if (! $this->xeroConnect) {
            $this->alert('error', 'Xero not connected');
            $this->toggleModal(false);

            return;
        }

        $student = Student::find($this->studentId);
        if (! $student) {
            $this->alert('error', 'No student found to sync');
            $this->toggleModal(false);

            return;
        }

        if (! $student->isXeroContactCreatated()) {
            $this->alert('error', 'Student is not in xero');
            $this->toggleModal(false);

            return;
        }

        try {
            if ($student->isXeroContactCreatated()) {
                $xeroContact = $student->xeroContact;
                dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($xeroContact));
            }
            // $student->asXeroContact();
            $student->fresh()->xeroContact;
            $this->loadXeroStudentData($student);
            $this->alert('success', 'Student successfully synced to Xero');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync student to Xero: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    public function confirmSyncAgentToXero()
    {
        if (! $this->xeroConnect) {
            $this->alert('error', 'Xero not connected');
            $this->toggleModal(false);

            return;
        }

        $agent = $this->agentId ? Agent::find($this->agentId) : null;
        if (! $agent) {
            $this->alert('error', 'No agent found to sync');
            $this->toggleModal(false);

            return;
        }

        try {
            $agent->asXeroContact();
            $this->loadXeroAgentData($agent);
            $this->alert('success', 'Agent successfully synced to Xero');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync agent to Xero: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    public function confirmReSyncAgentToXero()
    {
        if (! $this->xeroConnect) {
            $this->alert('error', 'Xero not connected');
            $this->toggleModal(false);

            return;
        }

        $agent = $this->agentId ? Agent::find($this->agentId) : null;
        if (! $agent) {
            $this->alert('error', 'No agent found to sync');
            $this->toggleModal(false);

            return;
        }

        if (! $agent->isXeroContactCreatated()) {
            $this->alert('error', 'Agent is not in xero');
            $this->toggleModal(false);

            return;
        }

        try {
            if ($agent->isXeroContactCreatated()) {
                $xeroContact = $agent->xeroContact;
                dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($xeroContact));
            }
            $agent->fresh()->xeroContact;
            $this->loadXeroAgentData($agent);
            $this->alert('success', 'Agent successfully synced to Xero');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync agent to Xero: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    protected function getXeroData($student, $agent)
    {
        $xeroData = [];

        if ($student) {
            $studentXeroContact = $student->xeroContact;
            $xeroData['student_xero_id'] = $studentXeroContact ? $studentXeroContact->xero_contact_id : null;
            $xeroData['student_xero_synced_at'] = $studentXeroContact ? $this->dateConvert($studentXeroContact->updated_at) : '';
            $xeroData['student_xero_failed_at'] = $studentXeroContact ? $this->dateConvert($studentXeroContact->xero_failed_at) : '';
            $xeroData['student_xero_failed_message'] = $studentXeroContact ? $studentXeroContact->xero_failed_message : '';
            // $xeroData['synced_at'] = $studentXeroContact ? $studentXeroContact->updated_at->format('d M Y H:i') : ';
        }

        if ($agent) {
            $agentXeroContact = $agent->xeroContact;
            $xeroData['agent_xero_id'] = $agentXeroContact ? $agentXeroContact->xero_contact_id : null;
            $xeroData['agent_xero_synced_at'] = $agentXeroContact ? $this->dateConvert($agentXeroContact->updated_at) : '';
            $xeroData['agent_xero_failed_at'] = $agentXeroContact ? $this->dateConvert($agentXeroContact->xero_failed_at) : '';
            $xeroData['agent_xero_failed_message'] = $agentXeroContact ? $agentXeroContact->xero_failed_message : '';
        }

        return $xeroData;
    }

    /* Sync to ZOHO */
    public function confirmSyncStudentToZoho()
    {
        if (! $this->zohoConnect) {
            $this->alert('error', 'Zoho not connected');
            $this->toggleModal(false);

            return;
        }

        $student = Student::find($this->studentId);
        if (! $student) {
            $this->alert('error', 'No student found to sync');
            $this->toggleModal(false);

            return;
        }

        try {
            $student->asZohoItem(true);
            $res = $student->convertToZohoContact();
            if ($res && $res->sync_status == 1) {
                dispatch(new SyncStudentDocumentToZoho($student->id));
                /*if(Zoho::validateZohoConnectionAndSetup()){
                    dispatch_sync(new SyncStudentDocumentToZoho($student->id));
                }*/
            }

            $this->loadZohoStudentData($student);
            $this->alert('success', 'Student successfully synced to Zoho');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync student to Zoho: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    public function confirmSyncAgentToZoho()
    {
        if (! $this->zohoConnect) {
            $this->alert('error', 'Zoho not connected');
            $this->toggleModal(false);

            return;
        }

        $agent = $this->agentId ? Agent::find($this->agentId) : null;
        if (! $agent) {
            $this->alert('error', 'No agent found to sync');
            $this->toggleModal(false);

            return;
        }

        try {
            $agent->asZohoItem(true);
            $this->loadZohoAgentData($agent);
            $this->alert('success', 'Agent successfully synced to Zoho');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync agent to Zoho: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    protected function getZohoData($student, $agent)
    {
        $zohoData = [];

        $orgData = IntegrationConfig::first();

        $zohoStudent = IntegrationItem::where([
            'syncable_id' => $student->id,
            'syncable_type' => IntegrationItem::SYNCABLE_TYPE_STUDENT,
        ])->first();

        $zohoAgent = IntegrationItem::where([
            'syncable_id' => $agent->id,
            'syncable_type' => IntegrationItem::SYNCABLE_TYPE_AGENT,
        ])->first();

        return [
            'zoho_organization_name' => @$orgData['data']['organization']['company_name'] ?? null,
            'zoho_time_zone' => @$orgData['data']['organization']['time_zone'] ?? null,
            'zoho_employee_count' => @$orgData['data']['organization']['employee_count'] ?? 0,

            'student_zoho_id' => $zohoStudent ? $zohoStudent->integration_id : null,
            'student_zoho_synced_at' => $zohoStudent ? $zohoStudent->updated_at : null,
            'student_zoho_failed_at' => $zohoStudent ? $zohoStudent->sync_failed_at : null,
            'student_zoho_failed_message' => $zohoStudent->sync_failed_message ?? null,

            'agent_zoho_id' => $zohoAgent->sync_item_id ?? null,
            'agent_zoho_synced_at' => $zohoAgent ? $zohoAgent->synced_at : null,
            'agent_zoho_failed_at' => $zohoAgent ? $zohoAgent->sync_failed_at : null,
            'agent_zoho_failed_message' => $zohoAgent->sync_failed_message ?? null,

            // 'student_zoho_synced_at'        => (isset($zohoStudent) && !empty($zohoStudent->metadata)) ? $zohoStudent->updated_at->format('d M Y H:i') : null,
            // 'student_zoho_synced_at'        => $zohoStudent->data['item']['Created_Time'] ?? null,
            // 'agent_zoho_synced_at'          => $zohoAgent->data['item']['Created_Time'] ?? null,
        ];
    }

    /* Sync to MOODLE */
    public function confirmSyncStudentToMoodle()
    {
        if (! $this->moodleConnect) {
            $this->alert('error', 'Moodle not connected');
            $this->toggleModal(false);

            return;
        }

        $student = Student::find($this->studentId);
        if (! $student) {
            $this->alert('error', 'No student found to sync');
            $this->toggleModal(false);

            return;
        }

        if (! $student->associatedUserAccount) {
            $this->alert('error', 'Student does not have associated login account');
            $this->toggleModal(false);

            return;
        }

        $user = Users::findOrFail($student->associatedUserAccount->id);
        if (! $user) {
            $this->alert('error', 'User data not found');
            $this->toggleModal(false);

            return;
        }

        try {
            dispatch(new SyncUserInfoFromMoodle($user->id));

            /* If we were able to find and link up the already existing user, just return the response. */
            if ($user->moodleItem()->first()) {
                $this->loadMoodleStudentData($student);
            } else {
                dispatch(new SyncUserToMoodle($user->id, true, true));
                $this->loadMoodleStudentData($student);
            }

            $this->alert('success', 'Student successfully synced to Moodle');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync student to Moodle: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    public function confirmSyncCourseToMoodle()
    {
        if (! $this->moodleConnect) {
            $this->alert('error', 'Moodle not connected');
            $this->toggleModal(false);

            return;
        }

        $course = $this->courseId ? Courses::find($this->courseId) : null;
        if (! $course) {
            $this->alert('error', 'No course found to sync');
            $this->toggleModal(false);

            return;
        }

        try {
            $courseMoodleItem = $course->moodleItem()->first();
            if (! $courseMoodleItem || ! $courseMoodleItem->getSyncId()) {
                // dispatch_sync(new SyncCourseToMoodle($course->id));
                dispatch_sync(new SyncCourseToMoodle(
                    $course->id,
                    true,
                    SyncParams::LazyFromArray([
                        'course_id' => $course->id,
                    ])
                ));
            }

            $this->loadMoodleCourseData();

            $this->alert('success', 'Course successfully synced to Moodle');
        } catch (\Exception $e) {
            $this->alert('error', 'Failed to sync course to Moodle: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    protected function getMoodleData($student)
    {
        $moodleData = [];

        if ($student?->associatedUserAccount?->id) {
            $user = Users::findOrFail($student->associatedUserAccount->id);
            $moodleData = array_merge($moodleData, $this->setMoodleSyncedResponseData('student', $user->moodleItem));
        }

        if (! empty($this->courseId)) {
            $course = Courses::findOrFail($this->courseId);
            $moodleData = array_merge($moodleData, $this->setMoodleSyncedResponseData('course', $course->moodleItem));
        }

        return $moodleData;
    }

    private function setMoodleSyncedResponseData($prefix, $moodleItem = [])
    {
        /* if (!$moodleItem) { return []; } */

        $syncField = $prefix === 'student' ? 'username' : 'id';
        $failed = (int) ($moodleItem && ! empty($moodleItem->sync_failed_at));
        // $synced = (int) ($moodleItem && empty($moodleItem->sync_failed_at) && !empty($moodleItem->synced_at));

        return [
            "{$prefix}_moodle_sync_id" => data_get($moodleItem, "data.item.$syncField") ?? null,
            "{$prefix}_sync_item_id" => $moodleItem && $moodleItem->sync_item_id ? $moodleItem->sync_item_id : null,
            "{$prefix}_moodle_synced_at" => $moodleItem && $moodleItem->synced_at ? $this->dateConvert($moodleItem->synced_at) : '',
            "{$prefix}_moodle_failed_at" => $moodleItem && $moodleItem->sync_failed_at ? $this->dateConvert($moodleItem->sync_failed_at) : '',
            "{$prefix}_moodle_failed_message" => $failed == 1 ? ($moodleItem->sync_failed_message ?? 'Something went wrong') : '',
            // "{$prefix}CC"    => $synced,
        ];
    }

    /* Connect to SSO */
    public function confirmConnectStudentToSSO()
    {
        if (! $this->ssoConnect) {
            $this->alert('error', 'SSO Not Allowed');
            $this->toggleModal(false);

            return;
        }

        if (! $this->studentId) {
            $this->alert('error', 'Student ID missing');
            $this->toggleModal(false);

            return;
        }

        $student = Student::findOrFail($this->studentId);
        if (! $student) {
            $this->alert('error', 'No student found to connect');
            $this->toggleModal(false);

            return;
        }

        if (! $student->associatedUserAccount) {
            $this->alert('error', 'Student does not have associated login account');
            $this->toggleModal(false);

            return;
        }

        $user = Users::findOrFail($student->associatedUserAccount->id);
        if (! $user) {
            $this->alert('error', 'User data not found');
            $this->toggleModal(false);

            return;
        }

        $name = $user->first_last_name;
        $userAr = [
            'firstName' => @$name[0],
            'lastName' => @$name[1],
            'email' => $user->email,
            'password' => Str::random(8),
            'temporaryPassword' => true,
            'notify' => true,
            'user_id' => $user->id,
        ];

        try {
            Log::info('identity creating', $userAr);
            dispatch_sync(new SyncUserToKeycloak(IdentityCreated::FromArray($userAr)));

            $this->alert('success', 'Student successfully connected to SSO');
            $this->loadSsoData($student);
        } catch (\Exception $e) {
            Log::info('Failed to create identity', $userAr);
            $this->alert('error', 'Failed to connect student to SSO: '.$e->getMessage());
        }

        // Close the modal regardless of success or failure
        $this->toggleModal(false);
    }

    protected function getSSOData($student)
    {
        $ssoData = [];

        if ($student) {
            $user = $student->associatedUserAccount;
            if ($user && method_exists($user, 'getSSSOId') && $user->getSSSOId()) {
                $ssoData = $user->sso_data ?? [];
                $ssoData['sso_id'] = $ssoData['sso_id'] ?? null;
                $ssoData['synced_at'] = isset($ssoData['synced_at']) ? $this->dateConvert($ssoData['synced_at']) : null;
            }
        }

        return $ssoData;
    }

    protected function dateConvert($dateVal = null)
    {
        return (isset($dateVal) && ! empty($dateVal)) ? \Carbon\Carbon::parse($dateVal)->diffForHumans() : '';
    }

    public function render()
    {
        return view('livewire.student.integration-connections');
    }
}
