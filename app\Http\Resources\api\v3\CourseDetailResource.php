<?php

namespace App\Http\Resources\api\v3;

use stdClass;
use Carbon\Carbon;
use App\Model\v2\Courses;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Model\v2\CourseType;
use Illuminate\Support\Facades\Auth;
use App\Classes\CoursesApiController;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $currency = config("cashier.currency");
        $gst = 1;
        $gstText = ($gst == 1) ? "including GST" : "without GST";

        //return parent::toArray($request);
        return [
            'id' => $this->id,
            'course_type_id' => $this->course_type_id,
            'course_type_text' => $this->whenLoaded('courseType', function () {
                                        return $this->courseType->title;
                                    }),
            'module_delivery' => (bool) $this->module_delivery,
            'is_superseded' => (bool) $this->is_superseded,
            'superseded_date' => ($this->superseded_date) ? date('m/d/Y', strtotime($this->superseded_date)) : null,
            'national_code' => $this->national_code,
            'course_code' => $this->course_code,
            'cricos_code' => $this->cricos_code,
            'course_name' => $this->course_name,
            'delivery_target' => $this->delivery_target,
            'delivery_target_text' => $this->getDelivertTargetText($this->delivery_target),
            'qualification_prerequisite_id' => $this->qualification_prerequisite_id,
            'prerequisite_text' => $this->whenLoaded('courseSubjects', function () {
                                    return optional($this->prerequisite)->course_code ?? $this->getPreRequisiteText($this->qualification_prerequisite_id);
                                }),
            'course_duration' => (int) $this->course_duration,
            'couse_duration_type' => $this->couse_duration_type,
            'duration_text' => $this->getDurationTypeText($this->course_duration, $this->couse_duration_type),
            'study_mode' => $this->getStudyMode(),
            'online_hours' => (float) $this->online_hours,
            'face_to_face_hours' =>  (float) $this->face_to_face_hours,
            'tuition_fee' =>  (float) $this->tuition_fee,
            'domestic_fee' =>  (float) $this->domestic_fee,
            'fee_target_text' => $this->getFeeTargetText($this->delivery_target),
            'fee_text' => $this->getFeeText($this->delivery_target, $this->tuition_fee, $this->domestic_fee),
            'fee_currency' => $currency,
            'gst_included' => $gst,
            'fee_currency_gst' => implode(", ", [strtoupper($currency), $gstText]),
            'maximum_weekly_study' =>  (float) $this->maximum_weekly_study,
            'effective_start' => ($this->effective_start) ? date('m/d/Y', strtotime($this->effective_start)) : null,
            'effective_end' => ($this->effective_start) ? date('m/d/Y', strtotime($this->effective_end)) : null,
            'campus_list' => $this->campus_list,
            'results_calculation_methods' => $this->results_calculation_methods,
            'work_placement' => $this->work_placement,
            'work_placement_hour' => $this->work_placement_hour,
            'work_placement_text' => $this->getWorkPlacementText($this->work_placement_hour, $this->work_placement_component_type),
            'course_delivery_type' => $this->course_delivery_type,
            'placement_officer_id' => $this->placement_officer_id,
            'vocational_duration' => $this->vocational_duration,
            'vocational_startweek' => $this->vocational_startweek,
            'course_level' => $this->couse_level,
            'activated_now' => ($this->activated_now == 2) ? 0 : $this->activated_now,
            'flexible_attendance' => $this->flexible_attendance,
            'fee_help' => $this->fee_help,
            'free_help_study_type' => $this->free_help_study_type,
            'free_help_study_load' => $this->free_help_study_load,
            'is_tcsi' => $this->is_tcsi,
            'course_completion_type' => $this->course_completion_type,
            'course_credit_point' => $this->course_credit_point,
            'major' => $this->major,
            'faculty_id' => $this->faculty_id,
            'department_id' => $this->department_id,
            'course_recognition_id' => (int) $this->course_recognition_id,
            'level_of_education_id' => (int) $this->level_of_education_id,
            'field_of_education_id' => (int) $this->field_of_education_id,
            'ANZSCO_code' => (int) $this->ANZSCO_code,
            'total_nominal_hours' => $this->total_nominal_hours,
            'total_hours_text' => $this->total_nominal_hours,
            'AVETMISS_Report' => $this->AVETMISS_Report,
            'qtac_course_id' => $this->qtac_course_id,
            'course_description' => $this->course_description,
            'course_summary' => $this->course_summary,
            'entry_requirements' => $this->entry_requirements,
            'identity_proof' => $this->identity_proof,
            'is_fee_recurring' => $this->is_fee_recurring,
            'fee_recurring_type' => ($this->is_fee_recurring) ? "per {$this->fee_recurring_type}" : 'Full Course',
            'is_seat_fixed' => $this->is_seat_fixed,
            'seat_limit' => ($this->is_seat_fixed) ? $this->seat_limit : "Unlimited",
            'has_all_information' => $this->checkInfo(),
            'cover_image' => ($this->id) ? $this->getCoverImageUrl() : '',
            'superseededinfo' => $this->when($this->course_type_id == config('constants.vet_course_type_id'), function () {
                    if($this->apicache) {
                        /* the particular vet course has the cached api data in local database */
                        $apiData = ($this->apicache) ? $this->apicache->detail->detail_response ?? null : null;
                        $jsonData = objectToArray(json_decode($apiData)) ?? [];
                        $apiData = $jsonData["GetDetailsResult"] ?? [];
                        return $this->prepareApiResultData($apiData);
                    }else{
                        /* the particular vet course do not have the data cached or the cache data is expired so get the new data from the api */
                        return $this->getApiInformation();
                    }
            }),
            'campuses' => $this->whenLoaded('campuses', function () {
                return CourseCampusResource::collection($this->campuses);
            }),
            /* will give the short names of locations of different campuses the course is available in */
            'campuses_location' => $this->whenLoaded('campuses', function () {
                return $this->getCampusesShortNames($this->campuses);
            }),
            'intakes' => $this->whenLoaded('intakes', function () {
                return CourseIntakeResource::collection(
                    collect($this->intakes)->map(function ($intake) {
                        $intake['delivery_target'] = $this->delivery_target;
                        $intake['delivery_target_text'] = $this->getDelivertTargetText($this->delivery_target);
                        $intake['study_mode'] = $this->getStudyMode();
                        $intake['online_hours'] = (float) $this->online_hours;
                        $intake['face_to_face_hours'] = (float) $this->face_to_face_hours;
                        $intake['tuition_fee'] = (float) $this->tuition_fee;
                        $intake['domestic_fee'] = (float) $this->domestic_fee;
                        $intake['fee_target_text'] = $this->getFeeTargetText($this->delivery_target);
                        $intake['fee_text'] = $this->getFeeText($this->delivery_target, $this->tuition_fee, $this->domestic_fee);
                        return $intake;
                    })
                );
            }),
            'related_courses' => $this->whenLoaded('related_courses', function () {
                return CourseRelatedCoursesResource::collection($this->related_courses);
            }),
        ];
    }
    private function getCampusesShortNames($campuses){
        if(empty($campuses)) return null;
        $locations = [];
        foreach($campuses as $val){
            $name = @$val->campus->name ?? null;
            if(!$name) continue;
            $shortname = Str::upper(substr($name, 0, 3));
            $locations[] = ["short_name" => $shortname, "name" => $name];
        }
        return $locations;
    }
    private function getDurationTypeText($duration = 0, $type = 0)
    {
        $durations = config('constants.arrCourseDurationType');
        $typeText = "Week"; //default to week
        if ($type) {
            $typeText = $durations[$type] ?? $typeText;
        }
        $duration = (int) $duration;
        return implode(" ", array($duration, Str::plural($typeText, $duration)));
    }
    private function getDelivertTargetText($target)
    {
        $targets = config('constants.arrDeliveryTarget');
        $typeText = "Both"; //default to week
        if ($target) {
            $typeText = $targets[$target] ?? $typeText;
        }
        return $typeText;
    }
    private function getFeeTargetText($target = "")
    {
        if ($target == "Both") {
            $text = "International | Domestic";
        } else {
            $text = ''; //Str::ucfirst($target);
        }
        return $text;
    }
    private function getFeeText($target = "", $tuitionFee = 0, $domesticFee = "")
    {
        $target = Str::lower($target);
        if ($target == "domestic") {
            $price = "$" . $domesticFee;
        } else if ($target == "international") {
            $price = "$" . $tuitionFee;
        } else {
            $price = "$" . $tuitionFee . " | " . "$" . $domesticFee;
        }
        return $price;
    }
    private function getStudyMode(){
        $hasOnline = (float) ($this->online_hours ?? 0);
        $hasFaceToFace = (float) ($this->face_to_face_hours ?? 0);
        if($hasOnline > 0 && $hasFaceToFace){
            return "Hybrid";
        }else if($hasOnline > 0){
            return "Online";
        }else{
            return "Face To Face";
        }
    }
    private function getPreRequisiteText($prerequisite = '')
    {
        $arrQualifictionLevelList = config('constants.arrQualifictionLevelList');
        return $arrQualifictionLevelList[$prerequisite] ?? "N/A";
    }
    private function getCourseTypeText($type = '')
    {
        $college_id = Auth::user()->college_id;
        $collegeFilter = array(0);
        if ($college_id > 0) {
            $collegeFilter[] = $college_id;
        }
        $courseType = CourseType::WhereIn('college_id', $collegeFilter)
            ->where("id", $type)
            ->select('id', 'title')->first();
        return $courseType["title"] ?? "Not Specified";
    }
    private function checkInfo()
    {
        //the courses those do not have complete information will have status 2
        if($this->activated_now == 2) return false;
        //for old setup
        if (
            !$this->course_type_id ||
            !$this->course_code ||
            !$this->course_name ||
            !$this->delivery_target ||
            !$this->course_duration ||
            !($this->tuition_fee || $this->domestic_fee) ||
            !$this->maximum_weekly_study ||
            !$this->campus_list ||
            !$this->results_calculation_methods
        )
            return false;
        else
            return true;
    }
    private function getApiInformation()
    {
        if (empty($this->course_code)) {
            return null;
        }
        $coursesApi = new CoursesApiController();
        $resArray = array();
        $courseDetail = $coursesApi->nationCodeApiCall($this->course_code);
        //api result data
        $apiData = $courseDetail["GetDetailsResult"] ?? [];
        return $this->prepareApiResultData($apiData);
    }
    private function prepareApiResultData($apiData){
        $existingCourse = new stdClass();
        if ($apiData) { //reset the values if data received from the api
            $existingCourse->course_code = $apiData["Code"] ?? "";
            $existingCourse->course_name = $apiData["Title"] ?? "";
            $classifications = $apiData["Classifications"]["Classification"] ?? [];
            for ($i = 0; $i < count($classifications); $i++) {
                $clsfData = $classifications[$i] ?? [];
                $schemeCode = $clsfData["SchemeCode"] ?? null;
                $valueCode = $clsfData["ValueCode"] ?? null;
                if ($schemeCode == 01) {
                    $existingCourse->ANZSCO_code = $valueCode;
                } else if ($schemeCode == 04) {
                    $existingCourse->field_of_education_id = $valueCode;
                } else if ($schemeCode == 05) {
                    $existingCourse->level_of_education_id = $valueCode;
                } else if ($schemeCode == 06) {
                    $existingCourse->course_recognition_id = $valueCode;
                }
            }
            //$outputArray["anzscocode"] = $result["GetDetailsResult"]["Classifications"]["Classification"][0]["ValueCode"];
            $existingCourse->total_nominal_hours = $apiData["CreatedDate"]["OffsetMinutes"] ?? null;
            $existingCourse->is_superseded = !(($apiData["CurrencyStatus"] ?? '') == "Current");
            $superSeededDate = $apiData["CurrencyPeriods"]["NrtCurrencyPeriod"]["EndDate"] ?? null;
            $superSeededDate = ($superSeededDate) ? Carbon::createFromFormat('Y-m-d', $superSeededDate)->format('M d Y') : '';
            $existingCourse->superseded_date = $superSeededDate;
            $existingCourse->superseded_course = $apiData["ReverseMappingInformation"]["Mapping"] ?? null;
        }
        return $existingCourse;
    }
    private function getWorkPlacementText($workPlacementHour = 0, $workPlacementText = ""){
        $type = config('constants.arrWorkPlacementComponentTypes');
        $type = $type[$workPlacementText] ?? "";
        if($workPlacementHour > 0){
            return "{$workPlacementHour} {$type}";
        }else{
            return "N/A";
        }
    }
}
