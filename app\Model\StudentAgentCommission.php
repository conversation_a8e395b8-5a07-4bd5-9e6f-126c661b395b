<?php

namespace App\Model;

use DB;
use Auth;
use App\Model\StudentCourse;
use Illuminate\Database\Eloquent\Model;
use Domains\Xero\Traits\XeroableInvoice;

class StudentAgentCommission extends Model {
    use XeroableInvoice;

    protected $table = 'rto_student_agent_commission';

    public function saveStudentAgentCommission($request, $transactionNumber, $commissionFormat, $agentId, $gstField, $student_course_id, $invoiceNumber=0) {

        $collegeId = Auth::user()->college_id;
        $studentId = $request->input('student_id');

        $objStudentAgentCommission = new StudentAgentCommission();

        $objStudentAgentCommission->college_id = $collegeId;
        $objStudentAgentCommission->student_id = $studentId;
        $objStudentAgentCommission->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
        $objStudentAgentCommission->student_course_id = $student_course_id;
        $objStudentAgentCommission->transaction_no = $transactionNumber;
        $objStudentAgentCommission->invoice_no = $invoiceNumber;
        $objStudentAgentCommission->agent_id = $agentId;
        $objStudentAgentCommission->commission_payable = $commissionFormat;
        $objStudentAgentCommission->GST = $gstField;
        if ($gstField == "GST") {
            $objStudentAgentCommission->gst_amount = number_format($commissionFormat * 10 / 100, 2, '.', '');
        } else {
            $objStudentAgentCommission->gst_amount = 0;
        }

        $objStudentAgentCommission->commission_paid = 0;

        $objStudentAgentCommission->CHQ_NO = 0;
        $objStudentAgentCommission->paid_date = null;
        $objStudentAgentCommission->comm_to_refund = 0;
        $objStudentAgentCommission->GST_to_refund = 0;
        $objStudentAgentCommission->refund_amount = 0;
        $objStudentAgentCommission->remarks = ($request->input('comment') != '') ? $request->input('comment') : null;
        $objStudentAgentCommission->is_commission_deducted = ($request->input('agent_commission_deduct') == true) ? 1 : 0;
        $objStudentAgentCommission->paid_date = ($request->input('payment_date') != "") ? date('Y-m-d', strtotime($request->input('payment_date'))) : null;

        if ($request->input('provide_agent_bonus') == true) {
            $objStudentAgentCommission->bonus_amount = ($request->input('applied_commission_text') != '') ? $request->input('applied_commission_text') : 0;
            $objStudentAgentCommission->bonus_gst = ($request->input('bonus_gst') != '') ? $request->input('bonus_gst') : null;
        }

        if ($request->input('agent_commission_deduct') == true) {
            $objStudentAgentCommission->commission_paid = $objStudentAgentCommission->commission_payable + $objStudentAgentCommission->gst_amount;
            $objStudentAgentCommission->is_approved = 1;
            $objStudentAgentCommission->is_process = 1;

            //if "Agent Commission is Deducted" is checked then mode should be "'1'=>Agent Deducted"
            $objStudentAgentCommission->mode = 1;
        } else {
            $objStudentAgentCommission->mode = ($request->input('payment_mode') != '') ? $request->input('payment_mode') : null;
        }

        $objStudentAgentCommission->created_by = Auth::user()->id;
        $objStudentAgentCommission->updated_by = Auth::user()->id;
        $objStudentAgentCommission->save();
    }

    public function saveStudentAgentCommissionOfferManage($getStudentDetails, $getInitialPaymentData,  $transactionNumber, $commissionFormat, $agentId, $gstField) {

        $objStudentAgentCommission = new StudentAgentCommission();

        $objStudentAgentCommission->college_id = $getInitialPaymentData[0]->college_id;
        $objStudentAgentCommission->student_id = $getInitialPaymentData[0]->student_id;
        $objStudentAgentCommission->course_id = $getStudentDetails->course_id;
        $objStudentAgentCommission->student_course_id = $getInitialPaymentData[0]->student_course_id;
        $objStudentAgentCommission->transaction_no = $transactionNumber;
        $objStudentAgentCommission->agent_id = $agentId;
        $objStudentAgentCommission->commission_payable = $commissionFormat;
        $objStudentAgentCommission->GST = $gstField;
        if ($gstField == "GST") {
            $objStudentAgentCommission->gst_amount = number_format($commissionFormat * 10 / 100, 2, '.', '');
        } else {
            $objStudentAgentCommission->gst_amount = 0;
        }

        $objStudentAgentCommission->commission_paid = 0;

        $objStudentAgentCommission->CHQ_NO = 0;
        $objStudentAgentCommission->paid_date = null;
        $objStudentAgentCommission->comm_to_refund = 0;
        $objStudentAgentCommission->GST_to_refund = 0;
        $objStudentAgentCommission->refund_amount = 0;
        // $objStudentAgentCommission->remarks = ($request->input('comment') != '') ? $request->input('comment') : null;
        $objStudentAgentCommission->is_commission_deducted = 0;
        $objStudentAgentCommission->paid_date = date('Y-m-d', strtotime(date('Y-m-d')));

        // if ($request->input('provide_agent_bonus') == true) {
            $objStudentAgentCommission->bonus_amount = 0;
        //     $objStudentAgentCommission->bonus_gst = ($request->input('bonus_gst') != '') ? $request->input('bonus_gst') : null;
        // }

        // if ($request->input('agent_commission_deduct') == true) {
        //     $objStudentAgentCommission->commission_paid = $objStudentAgentCommission->commission_payable + $objStudentAgentCommission->gst_amount;
            $objStudentAgentCommission->is_approved = 0;
            $objStudentAgentCommission->is_process = 0;

            //if "Agent Commission is Deducted" is checked then mode should be "'1'=>Agent Deducted"
            $objStudentAgentCommission->mode = 1;
        // } else {
        //     $objStudentAgentCommission->mode = ($request->input('payment_mode') != '') ? $request->input('payment_mode') : null;
        // }

        $objStudentAgentCommission->created_by = Auth::user()->id;
        $objStudentAgentCommission->updated_by = Auth::user()->id;
        $objStudentAgentCommission->save();
    }

    public function getAgentCommissionValue($studentId, $course_id, $invoiceNo='') {

        $res = StudentAgentCommission::where([
            'college_id' => Auth::user()->college_id,
            'course_id'  => $course_id,
            'student_id' => $studentId,
            'invoice_no' => $invoiceNo
        ])->select([
            DB::raw('SUM(gst_amount) as total_gst'),
            DB::raw('SUM(commission_payable) as total_commission_paid')
        ])->first();

        if($res){
            return $res->total_gst + $res->total_commission_paid;
        }
        return 0;

        /*$collegeId = Auth::user()->college_id;
        $totalGst = 0;
        $totalCommissionPaid = 0;
        $getInvoice = StudentInitialPaymentDetails::select('id')->where('invoice_number', $invoiceNo)->get();
        
        if (!empty($getInvoice)) {
            foreach ($getInvoice as $invoice) {
                $getTransaction_no[] = StudentInitialPaymentTransaction::select('transection_no')
                              ->where('initial_payment_detail_id', $invoice['id'])->get()->toArray();
            }
        }
            if (!empty($getTransaction_no)) {
                foreach ($getTransaction_no as $transaction) {
                    if(isset($transaction[0])){
                    $transaction_ids[] = $transaction[0]['transection_no']; 
               }
            }
        }
       
        if(!empty($transaction_ids))
        {
            $arrCommission = StudentAgentCommission::where('college_id', '=', $collegeId)
                    ->where('course_id', '=', $course_id)
                    ->where('student_id', '=', $studentId)
                    ->whereIn('transaction_no',$transaction_ids)
                    ->get(['commission_payable', 'gst_amount']);

            for ($i = 0; $i < count($arrCommission); $i++) {
                $totalGst = $arrCommission[$i]['gst_amount'] + $totalGst;
                $totalCommissionPaid = $arrCommission[$i]['commission_payable'] + $totalCommissionPaid;
            }
        }
        return $totalAmount = $totalGst + $totalCommissionPaid;*/
    }

    //get total(sum) Agent Commission payable from student-agent-commission
    public function _getAgentCommissionPayable($collegeId, $courseId, $studentId, $student_course_id) {

        $commissionpayable = StudentAgentCommission::where('college_id', '=', $collegeId)
                ->where('student_course_id', '=', $student_course_id)
                ->where('course_id', '=', $courseId)
                ->where('student_id', '=', $studentId)
                ->selectRaw('SUM(`commission_payable` + `gst_amount` - `comm_to_refund` - `GST_to_refund`) AS total')
                ->get();
        if ($commissionpayable->count() > 0) {
            return $commissionpayable[0]->total;
        }
    }

    //get total(sum) Agent Commission Paid from student-agent-commission
    public function _getAgentTotalCommissionPaid($collegeId, $courseId, $studentId, $student_course_id) {

        $commissionpaid = StudentAgentCommission::where('college_id', '=', $collegeId)
                ->where('student_course_id', '=', $student_course_id)
                ->where('course_id', '=', $courseId)
                ->where('student_id', '=', $studentId)
                ->selectRaw('SUM(`commission_paid`) AS total')
                ->get();
        if ($commissionpaid->count() > 0) {
            if (!empty($commissionpaid[0]->total)) {
                return $commissionpaid[0]->total;
            } else {
                return 0;
            }
        }
    }

    //get Agent Commission Saved Records from student-agent-commission
    public function _getAgentCommissionList($collegeId, $courseId, $studentId, $student_course_id) {
        return StudentAgentCommission::from('rto_student_agent_commission as rsac')
                        ->join('rto_student_initial_payment_details as rsipd','rsipd.invoice_number', '=', 'rsac.invoice_no')
                        ->join('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                        ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
                        ->where([
                            'rsac.college_id' => $collegeId,
                            'rsac.course_id'  => $courseId,
                            'rsac.student_id' => $studentId,
                            'rsac.student_course_id' => $student_course_id
                        ])
                        ->groupBy('rsac.id')
                        ->get(['ra.agency_name', 'rsac.*']);
    }

    public function getStudentAgentCommission($commissionType, $fromDate, $toDate, $agentId, $collegeId) {
        $fromDate = date('Y-m-d', strtotime($fromDate));
        $toDate = date('Y-m-d', strtotime($toDate));
//         echo $agentId;
//         exit;
        $query = StudentAgentCommission::from('rto_student_agent_commission as rsac')
                ->join('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
                ->join('rto_students', 'rto_students.id', '=', 'rsac.student_id')
                ->join('rto_courses', 'rto_courses.id', '=', 'rsac.course_id')
                ->join('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.student_id', '=', 'rsac.student_id');
                    $join->on('rto_student_courses.course_id', '=', 'rsac.course_id');
                })
                ->leftjoin('rto_student_initial_payment_details as rsipd', 'rsipd.invoice_number', '=', 'rsac.invoice_no')
                ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                ->where('rsac.college_id', '=', $collegeId)
                ->where('ra.user_id', '=', $agentId)
                ->whereBetween('rsac.paid_date', array($fromDate, $toDate));

        if ($commissionType == 1) {
            $query->where('rsac.is_approved', '=', 1);
            $query->where('rsac.is_process', '=', 0);
        }
        if ($commissionType == 2) {
            $query->where('rsac.is_approved', '=', 1);
            $query->where('rsac.is_process', '=', 1);
        }

        return $query->get(['ra.agency_name',
                    'rsac.*',
                    'rto_courses.course_code',
                    'rto_courses.course_name',
                    'rto_students.generated_stud_id',
                    'rto_students.first_name',
                    'rto_students.middel_name',
                    'rsipt.deposited_amount',
                    'rsipt.payment_date',
                    'rto_student_courses.start_date as courseStartDate',
                ])->toarray();
    }

    public function getStudentAgentCommissionApproved($commissionType, $fromDate, $toDate, $agentId, $collegeId) {
        $fromDate = date('Y-m-d', strtotime($fromDate));
        $toDate = date('Y-m-d', strtotime($toDate));

        $query = StudentAgentCommission::from('rto_student_agent_commission as rsac')
                ->join('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
                ->join('rto_students', 'rto_students.id', '=', 'rsac.student_id')
                ->join('rto_courses', 'rto_courses.id', '=', 'rsac.course_id')
                ->join('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.student_id', '=', 'rsac.student_id');
                    $join->on('rto_student_courses.course_id', '=', 'rsac.course_id');
                })
                ->leftjoin('rto_student_initial_payment_details as rsipd', 'rsipd.invoice_number', '=', 'rsac.invoice_no')
                ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                ->where('rsac.college_id', '=', $collegeId)
                ->where('ra.user_id', '=', $agentId)
                ->where('rsac.is_approved', '=', 1);

        if ($commissionType == 1) {
            $query->whereBetween('rsac.paid_date', array($fromDate, $toDate));
        } else {
            $query->where('rsac.is_process', '=', 1);
        }

        $query->groupBy('rsac.id');

        return $query->get(['ra.agency_name',
                    'rsac.*',
                    'rto_courses.course_code',
                    'rto_courses.course_name',
                    'rto_students.generated_stud_id',
                    'rto_students.first_name',
                    'rto_students.middel_name',
                    'rsipt.deposited_amount',
                    'rsipt.payment_date',
                    'rto_student_courses.start_date as courseStartDate',
                ])->toarray();
    }

    public function getAgentBonusDetailList($collegeId, $courseId, $studentId, $student_course_id) {
        return StudentAgentCommission::from('rto_student_agent_commission as rsac')
                        ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
                        ->where('rsac.college_id', '=', $collegeId)
                        ->where('rsac.course_id', '=', $courseId)
                        ->where('rsac.student_course_id', '=', $student_course_id)
                        ->where('rsac.student_id', '=', $studentId)
                        ->get(['ra.agency_name', 'rsac.*']);
    }

    public function getAgentCommissinInfo($collegeId, $agentCommissionID) {
        return StudentAgentCommission::from('rto_student_agent_commission as rsac')
                        ->join('rto_student_initial_payment_details as rsipd', 'rsipd.invoice_number', '=', 'rsac.invoice_no')
                        ->join('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                        ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
                        ->where('rsac.college_id', '=', $collegeId)
                        ->where('rsac.id', '=', $agentCommissionID)
                        ->get(['ra.agency_name', 'rsipd.commission_value', 'rsipd.GST as commission_gst', 'rsac.*']);
        //->get(['ra.agency_name', 'rsipd.commission_value', 'rsipd.GST as commission_gst', 'rsipd.invoice_number', 'rsac.*']);
    }

    public function updateAgentCommissionInfo($request) {

        $agentCommissionID = $request->input('agent_commission_id');

        if (!empty($agentCommissionID) && $agentCommissionID > 0) {

            $objAgentCommission = StudentAgentCommission::find($agentCommissionID);
            
            $objAgentCommission->commission_payable = empty($request->input('commission_payable')) ? NULL : $request->input('commission_payable');
            $objAgentCommission->commission_paid = empty($request->input('commission_paid')) ? NULL : $request->input('commission_paid');
            $objAgentCommission->mode = empty($request->input('payment_mode')) ? NULL : $request->input('payment_mode');
            //$objAgentCommission->invoice_no = empty($request->input('invoice_no')) ? NULL : $request->input('invoice_no');
            $objAgentCommission->paid_date = empty($request->input('payment_dt')) ? NULL : date('Y-m-d', strtotime($request->input('payment_dt')));
            $objAgentCommission->remarks = empty($request->input('remarks')) ? NULL : $request->input('remarks');

            if ($objAgentCommission->commission_payable == $objAgentCommission->commission_paid) {
                $objAgentCommission->is_approved = 1;
                $objAgentCommission->is_process = 1;
            }

            $objAgentCommission->save();
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function updateAgentBonusInfo($request) {

        $agentCommissionID = $request->input('agent_commission_id');

        if (!empty($agentCommissionID) && $agentCommissionID > 0) {

            $objAgentCommission = StudentAgentCommission::find($agentCommissionID);

            $objAgentCommission->bonus_amount = empty($request->input('bonus_amount')) ? NULL : $request->input('bonus_amount');
            $objAgentCommission->GST = empty($request->input('GST')) ? NULL : $request->input('GST');

            $objAgentCommission->save();
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function getTransactionNoArr($collegeId, $courseId, $studentId, $student_course_id) {

        $resultArr = StudentAgentCommission::where('college_id', '=', $collegeId)
                ->where('course_id', '=', $courseId)
                ->where('student_id', '=', $studentId)
                ->where('student_course_id', '=', $student_course_id)
                ->select('id', 'transaction_no')->get()
                //->pluck('transaction_no', 'id')
                ->toArray();

//        $defaultRecord[''] = '- - Select Transaction No - -';
//        return $result = $defaultRecord + $resultArr;
        return $resultArr;
    }

    public function getAgentPaidPaymentList($collegeId, $courseId, $studentId, $invoiceNumber) {
        return StudentAgentCommission::from('rto_student_agent_commission as rsac')
                        //->leftjoin('rto_agents as ra','ra.id','=','rsac.agent_id')
                        ->where('rsac.college_id', '=', $collegeId)
                        ->where('rsac.course_id', '=', $courseId)
                        ->where('rsac.student_id', '=', $studentId)
                        ->where('rsac.invoice_no', '=', $invoiceNumber)
                        //->where('rsac.transaction_no', '=', $transactionNo)
                        ->get(['rsac.*']);
    }

    //insert agent bonus from student-pay-schedule-fee (Tution Fee)
    public function savePayAgentCommission($arrAgentData) {


        $objStudentAgentCommission = new StudentAgentCommission();

        $objStudentAgentCommission->college_id = Auth::user()->college_id;
        $objStudentAgentCommission->student_id = $arrAgentData['studentId'];
        $objStudentAgentCommission->course_id = $arrAgentData['courseId'];
        $objStudentAgentCommission->student_course_id = $arrAgentData['student_course_id'];
        $objStudentAgentCommission->transaction_no = $arrAgentData['transactionNumber'];
        $objStudentAgentCommission->invoice_no = $arrAgentData['invoiceNumber'];
        $objStudentAgentCommission->agent_id = $arrAgentData['agentId'];
        $objStudentAgentCommission->commission_payable = $arrAgentData['commissionPayable'];
        $objStudentAgentCommission->GST = $arrAgentData['gstStatusVal'];

        if ($arrAgentData['gstStatusVal'] == "GST") {
            $objStudentAgentCommission->gst_amount = number_format($objStudentAgentCommission->commission_payable * 10 / 100,2);
        } else {
            $objStudentAgentCommission->gst_amount = 0;
        }

        if ($arrAgentData['isCommissionDeducted'] == true) {
            $objStudentAgentCommission->commission_paid = $objStudentAgentCommission->commission_payable + $objStudentAgentCommission->gst_amount;
            $objStudentAgentCommission->is_approved = 1;
            $objStudentAgentCommission->is_process = 1;

            //if "Agent Commission is Deducted" is checked then mode should be "'1'=>Agent Deducted"
            $objStudentAgentCommission->mode = 1;
        } else {
            $objStudentAgentCommission->mode = $arrAgentData['payment_mode'];
        }


        $objStudentAgentCommission->CHQ_NO = 0;
        $objStudentAgentCommission->paid_date = ($arrAgentData['paymentDate'] != "") ? date('Y-m-d', strtotime($arrAgentData['paymentDate'])) : null;
        $objStudentAgentCommission->bonus_type = ($arrAgentData['bonusType'] != "") ? $arrAgentData['bonusType'] : 0;
        $objStudentAgentCommission->bonus_amount = ($arrAgentData['bonusAmount'] != "") ? $arrAgentData['bonusAmount'] : 0;
        $objStudentAgentCommission->bonus_gst = ($arrAgentData['bonusGST'] != "") ? $arrAgentData['bonusGST'] : null;
        $objStudentAgentCommission->comm_to_refund = 0;
        $objStudentAgentCommission->GST_to_refund = 0;
        $objStudentAgentCommission->refund_amount = 0;
        $objStudentAgentCommission->remarks = ($arrAgentData['remarks'] != '') ? $arrAgentData['remarks'] : null;
        $objStudentAgentCommission->is_commission_deducted = ($arrAgentData['isCommissionDeducted'] == true) ? 1 : 0;
        $objStudentAgentCommission->created_by = Auth::user()->id;
        $objStudentAgentCommission->updated_by = Auth::user()->id;

        $objStudentAgentCommission->save();
    }

    //insert agent bonus from student-pay-schedule-fee ( Miscellaneous Fee)
    public function saveMiscellaneousAgentCommission($arrAgentData) {

        $objStudentAgentCommission = new StudentAgentCommission();

        $objStudentAgentCommission->college_id = Auth::user()->college_id;
        $objStudentAgentCommission->student_id = $arrAgentData['studentId'];
        $objStudentAgentCommission->course_id = $arrAgentData['courseId'];
        $objStudentAgentCommission->student_course_id = $arrAgentData['studentcourseId'];
        $objStudentAgentCommission->transaction_no = $arrAgentData['transactionNumber'];
        $objStudentAgentCommission->invoice_no = $arrAgentData['invoiceNumber'];
        $objStudentAgentCommission->agent_id = $arrAgentData['agentId'];
        $objStudentAgentCommission->commission_payable = 0;
        $objStudentAgentCommission->GST = null;

        $objStudentAgentCommission->gst_amount = 0;

        $objStudentAgentCommission->commission_paid = 0;
        $objStudentAgentCommission->mode = $arrAgentData['payment_mode'];
        $objStudentAgentCommission->CHQ_NO = 0;
        $objStudentAgentCommission->paid_date = ($arrAgentData['paymentDate'] != "") ? date('Y-m-d', strtotime($arrAgentData['paymentDate'])) : null;
        $objStudentAgentCommission->bonus_type = ($arrAgentData['bonusType'] != "") ? $arrAgentData['bonusType'] : 0;
        $objStudentAgentCommission->bonus_amount = ($arrAgentData['bonusAmount'] != "") ? $arrAgentData['bonusAmount'] : 0;
        $objStudentAgentCommission->bonus_gst = null;
        $objStudentAgentCommission->comm_to_refund = 0;
        $objStudentAgentCommission->GST_to_refund = 0;
        $objStudentAgentCommission->refund_amount = 0;
        $objStudentAgentCommission->remarks = ($arrAgentData['remarks'] != '') ? $arrAgentData['remarks'] : null;
        $objStudentAgentCommission->is_commission_deducted = ($arrAgentData['isCommissionDeducted'] == true) ? 1 : 0;
        $objStudentAgentCommission->created_by = Auth::user()->id;
        $objStudentAgentCommission->updated_by = Auth::user()->id;
        $objStudentAgentCommission->save();
    }

    public function studentAgentCommissionGet($view_type, $pay_year, $agent_id) {
        //echo $initialId;exit;

        $collegeId = Auth::user()->college_id;
        $sql = \App\Model\v2\StudentAgentCommission::join('rto_agents as agent', 'agent.id', '=', 'rto_student_agent_commission.agent_id')
                ->join('rto_students as student', 'student.id', '=', 'rto_student_agent_commission.student_id')
                ->join('rto_courses as course', 'course.id', '=', 'rto_student_agent_commission.course_id')
                ->where('rto_student_agent_commission.college_id', '=', $collegeId)
                ->where('rto_student_agent_commission.paid_date', 'like', '%' . $pay_year . '%')
                ->where('rto_student_agent_commission.agent_id', '=', $agent_id);
        if ($view_type == 1) {
            $sql->where('rto_student_agent_commission.is_commission_deducted', '=', 1);
        }
        if ($view_type == 2) {
            $sql->where('rto_student_agent_commission.invoice_no', '!=', "");
        }
        if ($view_type == 3) {
            $sql->where('rto_student_agent_commission.is_commission_deducted', '!=', 1);
            $sql->where('rto_student_agent_commission.mode', '=', 6);
        }

        $result = $sql->get(['agent.agency_name', 'rto_student_agent_commission.*', 'student.first_name', 'student.middel_name', 'student.generated_stud_id', 'course.course_code']);

        return $result;
    }

    //save refund amount from student-payment-summary
    public function _saveAgentRefundCommission($invoiceNo, $agentCommissionRefund, $gstToRefunded, $agentRefunded) {

        $collegeId = Auth::user()->college_id;

        $objStudentAgentCommission = StudentAgentCommission::where('college_id', '=', $collegeId)
                ->where('invoice_no', '=', $invoiceNo)
                //->where('transaction_no', '=', $transactionNo)
                ->get(['id']);

        $agentCommissionId = $objStudentAgentCommission[0]->id;

        $objUpdateRefundAmount = StudentAgentCommission::find($agentCommissionId);
        $objUpdateRefundAmount->is_approved = 0;
        $objUpdateRefundAmount->is_process = 0;
        $objUpdateRefundAmount->comm_to_refund = number_format($agentCommissionRefund, 2);
        $objUpdateRefundAmount->GST_to_refund = number_format($gstToRefunded, 2);
        $objUpdateRefundAmount->refund_amount = number_format($agentRefunded, 2);

        $objUpdateRefundAmount->save();
    }

    //add  refund amount from student-refund-history
    public function addAgentRefundCommission($invoiceNo) {

        $collegeId = Auth::user()->college_id;

        $objStudentAgentCommission = StudentAgentCommission::where('college_id', '=', $collegeId)
                ->where('invoice_no', '=', $invoiceNo)
                //->where('transaction_no', '=', $transactionNo)
                ->get(['id']);

        $agentCommissionId = $objStudentAgentCommission[0]->id;

        $objUpdateRefundAmount = StudentAgentCommission::find($agentCommissionId);
        $objUpdateRefundAmount->comm_to_refund = 0;
        $objUpdateRefundAmount->GST_to_refund = 0;
        $objUpdateRefundAmount->refund_amount = 0;
        $objUpdateRefundAmount->save();
    }

    //remove agent_commission records
    public function _deleteAgentCommission($collegeId, $invoiceNumber) {
        return StudentAgentCommission::where([
            'college_id' => $collegeId,
            'invoice_no' => $invoiceNumber
        ])->delete();
        /*return StudentAgentCommission::where('college_id', '=', $collegeId)
                        ->where('transaction_no', '=', $transactionNo)
                        ->delete();*/
    }

    //set reverse transaction from student-payment-summary
    public function _setReverseTransaction($collegeId, $invoiceNo) {

        /*$objReverseTransaction = StudentAgentCommission::where('college_id', '=', $collegeId)
                        ->where('transaction_no', '=', $transactionNo)->get(['commission_payable', 'gst_amount'])->toArray();*/

        $whereArr = ['college_id' => $collegeId, 'invoice_no' => $invoiceNo];
        $agentCommissionData = StudentAgentCommission::where($whereArr) ->select('commission_payable', 'gst_amount')->first();
        if($agentCommissionData){
            return StudentAgentCommission::where($whereArr)
                ->update([
                    'is_reversed'    => 1,
                    'comm_to_refund' => $agentCommissionData->commission_payable,
                    'GST_to_refund'  => $agentCommissionData->gst_amount
                ]);
        }
    }

    //reverse Back transaction from student-payment-summary
    public function _setReverseBackTransaction($collegeId, $invoiceNo) {
        return StudentAgentCommission::where('college_id', '=', $collegeId)
                        //->where('transaction_no', '=', $transactionNo)
                        ->where('invoice_no', '=', $invoiceNo)
                        ->update(['is_reversed' => 0, 'comm_to_refund' => 0, 'GST_to_refund' => 0]);
    }

    //filter from process-commission-agent
    public function _getAgentCommissionProcess($collegeId, $viewOption, $searchBy, $agentId, $toDate, $fromDate) {
//        $query = StudentInitialPaymentDetails::from('rto_student_initial_payment_details as rsipd')
//                ->select('rsipd.upfront_fee_to_pay','rsipd.upfront_fee_pay')
//                ->whereRaw('rsipd.upfront_fee_to_pay = rsipd.upfront_fee_pay')
//
//                ->get()->toArray();
//print_r($query);exit;
//        return $query;exit;
        $query = \App\Model\v2\StudentAgentCommission::from('rto_student_agent_commission as rsac')
                ->join('rto_students as rs', 'rs.id', '=', 'rsac.student_id')
                ->join('rto_courses as rc', 'rc.id', '=', 'rsac.course_id')
                ->join('rto_student_initial_payment_details as rsipd', 'rsipd.invoice_number', '=', 'rsac.invoice_no')
                ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                ->with(['xeroInvoice'])
                ->where('rsac.college_id', '=', $collegeId)
                ->where('rsac.agent_id', '=', $agentId);

        if ($viewOption == 1) {
            $query->where('rsac.is_approved', '=', 0);
            $query->where('rsac.is_process', '=', 0);
        }
        if ($viewOption == 2) {
            $query->where('rsac.is_approved', '=', 1);
            $query->where('rsac.is_process', '=', 0);
        }
        if ($searchBy == 1) {
           
           // $query->where('rsac.comm_to_refund', '=', 0);
            //$query->having('rsipd.upfront_fee_to_pay', '=', 'rsipd.upfront_fee_pay');
            $query->whereRaw('rsipd.upfront_fee_to_pay = rsipd.upfront_fee_pay');
        }
        if ($searchBy == 2) {
            
//            $query->where('rsac.comm_to_refund', '!=', 0);
            $query->whereRaw('rsipd.upfront_fee_to_pay != rsipd.upfront_fee_pay');
//            $query->having('rsipd.upfront_fee_to_pay', '!=', 'rsipd.upfront_fee_pay');
        }
        //echo $fromDate.'<br>';

        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $query->where('rsipd.due_date', '>=', (string) $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $query->where('rsipd.due_date', '<=', (string) $checkToDate);
        }
        //$query->whereBetween('rsipd.due_date', array($checkFromDate, $checkToDate));
        $query->groupBy('rsac.id');

        return $query->get(['rs.first_name', 'rs.family_name', 'rs.generated_stud_id','rsipd.upfront_fee_pay','rsipd.upfront_fee_to_pay', 'rc.course_code', 'rsipd.due_date', 'rsipd.upfront_fee_pay', 'rsipt.paid_amount as transaction_paid_amount', 'rsipt.amount_refund','rc.course_name', 'rsac.*']);
    }

    //update agent process commission from  process-commission-agent
    public function _updateAgentProcessCommission($viewOption, $approveCheckAll) {

        if (isset($approveCheckAll) && !empty($approveCheckAll)) {
            $agentCommissionId = array_keys($approveCheckAll);

            $flag = 'false';
            for ($i = 0; $i < count($approveCheckAll); $i++) {

                $objUpdateProcessCommission = StudentAgentCommission::find($agentCommissionId[$i]);
                $objUpdateProcessCommission->remarks = $approveCheckAll[$agentCommissionId[$i]];
                if ($viewOption == 1) {
                    $objUpdateProcessCommission->is_approved = 1;
                }
                if ($objUpdateProcessCommission->save()) {
                    $flag = 'true';
                }
            }
            return $flag;
        }
        return 'null';
    }

    //get listing and calculation of checked record (process modal) from process-commission-agent
    public function _getProcessingCommission($collegeId, $processCheckAll) {

        $objProcessCommission = StudentAgentCommission::where('college_id', '=', $collegeId)
                ->whereIn('id', $processCheckAll)
                ->get(['commission_payable', 'gst_amount', 'commission_paid']);


        $commissionPayable = 0;
        $gstAmount = 0;
        $commissionPaid = 0;

        for ($i = 0; $i < count($objProcessCommission); $i++) {
            $commissionPayable += $objProcessCommission[$i]->commission_payable;
            $gstAmount += $objProcessCommission[$i]->gst_amount;
            $commissionPaid += $objProcessCommission[$i]->commission_paid;
        }

        $arrProcessCommission = array('commissionPayable' => $commissionPayable,
            'gstAmount' => $gstAmount,
            'commissionPaid' => $commissionPaid
        );
        return $arrProcessCommission;
    }

    public function saveProcessingCommissionData($data) {
        $objSaveProcess = StudentAgentCommission::find($data['id']);

        if(!empty($data['remarks'])){
            $data['remarks'] = $objSaveProcess->remarks . ';' . $data['remarks'] ;
            $data['remarks'] = ltrim($data['remarks'] , ';');
        }else{
            $data['remarks'] = null;
        }

        $objSaveProcess->mode = (!empty($data['mode'])) ? $data['mode'] : null;
        $objSaveProcess->commission_paid = $objSaveProcess->commission_payable + $objSaveProcess->gst_amount - $objSaveProcess->refund_amount;
        $objSaveProcess->paid_date = (!empty($data['paid_date'])) ? date('Y-m-d', strtotime($data['paid_date'])) : null;
        $objSaveProcess->remarks = $data['remarks'];
        $objSaveProcess->is_process = 1;
        $objSaveProcess->save();
        return true;
    }

    //save or update records (process modal) from process-commission-agent
    public function _saveProcessingCommission($arrProcessingId, $invoiceNo, $paymentMode, $commissionPaidDate, $remarksProcessing) {

        for ($i = 0; $i < count($arrProcessingId); $i++) {
            
            
            
            $objSaveProcess = StudentAgentCommission::find($arrProcessingId[$i]);
            $objSaveProcess->invoice_no = ($invoiceNo != '') ? $invoiceNo : null;
            $objSaveProcess->mode = ($paymentMode != '') ? $paymentMode : null;
            $objSaveProcess->commission_paid = $objSaveProcess->commission_payable + $objSaveProcess->gst_amount - $objSaveProcess->refund_amount;
            $objSaveProcess->paid_date = ($commissionPaidDate != '') ? date('Y-m-d', strtotime($commissionPaidDate)) : null;
            
            if($remarksProcessing != ''){ 
                $remarksProcessing=$objSaveProcess->remarks . ';' . $remarksProcessing;
                $remarksProcessing = ltrim($remarksProcessing, ';');
            }else{ 
                $remarksProcessing=null;
            }            
            $objSaveProcess->remarks = $remarksProcessing;
            $objSaveProcess->is_process = 1;
            $objSaveProcess->save();
        }
    }

    // Disapprove Agent Commission Record
    public function disapproveAgentCommission($processingId) {
        return StudentAgentCommission::where('id', $processingId)
            ->update([
                'is_approved' => 0,
                'is_process' => 0
            ]);
    }

    public function generateAgentCommissionReport($perPage, $toDate, $fromDate, $isxls = null) {
        $query = StudentAgentCommission::from('rto_student_agent_commission as rsac')
                ->join('rto_agents as ra', 'ra.id', '=', 'rsac.agent_id')
                ->join('rto_students as rs', 'rs.id', '=', 'rsac.student_id')
                ->join('rto_courses as rc', 'rc.id', '=', 'rsac.course_id')
                ->join('rto_student_courses as rsc', 'rsc.student_id', '=', 'rsac.student_id')
                ->join('rto_campus as rcamp', 'rcamp.id', '=', 'rsc.campus_id')
                ->leftjoin('rto_student_initial_payment_details as rsipd', 'rsipd.invoice_number', '=', 'rsac.invoice_no')
                ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                ->select('rcamp.name as campus_name', 'rs.first_name', 'rs.family_name', 'rs.generated_stud_id', 'rc.course_code', 'rsc.course_attempt', 'rsc.status', 'rsc.course_fee', 'ra.agency_name', 'rsipd.due_date', 'rsipd.upfront_fee_pay', 'rsipt.paid_amount as transaction_paid_amount', 'rc.course_name', 'rsac.id')
                ->where(DB::raw('rsac.commission_payable + rsac.gst_amount'), '!=', DB::raw('rsac.commission_paid'));
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $query->where('rsipd.due_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $query->where('rsipd.due_date', '<=', $checkToDate);
        }
        $query->groupBy('rsac.id');
        $query->orderBy('rs.id', 'desc');
        if (isset($isxls)) {
            return $query->get();
        } else {
            return $query->paginate($perPage);
        }
    }

    public function checkPaymentMode($collegeId, $paymdentId) {

        return StudentAgentCommission::where('college_id', $collegeId)
                        ->where('mode', $paymdentId)
                        ->count();
    }

    public function _getAgentCommissionProcessExcel($collegeId, $viewOption, $searchBy, $agentId, $toDate, $fromDate) {
        $query = \App\Model\v2\StudentAgentCommission::from('rto_student_agent_commission as rsac')
                ->leftjoin('rto_agents as agent', 'agent.id', '=', 'rsac.agent_id')
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsac.student_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsac.course_id')
                ->leftjoin('rto_student_initial_payment_details as rsipd', 'rsipd.invoice_number', '=', 'rsac.invoice_no')
                ->leftjoin('rto_student_initial_payment_transaction as rsipt', 'rsipt.initial_payment_detail_id', '=', 'rsipd.id')
                ->where('rsac.college_id', '=', $collegeId)
                ->where('rsac.agent_id', '=', $agentId);
        if ($viewOption == 1) {
            $query->where('rsac.is_approved', '=', 0);
            $query->where('rsac.is_process', '=', 0);
        }
        if ($viewOption == 2) {
            $query->where('rsac.is_approved', '=', 1);
            $query->where('rsac.is_process', '=', 0);
        }
        if ($searchBy == 1) {
            $query->whereRaw('rsipd.upfront_fee_to_pay = rsipd.upfront_fee_pay');
            //$query->where('rsac.refund_amount', '=', 0);
        }
        if ($searchBy == 2) {
            $query->whereRaw('rsipd.upfront_fee_to_pay != rsipd.upfront_fee_pay');
            //$query->where('rsac.refund_amount', '!=', 0);
        }
//        if ($fromDate != '' && $fromDate != 0) {
        $checkFromDate = date('Y-m-d', strtotime($fromDate));
        $query->where('rsipd.due_date', '>=', $checkFromDate);
//        }
//        if ($toDate != '') {
        $checkToDate = date('Y-m-d', strtotime($toDate));
        $query->where('rsipd.due_date', '<=', $checkToDate);
//        }
        $query->groupBy('rsac.id');
        $result = $query->get(['rs.first_name', 'agent.agency_name', 'rs.family_name', 'rs.generated_stud_id', 'rc.course_code', 'rsipd.due_date', 'rsipd.upfront_fee_pay', 'rsipt.paid_amount as transaction_paid_amount', 'rc.course_name', 'rsac.*']);
        for ($i = 0; $i < count($result); $i++) {
            $objRtoStudentCourse = new StudentCourse();
            $objCourseInfo = $objRtoStudentCourse->_getCourseInfo($result[$i]['course_id'], $result[$i]['student_id']);
            $result[$i]['status'] = $objCourseInfo[0]['status'];
        }
        return $result;
    }
    
     public function deleteStudentAgentCommission($studentId, $courseId) {
        return StudentAgentCommission::where('college_id', '=',  Auth::user()->college_id)
                        ->where('student_id', '=', $studentId)
                        ->where('course_id', $courseId)
                        ->delete();
    }
}
