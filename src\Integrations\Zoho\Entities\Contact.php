<?php

namespace Integrations\Zoho\Entities;

use App\Model\v2\Student;
use Support\Traits\ArrayToProps;
use Integrations\Zoho\Traits\ToZohoArray;

class Contact implements ZohoPayload
{
    use ArrayToProps;
    use ToZohoArray;
    
    public $student_id;
    public $first_name;
    public $last_name;
    public $course;
    public $dob;
    public $email;
    public $mobile;
    public $description;
    public $title;
    public $gender;
    public $usi;
    public $passport_number;
    public $start_date;
    public $finish_date;
    public $reference_id;
    public $account_name;
    public $status;
    public $workdrive_link_url;
    public $coe;
    

    public function key(): string
    {
        return 'students';
    }

    public static function ForConversion(Student $student)
    {
        $futureArray = FutureStudent::LazyFromArray($student)->toArray(true);
        return Contact::LazyFromArray(array_merge(
            $futureArray, [
                'student_id' => $student->generated_stud_id,
                'first_name' => $student->first_name,
                'last_name'  => $student->family_name,
                'email'      => $student->email,
                'coe'        => null //TODO: get coe here
            ]
        ));
    }

    public function extends(): ?string
    {
        return 'leads';
    }
}