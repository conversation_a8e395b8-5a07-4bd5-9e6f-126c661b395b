<?php

namespace App\Model\v2;

use App\Classes\CoursesApiController;
use App\Classes\SiteConstants;
use App\DTO\Courses\CourseFilterDTO;
use App\DTO\Courses\CourseUnitDTO;
use App\Helpers\Helpers;
use App\Model\CourseFieldOfEducations;
use App\Model\CourseLevelEducation;
use App\Model\Departments;
use App\Model\Faculties;
use App\Services\CourseSetupService;
use App\Services\SyncUnitsSetupService;
use Carbon\Carbon;
use Config;
use Domains\Moodle\Contracts\CanConnectToMoodle;
use Domains\Moodle\Entities\Entity;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Repositories\MoodleRepository;
use Domains\Moodle\Traits\MoodleSyncableItem;
use Domains\Xero\Traits\XeroableItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Integrations\Base\Traits\ThirdPartySyncableItem;
use Integrations\Zoho\Contracts\CanConnectToZoho;
use Integrations\Zoho\Entities\ZohoPayload;
use Integrations\Zoho\Traits\ZohoSyncableItem;
use Laravel\Scout\Searchable;
use Support\Traits\CreaterUpdaterTrait;
use Support\Traits\TenantAwareSearchable;

class Courses extends Model implements CanConnectToMoodle, CanConnectToZoho
{
    use CreaterUpdaterTrait;
    use HasFactory;
    use MoodleSyncableItem;
    use SoftDeletes;
    use TenantAwareSearchable;
    use ThirdPartySyncableItem;
    use XeroableItem;
    use ZohoSyncableItem;

    const DURATION_TYPE_DAILY = '1';

    const DURATION_TYPE_WEEKLY = '2';

    const DURATION_TYPE_MONTHLY = '3';

    const DURATION_TYPE_YEARLY = '4';

    const ACTIVITED_YES = 1;

    const ACTIVITED_NO = 0;

    const IS_TCSI_YES = 1;

    const IS_TCSI_NO = 0;

    protected $table = 'rto_courses';

    protected $fillable = [
        'id',
        'college_id',
        'course_code',
        'is_vet_synced',
        'course_name',
        'activated_now',
        'course_type_id',
        'module_delivery',
        'is_superseded',
        'superseded_date',
        'is_super_code',
        'national_code',
        'cricos_code',
        'is_cricos_code',
        'delivery_target',
        'qualification_prerequisite_id',
        'course_duration',
        'couse_duration_type',
        'online_hours',
        'face_to_face_hours',
        'tuition_fee',
        'domestic_fee',
        'maximum_weekly_study',
        'effective_start',
        'effective_end',
        'course_completion_type',
        'course_delivery_type',
        'results_calculation_methods',
        'flexible_attendance',
        'couse_level',
        'campus_list',
        'work_placement',
        'work_placement_component_type',
        'placement_officer_id',
        'work_placement_hour',
        'vocational_duration',
        'vocational_startweek',
        'fee_help',
        'free_help_study_load',
        'free_help_study_type',
        'is_tcsi',
        'course_credit_point',
        'course_recognition_id',
        'level_of_education_id',
        'field_of_education_id',
        'ANZSCO_code',
        'total_nominal_hours',
        'AVETMISS_Report',
        'status',
        'core_units_number',
        'elective_units_number',
        'faculty_id',
        'department_id',
        'qtac_course_id',
        'course_description',
        'course_summary',
        'identity_proof',
        'entry_requirements',
        'fee_recurring_type',
        'is_fee_recurring',
        'seat_limit',
        'is_seat_fixed',
        'major',
        'cover_image',
        'course_tags',
        'created_by',
        'updated_by',
    ];

    protected static function booted()
    {
        static::created(function ($course) {
            self::updateUnitCompletionStatus($course);
            $course->makeCoreCountSameForAllTemplates();
            self::AddRecordToCoursesMaterialTable($course);
        });
        static::updated(function ($course) {
            self::updateUnitCompletionStatus($course);
            $course->makeCoreCountSameForAllTemplates();
            self::CheckForMaterialTable($course);
        });
    }

    public static function AddRecordToCoursesMaterialTable($course)
    {
        $materialData = [
            'college_id' => $course->college_id,
            'parent_id' => 0,
            'folder_or_file' => $course->course_code,
            'original_name' => null,
            'type' => 'Course',
            'description' => $course->course_name,
            'is_student' => '0',
            'is_teacher' => '0',
        ];
        CoursesMaterial::create($materialData);
    }

    public static function CheckForMaterialTable($course)
    {
        $table = CoursesMaterial::where('folder_or_file', '=', $course->course_code)->first();
        if (! $table) {
            self::AddRecordToCoursesMaterialTable($course);
        }
    }

    public static function updateUnitCompletionStatus($course)
    {
        $courseComplete = $course->isCourseComplete() ?? false;
        if (! $courseComplete && $course->activated_now != 1) {
            $course->activated_now = 2;
            $course->saveQuietly();
        } elseif ($course->activated_now == 2) {
            $course->activated_now = 0;
            $course->saveQuietly();
        }
    }

    public function makeCoreCountSameForAllTemplates()
    {
        // make number of core subjects similar across all templates within a course
        // because the number of core subjects will be same for all majors
        CourseTemplate::where('course_id', $this->id)->update(['no_of_core_subject' => $this->core_units_number]);
    }

    public function courseUnits()
    {
        return $this->hasMany(SubjectUnits::class, 'course_id', 'id');
    }

    /*
    old unit setup
    public function addedUnits(){
        return $this->hasMany(UnitModule::class,'course_id','id')
                ->where('status', 1);
    }
    */
    public function addedUnits()
    {
        return $this->hasMany(SubjectUnits::class, 'course_id', 'id')
            ->where('status', 1);
    }

    public function courseSubjects()
    {
        return $this->hasMany(CourseSubject::class, 'course_id', 'id');
    }

    public function courseType()
    {
        return $this->belongsTo(CourseType::class, 'course_type_id', 'id');
    }

    public function faculty()
    {
        return $this->belongsTo(Faculties::class, 'faculty_id', 'id');
    }

    public function prerequisite()
    {
        return $this->belongsTo(Courses::class, 'qualification_prerequisite_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Departments::class, 'department_id', 'id');
    }

    public function templates()
    {
        return $this->hasMany(CourseTemplate::class, 'course_id', 'id');
    }

    public function apicache()
    {
        $cacheRefreshWindow = config('constants.trainingGovApiCacheRefreshWindow', 0);
        $today = Carbon::now();
        $dateCheck = $today->subDays($cacheRefreshWindow);
        $dateCheck = $dateCheck->format('Y-m-d');

        return $this->hasOne(TrainingApiCache::class, 'code', 'course_code')
            ->where(function (Builder $query) use ($dateCheck) {
                $query->where('last_updated', '>=', $dateCheck);
            });
    }

    public function intakes()
    {
        return $this->hasMany(CoursesIntakeDate::class, 'course_id', 'id');
    }

    public function campuses()
    {
        return $this->hasMany(CollegeCourse::class, 'course_id', 'id');
    }

    public function subjects()
    {
        return $this->hasMany(CourseSubjects::class, 'course_id', 'id');
    }

    public function getCoverImageUrl()
    {
        $filePath = config('constants.uploadFilePath.CourseCoverImages');
        $filePath = Helpers::changeRootPath($filePath);
        $cover_image = $this->cover_image ?? null;
        if (! empty($cover_image)) {
            $isSvg = strtolower(pathinfo($cover_image, PATHINFO_EXTENSION)) === 'svg';

            if ($isSvg) {
                return [
                    'small' => url($filePath['view'].$cover_image),
                    'thumb' => url($filePath['view'].$cover_image),
                    'large' => url($filePath['view'].$cover_image),
                ];
            }

            $defaulThumbName = Config::get('constants.defaultThumbText');
            $smallThumbName = Config::get('constants.smallThumbText');

            return [
                'small' => url($filePath['view'].$smallThumbName.$cover_image),
                'thumb' => url($filePath['view'].$defaulThumbName.$cover_image),
                'large' => url($filePath['view'].$cover_image),
            ];
        }

        return '';
    }

    public function subjectsold()
    {
        return $this->hasManyThrough(
            Subject::class,
            CourseSubject::class,
            'course_id', // foreign key in the CourseSubject table
            'id', // foreign key on Subject table
            'id', // local key in courses table
            'subject_id' // local key in CourseSubject
        )
            ->select(
                'rto_subject.*',
                'rto_course_subject.id AS map_id',
                'rto_course_subject.course_stage',
                'rto_course_subject.subject_type',
                'rto_course_subject.subject_fee',
                'rto_course_subject.domestic_subject_fee',
                'rto_course_subject.unit_type',
                'rto_course_subject.delivery_mode',
                'rto_course_subject.EFTSL_study_load',
                'rto_course_subject.contact_hours',
                'rto_course_subject.is_active',
                'rto_course_subject.inc_in_certificate',
                'rto_course_subject.display_order',
            )
            ->orderBy('rto_course_subject.display_order');
    }

    /* function to get units in the course */
    public function subjects_normalized()
    {
        return $this->hasMany(CourseSubjects::class, 'course_id', 'id')
            ->orderBy('rto_course_subjects.display_order');
    }

    /* function to get units in the course */
    public function units_normalized()
    {
        return $this->hasMany(SubjectUnits::class, 'course_id', 'id');
    }

    public function getResultCalculationMethod($courseId)
    {
        return $arrCourse = Courses::select('results_calculation_methods', 'fee_help')
            ->where('college_id', '=', Auth::user()->college_id)
            ->where('id', '=', $courseId)
            ->get()
            ->toArray();
    }

    public function getCourseArrWithStatusForStudent($college_id, $studentId)
    {
        $arrCourses = Courses::join('rto_student_courses as rsc', 'rsc.course_id', '=', 'rto_courses.id')
            ->where('rto_courses.college_id', '=', $college_id)
            ->where('rsc.student_id', '=', $studentId)
            ->where('rsc.offer_status', '=', 'Enrolled')
            ->get([
                'rto_courses.id',
                'rto_courses.course_name',
                'rto_courses.course_code',
                'rsc.status',
            ])
            ->toArray();
        // echo '<pre>';print_r($arrCourses);exit;

        if (count($arrCourses) > 0) {
            foreach ($arrCourses as $key => $value) {
                $result[$arrCourses[$key]['id']] = $arrCourses[$key]['course_code'].' : '.$arrCourses[$key]['course_name'].' - '.$arrCourses[$key]['status'];
            }
        } else {
            $result[''] = 'No Course Found';
        }

        return $result;
    }

    public function getDescriptionAttribute()
    {
        return $this->course_code.' : '.$this->course_name;
    }

    /*
    method used to return the course code and course name
    generally used to setup any dropdown inputs
    */
    public static function getCoursesList($college_id = 0, $format = false)
    {

        $arrCourses = Courses::select('id', 'course_name', 'course_code')
            ->where('college_id', $college_id)
            ->where('status', 1)
            ->get()->toArray();
        if ($format) {
            $arrCourses = collect($arrCourses)->map(function ($item) {
                return [
                    'id' => $item['id'],
                    'code' => $item['course_code'],
                    'title' => $item['course_code'].' - '.$item['course_name'],
                ];
            });
        }

        return $arrCourses;
    }

    public static function getCampusList($college_id, $format = false)
    {
        $arrCampus = CollegeCampus::select('id', 'name')
            ->where('status', 1)
            ->Where('college_id', $college_id)
            ->get();
        if ($format) {
            $arrCampus = collect($arrCampus)->map(function ($item) {
                return [
                    'id' => $item['id'],
                    'text' => $item['name'],
                ];
            })->toArray();
        }

        return $arrCampus;
    }

    public static function getCoursesForDropdown($college_id, $format = true, $campusStritct = false)
    {
        $selects = [
            'rto_courses.id',
            'rto_courses.course_code',
            'rto_courses.course_name',
            'rto_courses.course_type_id',
            'rto_courses.activated_now',
            'rto_courses.status',
        ];
        $query = Courses::Where('rto_courses.college_id', $college_id);
        if ($campusStritct) {
            $selects[] = DB::raw('GROUP_CONCAT(rto_course_campus.campus_id) as campus_list');
            $query->join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
                ->groupBy('rto_courses.id');
            if ($campusStritct > 0) {
                // $query->havingRaw('FIND_IN_SET(?, campus_list) > 0', [$campusStritct]);
            }
            // $selects[] = 'CASE WHEN FIND_IN_SET(28, GROUP_CONCAT(rto_course_campus.campus_id)) > 0 THEN 1 ELSE 0 END AS campus_flag';
            $selects[] = DB::raw('CASE WHEN FIND_IN_SET('.$campusStritct.', GROUP_CONCAT(rto_course_campus.campus_id)) > 0 THEN 1 ELSE 0 END AS is_in_campus');
        } else {
            $selects[] = 'rto_courses.campus_list';
            $selects[] = DB::raw('1 AS is_in_campus');
        }
        $arrCampus = $query->select($selects)->get();
        if ($format) {
            $arrCampus = collect($arrCampus)->map(function ($item) {
                return [
                    'id' => $item['id'],
                    'text' => $item['course_code'].' - '.$item['course_name'],
                    'course_type_id' => (int) $item['course_type_id'],
                    'active' => $item['activated_now'] == 1,
                    'campus_list' => $item['campus_list'],
                    'campuses' => explode(',', $item['campus_list']),
                    'current_campus' => $item['is_in_campus'] == 1,
                ];
            })->toArray();
        }

        return $arrCampus;
    }

    public static function getStaffAndTeacher($college_id, $format = false)
    {
        $staffAndTeacher = StaffPosition::from('rto_staff_position as rsp')
            ->join('rto_staff_and_teacher as rst', 'rst.position', '=', 'rsp.id')
            ->Where('college_id', $college_id)
            ->select(DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as name"), 'rst.id as id')
            ->get()
            ->toArray();
        if ($format) {
            $staffAndTeacher = collect($staffAndTeacher)->map(function ($item) {
                return [
                    'id' => $item['id'],
                    'text' => $item['name'],
                ];
            })->toArray();
        }

        return $staffAndTeacher;
    }

    public static function getFaculties($college_id, $textOnly = true)
    {
        $arrFacilitiess = Faculties::select('id', 'name AS text')
            ->where(['college_id' => $college_id, 'status' => 1]);
        if ($textOnly) {
            $arrFacilitiess = $arrFacilitiess->pluck('text');
        } else {
            $arrFacilitiess = $arrFacilitiess->get();
        }

        return $arrFacilitiess;
    }

    public static function getLevelEducation()
    {
        $arrLevelEducation = CourseLevelEducation::select('id', 'avaitmiss_id AS code', 'name AS text')
            ->where('status', 1)
            ->get()
            ->each(function ($item) {
                // this was done because the item->code is stored as integer in rto_courses table
                $item->code = (int) $item->code;
            });

        return $arrLevelEducation;
    }

    public static function getFieldOfEducation()
    {
        $arrFieldEducation = CourseFieldOfEducations::select('id', 'avaitmiss_id AS code', 'name AS text')
            ->where('status', 1)
            ->get()
            ->each(function ($item) {
                // this was done because the item->code is stored as integer in rto_courses table
                $item->code = (int) $item->code;
            });

        return $arrFieldEducation;
    }

    public static function getDepartments($college_id)
    {

        $arrDepartments = Departments::join('rto_faculties', 'rto_departments.faculty_id', '=', 'rto_faculties.id')
            ->select('rto_departments.id', 'rto_departments.faculty_id', 'rto_departments.name as text', 'rto_faculties.name as faculty_name')
            ->where(['rto_departments.status' => 1, 'rto_departments.college_id' => $college_id])->get()->groupBy('faculty_id')->toArray();

        return $arrDepartments;
    }

    public static function getCourseRecognition()
    {

        $arrCourseRecognition = CourseRecognitions::where('status', 1)
            ->pluck('name', 'avaitmiss_id')
            ->toArray();
        $arrCourseRecognitionTitle['0'] = '- - Choose Course Recognition - -';

        return $arrCourseRecognitions = $arrCourseRecognition;
    }

    public static function getANZSCOCodesList()
    {

        $arrANZSCOCode = AnzscoCodes::where('status', 1)
            ->pluck('name', 'avaitmiss_id')
            ->toArray();
        $ANZSCOCodesTitle['0'] = '-- Choose  ANZSCOCodes --';

        return $arrANZSCOCodes = $arrANZSCOCode;
    }

    public function getCourseDetail($college_id, $course_id, $getBy = 'id')
    {
        $field = 'rto_courses.id';
        if ($getBy == 'code') {
            $field = 'rto_courses.course_code';
        }
        $arrCourse = Courses::select(
            'rto_courses.id',
            'rto_courses.course_name',
            'rto_courses.course_code',
            'rto_course_subject.course_id',
            'rto_subject.id as subject_id',
            'rto_subject.subject_name',
            'rto_subject.subject_code',
            DB::raw('IFNULL(group_concat(distinct rto_assessment_tasks.task_name separator "<br>"),"<span style=\'color:red;\'>No assessment info found</span>") as tasks'),
            DB::raw('group_concat(distinct concat(rto_subject_unit.vet_unit_code," : ",rto_subject_unit.unit_name) separator "<br>") as units')
        )
            ->leftjoin('rto_course_subject', 'rto_course_subject.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_subject.id')
            ->leftjoin('rto_assessment_tasks_units', 'rto_assessment_tasks_units.unit_id', '=', 'rto_subject_unit.id')
            ->leftjoin('rto_assessment_tasks', 'rto_assessment_tasks.id', '=', 'rto_assessment_tasks_units.assessment_task_id')
            ->where('rto_courses.college_id', $college_id)
            ->where($field, $course_id)
            ->where('rto_courses.status', 1)
            ->orderby('rto_subject.subject_name')
            ->groupby('rto_subject_unit.id')
            ->get()->toArray();

        return $arrCourse;
    }

    public static function GetCourseStructureData($course_id, $getBy = 'id')
    {
        $college_id = Auth::user()->college_id;
        $field = 'rto_courses.id';
        if ($getBy == 'code') {
            $field = 'rto_courses.course_code';
        }
        $result = Courses::where(['college_id' => $college_id, $field => $course_id])
            ->join('rto_course_subject', 'rto_course_subject.course_id', '=', 'rto_courses.id')
            ->join('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_course_subject.subject_id')
            ->join('rto_subject', 'rto_subject.id', '=', 'rto_subject_unit.subject_id')
            ->join('rto_assessment_tasks_units', 'rto_assessment_tasks_units.unit_id', '=', 'rto_subject_unit.id')
            ->join('rto_assessment_tasks', 'rto_assessment_tasks.id', '=', 'rto_assessment_tasks_units.assessment_task_id')
            ->select(
                'rto_course_subject.subject_id',
                'rto_subject.subject_code',
                'rto_subject.subject_name',
                'rto_subject_unit.id as unit_id',
                'rto_subject_unit.unit_code',
                'rto_subject_unit.unit_name',
                'rto_assessment_tasks.id as task_id',
                'rto_assessment_tasks.task_name',
                'rto_assessment_tasks.task_description'
            )
            ->get();
    }

    public static function SearchCoursesList(?CourseFilterDTO $filters = null)
    {
        $searchText = $filters->search ?? '';
        $searchType = $filters->type ?? '';
        $searchFaculty = $filters->faculty ?? '';
        $searchStatus = $filters->status ?? '';
        $searchDuration = $filters->duration ?? '';
        $college_id = $filters->college_id ?? 0;
        $paginate = $filters->paginate ?? 10;
        $searchDuration = ($searchDuration == '50+') ? 51 : (int) $searchDuration;
        $courses = Courses::query()
            ->when($searchText, function ($query, $search) {
                $query->Where(function ($query) use ($search) {
                    $query->OrWhere('course_name', 'like', '%'.$search.'%')
                        ->OrWhere('course_code', 'like', '%'.$search.'%');
                });
                /*$query->where('course_name', 'like', '%' . $search . '%')
                            ->OrWhere('course_code', 'like', $search . '%');*/
            })
            ->when($searchType, function ($query, $search) {
                $query->where('course_type_id', $search);
            })
            ->when($searchFaculty, function ($query, $search) {
                $query->where('faculty_id', $search);
            })
            ->when($searchStatus, function ($query, $search) {
                if ($search == 'incomplete') {
                    $query->Where(function ($query) {
                        $query->where('activated_now', 2)
                            ->OrWhere('course_type_id', null)
                            ->OrWhere('course_code', null)
                            ->OrWhere('course_name', null)
                            ->OrWhere('delivery_target', null)
                            ->OrWhere('course_duration', null)
                            ->OrWhere('maximum_weekly_study', null)
                            ->OrWhere('campus_list', null)
                            ->OrWhere('results_calculation_methods', null)
                            ->orWhere(function ($query) {
                                $query->where('tuition_fee', null)
                                    ->Where('domestic_fee', null);
                            });
                    });
                } else {
                    $activeStatus = (($search == 'inactive') ? 0 : 1);
                    $query->where('activated_now', '=', $activeStatus);
                }
            })
            ->when($searchDuration, function ($query, $search) {
                $typeField = 'couse_duration_type';

                $start = ($search > 50) ? 350 : ($search - 10) * 7;
                $end = ($search > 50) ? 50000 : $search * 7;
                $range = [$start, $end];
                $query->whereBetween(DB::raw("CASE 
                                        WHEN `$typeField` = 1 THEN (course_duration)
                                        WHEN (`$typeField` = 2 OR `$typeField` = 0 OR `$typeField` = NULL) THEN (course_duration * 7)
                                        WHEN `$typeField` = 3 THEN (course_duration * 30)
                                        WHEN `$typeField` = 4 THEN (course_duration * 365)
                                        ELSE 0
                                    END"), $range);
            })
            ->where('college_id', $college_id)
            ->with([
                'courseType',
                'faculty',
                'moodleItem',
                'prerequisite',
                'department',
                'apicache.detail',
                'courseSubjects.subject.subjectUnits.assessmentTasksUnits.assessmentTask',
            ])->paginate($paginate);

        return $courses;
    }

    public static function GetAllCourseSubjects($courseCode = '', $getBy = 'code', $newCourse = false)
    {
        $college_id = Auth::user()->college_id;
        if (empty($courseCode)) {
            return [];
        }
        $codeField = 'course_code';
        if ($getBy != 'code') {
            $codeField = 'id';
        }
        $relation = ($newCourse) ? 'subjects_normalized' : 'subjects';
        $subjects = Courses::With($relation)
            ->Where([$codeField => $courseCode])
            ->get()
            ->pluck($relation)
            ->flatten()
            ->unique('id');

        return $subjects;
    }

    /**
     * Modify the query used to retrieve models when making all of the models searchable.
     */
    protected function makeAllSearchableUsing(Builder $query): Builder
    {
        return $query;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray($context = null): array
    {
        // $this->loadMissing(['studentCourses.course']);
        return [
            'id' => (int) $this->id,
            'course_code' => $this->course_code,
            'course_name' => cleanNonUTF($this->course_name),
            'cricos_code' => $this->cricos_code,
            'qualification_prerequisite_id' => $this->qualification_prerequisite_id,
            'course_duration' => $this->course_duration,
            'tuition_fee' => (float) $this->tuition_fee,
            'domestic_fee' => (float) $this->domestic_fee,

        ];
    }

    public static function QuickAddCourse($data = [])
    {
        $college_id = Auth::user()->college_id;
        if (empty($data)) {
            return null;
        }
        // to get course data with trash included
        $existingCourse = Courses::Where('college_id', $college_id)
            ->Where('course_code', $data['course_code'])
            ->withTrashed()->first();

        $restored = false;
        if (! $existingCourse) {
            // insert the course
            $existingCourse = new Courses;
        } elseif ($existingCourse->trashed()) {
            $existingCourse->restore();
            $restored = true;
        }

        $data['college_id'] = $college_id ?? 0;
        $data['is_super_code'] = $data['superseded_course']['Code'] ?? null;
        $data['activated_now'] = 0;
        $existingCourse->fill($data);
        $existingCourse->save();
        if ($restored) {
            $existingCourse->wasdeleted = true;
        }
        // if ($existingCourse->course_type_id == SiteConstants::SHORT_COURSE_TYPE_ID && ! $hasUnits) {
        //     $existingCourse->setUpSubjectUnitForShortCourse();
        // }

        return $existingCourse;
    }

    public function isCourseComplete()
    {
        $service = new CourseSetupService;
        $progress = $service->getProgressStatus($this);

        $hasFalseStatus = ! empty(array_filter($progress, function ($item) {
            return isset($item['status']) && $item['status'] === false && (empty($item['ignore']));
        }));

        return ! $hasFalseStatus;
    }

    public function isCourseDeletable()
    {
        $objStudentCourse = new StudentCourses;
        $arrStudentCourse = $objStudentCourse->_checkCourseExist($this->id);

        return $arrStudentCourse == 0;
    }

    public function updateStatus($courseStatus = 0)
    {
        if ($courseStatus == 0) {
            // check if this course can be deactivated
            $complete = $this->isCourseDeletable();
            if (! $complete) {
                return 'stop_deactivate';
            }
        } else {
            // check if this course can be activated
            $complete = $this->isCourseComplete();
            if (! $complete) {
                return 'stop_activate';
            }
        }
        $this->activated_now = $courseStatus;
        $saved = $this->saveQuietly();

        return $saved ?? 'status_not_changed';
    }

    public function setMasterPackaging()
    {
        $isVetCourse = $this->course_type_id === 2;
        // $coreNumbers = $this->core_units_number ?? 0;
        // $isVetCourse = $this->elective_units_number ?? 0;
        // get the course templates
        $electiveUnits = $coreUnits = 0;
        if ($this->id) {
            $template = new CourseTemplate;
            $maxTemplatePackaging = $template->getMaxPackagingRulesOfCourse($this->id);
            $electiveUnits = $maxTemplatePackaging['elective'] ?? 0;
            $coreUnits = $maxTemplatePackaging['core'] ?? 0;
        }
        if (! $electiveUnits && ! $coreUnits) {
            if ($isVetCourse) {
                $coursesApi = new CoursesApiController;
                $courseCode = $this->course_code ?? '';
                $courseDetail = $coursesApi->loadCourseUnits($courseCode);
                $unitsList = collect($courseDetail['Units'] ?? []);
                $totalUnits = $unitsList->count();
                $totalUnits = $totalUnits > 0 ? $totalUnits : 1;
                $electiveUnits = $unitsList->where('IsEssential', '!=', true)->count();
                $coreUnits = $totalUnits - $electiveUnits;
            } else {
                // to make failsafe
                $coreUnits = $electiveUnits = 1;
            }
        }
        $this->core_units_number = $coreUnits;
        $this->elective_units_number = $electiveUnits;

    }

    public function getZohoPayload(): ZohoPayload
    {
        return \Integrations\Zoho\Entities\Course::FromCourse($this);
    }

    /*
    add unit to course
    Parameter: $unit : unit data requested to add to the courese
    $masterUnit : master unit record (record from rto_units) for the requested unit
    ($unit and $masterUnit can be different if masterUnit data is edited the updated info comes in $unit)
    $subject (subject to add the unit to
    $template: Template that the unit is being added to can be empty or master template
    (master template will have no bar for unit to add but other will restrict according to the packaging rules)
    */
    public function addUnitToCourse(CourseUnitDTO $unit, Units $masterUnit, Subject|CourseSubjects|null $subject = null, ?CourseTemplate $template = null)
    {
        $courseId = $this->id ?? null;
        if (empty($courseId)) {
            $unit->process_result = 'failed';

            return $unit;
        }
        $subjectData = [
            'id' => $unit->primary_id ?? null,
            'course_id' => $courseId,
            'subject_type' => $unit->Type ?? 'Elective',
            'subject_code' => $unit->subject_code ?? '',
            'subject_name' => $unit->subject_name ?? '',
            'subject_display_order' => $unit->subject_display_order ?? null,
            'is_active' => 1,
        ];

        if ($subject && ! ($subject instanceof CourseSubjects)) {
            $subject = null;
        }
        $subjectCreated = empty($unit->primary_id);
        if (empty($subject) && ! empty($subjectData['subject_code'])) {
            // get the subject data
            $subject = CourseSubjects::syncSubject($subjectData);
        }
        $courseSubjectId = $subject->id ?? null;
        /*
        $subject can have null value; subject
        having null value indicates that the unit is not assigned to any subject
        */
        $canHold = false;
        if ($template) {
            /*
            the unit is being added to course template
            check if the template can hold more units
            */
            $canHold = $template->hasSpaceForUnit($unit);
        }

        $unitData = $unit->unit_details ?? [];
        $unitId = $unitData['id'] ?? null;
        if (empty($unitId)) {
            $unitDataArr = [
                'id' => null,
                'unit_id' => $unitData['unit_id'] ?? $masterUnit->id ?? null,
                'course_id' => $unitData['course_id'] ?? $courseId ?? null,
                'course_subject_id' => $courseSubjectId,
                'unit_code' => $unitData['unit_code'] ?? $masterUnit->unit_code ?? null,
                'vet_unit_code' => $unitData['vet_unit_code'] ?? $masterUnit->vet_unit_code ?? null,
                'unit_name' => $unitData['unit_name'] ?? $masterUnit->unit_name ?? null,
                'description' => $unitData['description'] ?? $masterUnit->description ?? null,
                'unit_type' => $unitData['unit_type'] ?? $masterUnit->unit_type ?? null,
                'field_education' => $unitData['field_education'] ?? $masterUnit->field_education ?? null,
                'delivery_mode' => $unitData['delivery_mode'] ?? $masterUnit->delivery_mode ?? null,
                'internal' => $unitData['internal'] ?? $masterUnit->internal ?? null,
                'external' => $unitData['external'] ?? $masterUnit->external ?? null,
                'workplace_based_delivery' => $unitData['workplace_based_delivery'] ?? $masterUnit->workplace_based_delivery ?? null,
                'nominal_hours' => $unitData['nominal_hours'] ?? $masterUnit->nominal_hours ?? null,
                'tution_fees' => $unitData['tution_fees'] ?? $masterUnit->tution_fees ?? null,
                'domestic_tution_fees' => $unitData['domestic_tution_fees'] ?? $masterUnit->domestic_tution_fees ?? null,
                'module_unit_flag' => $unitData['module_unit_flag'] ?? $masterUnit->module_unit_flag ?? null,
                'vet_flag' => $unitData['vet_flag'] ?? $masterUnit->vet_flag ?? null,
                'work_placement' => $unitData['work_placement'] ?? $masterUnit->work_placement ?? null,
                'AVETMISS_Report' => $unitData['AVETMISS_Report'] ?? $masterUnit->AVETMISS_Report ?? null,
            ];
            $unitData = $unitDataArr;
        }
        /*
        if the unit has not been selected (not added) and the template is not a master template
        we will only remove the unit from the template but won't be removed from the course
        (will remain in master template)
        */
        if ($unit->Added == false && $template && $template->id > 0) {
            $addedUnit = SubjectUnits::where(['course_id' => $courseId, 'unit_code' => $unitData['unit_code']])->first();
            if ($addedUnit && $addedUnit->id) {
                $addedUnit->status = 0;
                $template->mapUnitToTemplate($addedUnit);
                if ($subject) {
                    $subject->is_active = 0;
                    $template->mapSubjectToTemplate($subject);
                }
                $addedUnit->process_result = 'success';

                return $addedUnit->toArray();
            }
        }
        /*
        if the unit can be added to the course in selected template
        */
        if ($canHold) {
            // insert the unit to the course
            // get unit if already added
            $unitData['course_subject_id'] = $courseSubjectId ?? $unitData['course_subject_id'];
            $unitData['unit_code'] = $unit->Code ?? $unitData['unit_code'];
            $unitData['unit_name'] = $unit->Title ?? $unitData['unit_name'];
            $unitData['unit_type'] = $unit->Type ?? $unitData['unit_type'];
            $unitData['status'] = $unit->Added ? 1 : 0;
            $unitData['unit_source'] = ($unit->Source == 'api') ? 'package' : 'custom';
            // get the old data if exist
            $addedUnit = SubjectUnits::where(['course_id' => $courseId, 'unit_code' => $unitData['unit_code']]
            )->first();
            if ($addedUnit) {
                $allowed = $addedUnit->canBeUpdated($unitData);
                if (! $allowed) {
                    $unitData['process_result'] = 'failed';
                    $unitData['not_allowed'] = true;

                    return $unitData;
                }
                $addedUnit->fill($unitData)->update();
            } else {
                $addedUnit = new SubjectUnits($unitData);
                $addedUnit->save();
            }

            $addedUnit->process_result = 'success';

            // $template->adjustCoreUnitsAcrossAllTemplates();
            return $addedUnit;
        } else {
            $unitData['process_result'] = 'failed';
            $unitData['quota_exceeded'] = true;
        }

        return $unitData;
    }

    public function updatePackagingRules($data = null)
    {
        // core units
        $coreCount = $this->addedUnits->where('unit_type', SiteConstants::CORETEXT)->count();
        $electiveCount = $this->addedUnits->where('unit_type', SiteConstants::ELECTIVETEXT)->count();
        $coreUnits = $data['no_of_core_subject'] ?? 0;
        $electiveUnits = $data['no_of_elective_subject'] ?? 0;
        if ($coreCount > $coreUnits) {
            // there are alreay more units added so the packaging rule can not be less than that
            throw new \Exception(config_locale(['messages.courses.coreexceeded', ['count' => $coreCount]]));
        }
        if (! $data['is_master_template'] && $electiveCount > $electiveUnits) {
            // there are alreay more units added so the packaging rule can not be less than that
            throw new \Exception(config_locale(['messages.courses.electiveexceeded', ['count' => $electiveCount]]));
        }
        $this->core_units_number = $coreUnits;
        $this->elective_units_number = $electiveUnits;
        $this->save();
        // update the master template as well
        $masterTemplate = CourseTemplate::where([
            'course_id' => $this->id,
            'is_master_template' => 1,
        ])->first();
        if ($masterTemplate) {
            $masterTemplate->no_of_core_subject = $coreUnits;
            $masterTemplate->no_of_elective_subject = $electiveUnits;
            $masterTemplate->save();
        }
    }

    public function getMoodlePayload($params = null): Entity
    {
        return \Domains\Moodle\Entities\Category::FromCourse($this, $params);
    }

    public function getMoodleRepository(): MoodleRepository
    {
        return Moodle::categories();
    }

    public function updateFromMoodle(Entity $course)
    {

        $parts = explode(':', $course->name);
        $this->fill([
            'course_code' => @$parts[0],
            'course_name' => @$parts[1],
            // 'course_description' => $payload->summary
        ]);

        $this->save();

        $this->saveMoodleItem($this->moodleItem()->firstOrCreate([
            'name' => 'moodle',
        ]), $course);
    }

    public function getMoodleRemovePayload($id, $params = null)
    {
        return [
            [
                'id' => $id,
                'recursive' => @$params['recursive'] ?? 0,
            ],
        ];
    }

    /* HANDLE COURSE CREATING LOGIC FROM MOODLE */
    public static function CreateFromMoodle(Entity $course)
    {
        $parts = explode(':', $course->name);
        $model = Courses::create([
            'course_code' => @$parts[0],
            'course_name' => @$parts[1],
        ]);

        $model->saveMoodleItem($model->moodleItem()->firstOrCreate([
            'name' => 'moodle',
        ]), $course);
    }

    public function getRelatedCourses($sameType = true)
    {
        $tags = $this->course_tags ?? null;
        $relatedCourses = null;
        if (! empty($tags)) {
            $tags = explode(',', $this->course_tags);
            // Build the FIND_IN_SET conditions dynamically
            $findInSetConditions = collect($tags)->map(function ($tag) {
                $courseTag = trim($tag);

                return "(FIND_IN_SET('$courseTag', REPLACE(rto_courses.course_tags, ' ', '')) > 0)";
            })->implode(' + ');

            // Query to calculate common tags
            $relatedQuery = Courses::with([
                'intakes' => function ($q) {
                    $q->with('campusIntakes', function ($cq) {
                        $cq->with('campus');
                    })
                        ->where('intake_start', '>=', Carbon::now())
                        ->where('active', '=', 1)
                        ->orderBy('intake_start');
                },
            ])
                ->select(['*', DB::raw("($findInSetConditions) as common_tag_count")])
                ->where('activated_now', '=', 1)
                ->where('id', '!=', $this->id)
                ->having('common_tag_count', '>', 0)
                ->orderByDesc('common_tag_count')
                ->limit(10);
            if ($sameType) {
                $relatedQuery->where('course_type_id', $this->course_type_id);
            }
            $relatedCourses = $relatedQuery->get();
        }
        $this->setRelation('related_courses', $relatedCourses);
    }

    public function setUpSubjectUnitForShortCourse()
    {
        /* first add unit */
        /* get the unit details from the api */
        $coursesApi = new CoursesApiController;
        // get details form the code given
        $unitCodeDetails = $coursesApi->nationCodeApiCall($this->course_code, true);

        $unitDetails = $unitCodeDetails['GetDetailsResult'] ?? [];
        $unitCode = $unitDetails['Code'] ?? '';
        $agreedNominalHrs = AgreedNominalHours::where('status', 1)->where('code', $unitCode)->value('agreed_hours');
        $unit_type = SiteConstants::CORETEXT;
        $classification = $unitDetails['Classifications']['Classification'] ?? [];
        $fieldOfEducation = $classification[0]['ValueCode'] ?? null;
        $nominalHours = $agreedNominalHrs ?? null;
        $active_from = @$unitDetails['CurrencyPeriods']['NrtCurrencyPeriod']['StartDate'] ?? Carbon::now();

        $unitInserData = [
            'ID' => null,
            'Code' => $unitCode,
            'Title' => $unitDetails['Title'] ?? '',
            'Type' => $unit_type,
            'unit_code' => $unitCode,
            'vet_unit_code' => $unitCode,
            'unit_name' => $unitDetails['Title'] ?? '',
            'description' => $unitDetails['Description'] ?? '',
            'unit_type' => $unit_type,
            'field_education' => $fieldOfEducation,
            'nominal_hours' => $nominalHours,
            'vet_flag' => ! empty($unitDetails) ? 1 : 0,
            'AVETMISS_Report' => ! empty($unitDetails) ? 1 : 0,
            'status' => 1,
            'Added' => 1,
            'unit_type_basic' => $unit_type,
            'unit_source' => 'api',
            'Source' => 'api',
            'unit_scope' => 'course',
            'active_from' => $active_from,
            'is_superseded' => 0,
            'superseded_date' => null,
            'superseded_by' => null,
        ];

        return $unitInserData;
        // $masterUnitsData = SyncUnitsSetupService::SaveUnitsToMasterUnitsCollectionTable(collect([collect($unitInserData)]));
        // $masterUnit = $masterUnitsData->first();

        // $unit = $this->addUnitToCourse(CourseUnitDTO::LazyFromArray($unitInserData), $masterUnit);
        // $unit = SubjectUnits::find($unit['id']);
        // $service = new CourseSetupService;
        // $subjectData = [
        //     'subject_type' => $unit_type,
        //     'grading_type' => 'none',
        //     'contact_hours' => $nominalHours,
        //     'inc_in_certificate' => 1,
        //     'is_active' => 1,
        // ];

        // $service->assignSoloSubjectToUnit($unit, $this, $subjectData);

    }

    public function isShortCourse()
    {
        return $this->course_type_id === SiteConstants::SHORT_COURSE_TYPE_ID;
    }

    public function isVetCourse()
    {
        return $this->course_type_id === SiteConstants::VET_COURSE_TYPE_ID;
    }

    public function isHigherEd()
    {
        return $this->course_type_id === SiteConstants::HIGHERED_COURSE_TYPE_ID;
    }
}
