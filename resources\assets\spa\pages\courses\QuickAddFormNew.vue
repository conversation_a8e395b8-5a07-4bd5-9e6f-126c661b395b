<template lang="">
    <SidebarDrawer
        :visibleDialog="props.visible"
        :hideOnOverlayClick="false"
        :fixedActionBar="!props.loading"
        :width="'650px'"
        @drawerclose="cancelProcess"
        @drawersaved="handleSaveForm"
        :primaryBtnLabel="primaryBtnLabel"
        :isDisabled="isSaving || props.loading"
        :isSubmitting="isSaving"
        :wrapperClass="'custom-modal-wrapper'"
    >
        <template #title>
            <div class="text-lg font-medium">{{ modelTitle }}</div>
        </template>
        <template #content>
            <div v-if="props.loading" class="space-y-2">
                <div class="animate-pulse text-lg">
                    Please wait, course units are being fetched from training.gov.au
                </div>
                <ContextLoader :template="'skeleton-list'" :skeletonSize="'large'" />
            </div>
            <div id="courses-spa" v-else>
                <form @submit.prevent="handleSubmit" method="post" novalidate v-if="!newCompetency">
                    <div class="mb-1 font-medium text-gray-700">Select Course Type</div>
                    <div class="tw-radio-group mb-6 bg-white">
                        <RadioGroup
                            :data-items="getCourseTypeItems"
                            :id="'course_types'"
                            v-model="courseType"
                        />
                        <div v-if="v$.courseType.$error" class="text-red-500">
                            Select a course type.
                        </div>
                    </div>
                    <div v-if="props.custom">
                        <div class="mb-4">
                            <label
                                for="course_code"
                                class="mb-1 inline-block font-medium text-gray-700"
                                >Enter Course Code</label
                            >
                            <Input
                                :id="'course_code'"
                                v-model="courseCode"
                                :class="'w-full uppercase'"
                                @input="restrictInput"
                            />
                            <div v-if="v$.courseCode.$error" class="text-red-500">
                                Course code is required and should contain only alphabets and
                                numbers.
                            </div>
                        </div>
                        <div class="mb-4">
                            <label
                                for="course_title"
                                class="mb-1 inline-block font-medium text-gray-700"
                                >Enter Course Title</label
                            >
                            <Input :id="'course_title'" v-model="courseTitle" :class="'w-full'" />
                            <div v-if="v$.courseTitle.$error" class="text-red-500">
                                Course title is required.
                            </div>
                        </div>
                    </div>
                    <div class="space-y-6" v-else-if="canAddUnits">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <div class="text-sm">You have added</div>
                                <div
                                    class="rounded-md bg-primary-blue-100 px-3 py-1 text-xs text-primary-blue-800"
                                >
                                    {{ selectedCore }} / {{ getCourseCoreUnits.length }} Core Units
                                </div>
                                <div class="rounded-md bg-gray-100 px-3 py-1 text-xs text-gray-800">
                                    {{ selectedElective }} /
                                    {{ getCourseElectiveUnits.length }} Elective Units
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" v-if="!props.loading">
                            <PrimaryButton @click="addUnits" class="w-auto">
                                <div class="flex w-auto items-center">
                                    <icon :name="'plus'" :width="16" :height="16" :fill="'white'" />
                                    <div class="ml-2">New Unit Of Competency</div>
                                </div>
                            </PrimaryButton>
                        </div>
                        <div class="UnitsAdded rounded-md border px-4">
                            <div v-for="(unit, index) in getCourseCoreUnits" class="">
                                <div
                                    class="mb-2 flex items-center space-x-1 border-b py-2 text-sm"
                                    v-if="unit.Added"
                                >
                                    <div class="mr-2">
                                        <icon :name="'draghandle'" :width="20" :height="20" />
                                    </div>
                                    <unit-type :type="'core'" :text="'Core'" />
                                    <div class="text-gray-500">
                                        {{ unit.Code }}
                                    </div>
                                    <div class="text-gray-700">
                                        {{ unit.Title }}
                                    </div>
                                </div>
                            </div>
                            <div v-for="(unit, index) in getCourseElectiveUnits" class="">
                                <div
                                    class="mb-2 flex items-center justify-between space-x-1 border-b py-2 text-sm"
                                    v-if="unit.Added"
                                >
                                    <div class="flex items-center space-x-1">
                                        <div class="mr-2">
                                            <icon :name="'draghandle'" :width="20" :height="20" />
                                        </div>
                                        <unit-type :type="'elective'" :text="'Elective'" />
                                        <div class="text-gray-500">
                                            {{ unit.Code }}
                                        </div>
                                        <div class="text-gray-700">
                                            {{ unit.Title }}
                                        </div>
                                    </div>
                                    <div class="cursor-pointer" @click="removeUnit(unit)">
                                        <icon :name="'cross'" :width="20" :height="20" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <form @submit.prevent="handleSubmit" method="post" novalidate v-else>
                    <div>
                        <div class="font-semibold text-gray-900">
                            Which Unit would you like to add?
                        </div>
                        <div class="text-gray-500">
                            Below is the list of units from
                            <a href="https://training.gov.au/" target="_blank" class="text-gray-500"
                                >https://training.gov.au/</a
                            >. Use the search box to filter the units.
                        </div>
                    </div>
                    <div v-if="hasUnits">
                        <div class="mt-4">
                            <div class="flex w-full">
                                <div class="searchCoursesToAdd relative w-full">
                                    <span class="absolute left-2 top-1/2 -translate-y-1/2">
                                        <icon :name="'lens'" width="16" height="16" />
                                    </span>
                                    <input
                                        type="text"
                                        id="searchCourse"
                                        v-model="filterUnits"
                                        class="tw-input-text pl-8"
                                        placeholder="Search by unit"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="mt-6 rounded-md border p-4">
                            <div class="mb-2 flex items-center border-b py-2">
                                <checkbox
                                    :id="'toggleall'"
                                    v-model="allSelected"
                                    @change="toggleAllSelect"
                                    :disabled="filterUnits != ''"
                                >
                                    <label
                                        class="k-checkbox-label"
                                        :for="'toggleall'"
                                        :style="{ display: 'inline-block' }"
                                    >
                                        <div class="flex items-center space-x-2 text-xs">
                                            {{ getToggleText }}
                                        </div>
                                    </label>
                                </checkbox>
                            </div>
                            <div v-for="(unit, index) in getCourseCoreUnits">
                                <div
                                    class="mb-2 flex items-center border-b py-2 [&>*:last-child]:border-b-0"
                                    :key="index"
                                    v-if="checkIfUnitVisible(unit)"
                                >
                                    <checkbox
                                        :id="unit.Code"
                                        v-model="courseUnits.core[index].Added"
                                        :checked="true"
                                    >
                                        <label
                                            class="k-checkbox-label"
                                            :for="unit.Code"
                                            :style="{ display: 'inline-block' }"
                                        >
                                            <div class="flex items-center space-x-2 text-xs">
                                                <unit-type :type="'core'" :text="'Core'" />
                                                <div class="text-gray-500">
                                                    {{ unit.Code }}
                                                </div>
                                                <div class="text-gray-700">
                                                    {{ unit.Title }}
                                                </div>
                                                <CourseStatusBtn
                                                    :status="selectedUnits[unit.Code] ? 4 : null"
                                                />
                                            </div>
                                        </label>
                                    </checkbox>
                                </div>
                            </div>
                            <div v-for="(unit, index) in getCourseElectiveUnits">
                                <div
                                    class="flex items-center border-b py-2 [&>*:last-child]:border-b-0"
                                    v-if="checkIfUnitVisible(unit)"
                                >
                                    <checkbox
                                        :id="unit.Code"
                                        v-model="courseUnits.elective[index].Added"
                                        @change="updateChanges"
                                    >
                                        <label
                                            class="k-checkbox-label"
                                            :for="unit.Code"
                                            :style="{ display: 'inline-block' }"
                                        >
                                            <div class="flex items-center gap-2 text-sm">
                                                <unit-type :type="'elective'" :text="'Elective'" />
                                                <div class="ml-3 text-gray-500">
                                                    {{ unit.Code }}
                                                </div>
                                                <div class="text-gray-700">
                                                    {{ unit.Title }}
                                                </div>
                                                <CourseStatusBtn
                                                    :status="selectedUnits[unit.Code] ? 4 : null"
                                                />
                                            </div>
                                        </label>
                                    </checkbox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="mt-6 rounded-md border p-4">
                        <div class="text-lg font-medium text-gray-700">
                            There are currently no units accessible for the chosen course.
                        </div>
                        <div class="text-gray-600">
                            Please proceed with the course addition at this time, as you will have
                            the option to manually add units at a later stage.
                        </div>
                    </div>
                </form>
            </div>
        </template>
    </SidebarDrawer>
</template>
<script setup>
import { useVuelidate } from '@vuelidate/core';
import { required, alphaNum, integer, minValue } from '@vuelidate/validators';
import { resetObj, mergeFirstObject, groupBy, parseIntKeys } from '@spa/helpers';
import { ref, computed, watch } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import CourseStatusBtn from './commons/CourseStatusButtons.vue';
import { RadioGroup, Input, Checkbox } from '@progress/kendo-vue-inputs';
import { Label } from '@progress/kendo-vue-labels';
import { filterBy } from '@progress/kendo-data-query';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import ContextLoader from '@spa/components/Loader/ContextLoader.vue';

const $page = usePage();

const props = defineProps({
    course: { default: null },
    visible: { default: false },
    loading: { default: false },
    custom: { default: false },
    searchText: { default: '' },
    page: { default: null },
    take: { default: null },
    coursetypes: { default: null },
});
const emit = defineEmits(['update:visible', 'added', 'closed']);

const courseType = ref(null);
if (props.custom == true) {
    courseType.value = 0;
} else if (props.course?.ComponentType == 'Unit' || props.course?.ComponentType == 'SkillSet') {
    courseType.value = 16;
} else {
    courseType.value = props.course?.course_type_id || null;
}

const courseUnits = ref(props.course?.units || []);
const courseCode = ref('');
const courseCodeValid = ref(false);
const isSaving = ref(false);
const courseTitle = ref('');
const filterUnits = ref('');
const selectedCore = ref(0);
const selectedElective = ref(0);
//variable to store the units selected
const selectedUnits = ref({});
//variable to store the units selected to save
const addedUnits = ref({});

const newCompetency = ref(false);
const allSelected = ref(false);
const getCourseTypeItems = computed(() => {
    const allowedCourseIds = [2, 16];
    if (!props.custom) {
        return props.coursetypes?.filter((item) => allowedCourseIds.includes(item.value));
    }
    return props.coursetypes || [];
});
const modelTitle = computed(() => {
    if (props.loading) {
        return 'Please wait... Collecting units for the course.';
    }
    return props.course?.Code
        ? `Add Course - [${props.course?.Code}] ${props.course?.Title}`
        : 'Add New Course';
});

const primaryBtnLabel = computed(() => {
    if (isSaving.value) {
        return 'Adding this course';
    } else if (newCompetency.value) {
        return `Add${countUnitsSelected.value > 0 ? ` ${countUnitsSelected.value}` : ''}${
            countUnitsSelected.value > 1 ? ' Units' : ' Unit'
        }`;
    } else {
        return 'Quick add course';
    }
});
const hasUnits = computed(() => {
    return courseUnits.value?.core?.length + courseUnits.value?.elective?.length > 0;
});
const rules = computed(() => ({
    courseType: {
        required,
        integer,
        minValueValue: minValue(1),
    },
    courseCode: {
        required,
        alphaNum,
    },
    courseTitle: {
        required,
    },
}));
const isShortCourse = computed(() => {
    return courseType.value == 16;
});
const canAddUnits = computed(() => {
    return isShortCourse.value == false || hasUnits.value == true;
});
const v$ = useVuelidate(rules, { courseType, courseCode, courseTitle });

const restrictInput = () => {
    // Remove any non-alphanumeric characters from the input
    courseCode.value = courseCode.value.replace(/[^a-zA-Z0-9]/g, '');
};

//get the course units
//get all core units // all core units should be added/selected automatically
const getCourseCoreUnits = computed(() => {
    let units = [];
    courseUnits.value?.core?.forEach((unit) => {
        if (!unit.Added) {
            unit.Added = true;
        }
        units.push(unit);
    });
    return units;
});
//get all elective units
const getCourseElectiveUnits = computed(() => {
    let units = [];
    courseUnits.value?.elective?.forEach((unit) => {
        units.push(unit);
    });
    return units;
});
const checkIfUnitVisible = (unit) => {
    if (filterUnits.value) {
        const hasInCode = unit.Code.toLowerCase().includes(filterUnits.value.toLowerCase());
        const hasInTitle = unit.Title.toLowerCase().includes(filterUnits.value.toLowerCase());
        if (!hasInCode && !hasInTitle) {
            return false;
        }
    }
    return true;
};
//watch for the changes in course units
watch(
    () => props.course,
    (newCourse) => {
        selectedUnits.value = {};
        selectedCore.value = 0;
        selectedElective.value = 0;
        if (newCourse) {
            newCourse.units?.core?.forEach((unit) => {
                selectedCore.value += 1;
                selectedUnits.value[unit.Code] = true;
            });
            newCourse.units?.elective?.forEach((unit) => {
                selectedElective.value += unit.Added ? 1 : 0;
                selectedUnits.value[unit.Code] = unit.Added ? true : false;
            });
            courseUnits.value = newCourse.units;
            if (newCourse.ComponentType == 'Unit' || newCourse.ComponentType == 'SkillSet')
                courseType.value = 16;
        }
    }
);
const countUnitsStatus = () => {
    selectedCore.value = 0;
    selectedElective.value = 0;
    courseUnits.value?.core?.forEach((unit) => {
        selectedCore.value += unit.Added ? 1 : 0;
        //selectedUnits.value[unit.Code] = unit.Added ? true : false;
    });
    courseUnits.value?.elective?.forEach((unit) => {
        selectedElective.value += unit.Added ? 1 : 0;
        //selectedUnits.value[unit.Code] = unit.Added ? true : false;
    });
};
const updateChanges = () => {
    countUnitsStatus();
    const selecetedValues = selectedCore.value + selectedElective.value;
    const availableValues = courseUnits.value?.length + courseUnits.value?.length;
    allSelected.value = selecetedValues == availableValues;
};
const removeUnit = (unit) => {
    unit.Added = false;
    countUnitsStatus();
};
const toggleAllSelect = () => {
    for (const item of courseUnits.value?.core) {
        item.Added = true;
    }
    for (const item of courseUnits.value?.elective) {
        item.Added = allSelected.value;
    }
    countUnitsStatus();
};
const getToggleText = computed(() => {
    return allSelected.value == false ? 'Select All' : 'Clear All';
});
const countUnitsSelected = computed(() => {
    return selectedCore.value + selectedElective.value;
});
const cancelProcess = () => {
    if (newCompetency.value == true) {
        newCompetency.value = false;
        return;
    }
    courseType.value = props.custom == true ? 0 : 2;
    courseCode.value = '';
    courseTitle.value = '';
    courseUnits.value = [];
    emit('closed');
};

const addUnits = () => {
    newCompetency.value = true;
    //console.log(addedUnits.value);
};
const addCourseUnits = () => {
    if (newCompetency.value == true) {
        newCompetency.value = false;
        addedUnits.value = selectedUnits.value;
        //selectedUnits.value = {};
    }
};
const quickAddCourse = async (e) => {
    if (isSaving.value == true) {
        return;
    }
    //save the course
    let CourseData;
    if (props.custom) {
        const result = await v$?.value.$validate();
        if (!result) {
            return;
        }
        CourseData = { Code: courseCode.value, Title: courseTitle.value };
    } else {
        CourseData = props.course;
    }
    isSaving.value = true;
    emit('update:visible', false);
    router.post(
        route('spa.courses.quickadd'),
        {
            searchText: props.searchText,
            page: props.page,
            take: props.take,
            type: courseType.value,
            course: CourseData,
            custom: props.custom,
        },
        {
            onBefore: (visit) => {
                isSaving.value = true;
            },
            onStart: (visit) => {
                isSaving.value = true;
            },
            onSuccess: (resp) => {
                if (newCompetency.value == true) {
                    newCompetency.value = false;
                }
                isSaving.value = false;
            },
            onFinish: (visit) => {
                isSaving.value = false;
            },
        }
    );
};

const handleSaveForm = (event) => {
    if (props.loading) {
        return false;
    }
    if (props.custom !== true && newCompetency.value == true) {
        addCourseUnits();
    } else {
        quickAddCourse();
    }
};
</script>
<style lang=""></style>
