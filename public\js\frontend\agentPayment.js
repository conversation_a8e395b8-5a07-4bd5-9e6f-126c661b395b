var AgentPayment = function() {
    $('#from_date').val('');
    var processCommission = function() {
        radioCheckboxClass();
        dateFormateNew('#from_date,#to_date,#commission_paid_date');
     
//        $('body').on('click', '.check_or_not', function() {
//            if ($(this).is(":checked")) {
//                $('.check_all').prop('checked', true);
//            }
//            else {
//                $('.check_all').prop('checked', false);
//            }
//        });

        $('body').on('ifChecked', '.check_or_not', function(event) {
            $(':checkbox').each(function() {
                $('.check_all').iCheck('check');
            });
        });

        $('body').on('ifUnchecked', '.check_or_not', function(event) {
            $(':checkbox').each(function() {
                $('.check_all').iCheck('uncheck');
            });
        });

        $('#date_option').on('change', function() {
            $('#from_date').val('');
            $('#to_date').val('');
            var dateOption = $('#date_option').val();
            if (dateOption == 2) {
                $('#betweenDateField').show();
                   checkDateRange('.dateField', '#from_date', '#to_date', 'From Date Must be Greater From To Date');
            }
            else {
                $('#from_date').val('');
                $('#betweenDateField').hide();
            }
        });

        $('#view_option').on('change', function() {
            var viewOption = $('#view_option').val();
            if (viewOption == 2) {
                $('#search_by_div').hide();
                $("#search_by option:contains(" + '- - Select Search Option - -' + ")").prop('selected', 'selected');
            } else {
                $('#search_by_div').show();
            }
            setTimeout(function() {
                $('#date_option').trigger('click');
            }, 100);

            $('#viewData').trigger('click');
        });

        setTimeout(function() {
            $('#view_option').trigger('click');
            $('#viewData').trigger('click');
        }, 1000);

        $('#viewData').on('click', function() {
            processCommissionList();
        });

        $('body').on('click', '.approve-check', function() {
            let viewOption = $('#view_option').val();
            let approveCheckAll = {};
            $(".check_all").each(function() {
                if ($(this).is(":checked")) {
                    approveCheckAll[$(this).val()] = $(this).closest('tr').find('.remarks').val();
                }
            });
            
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                url: site_url + "agent-payment/ajaxAction",
                data: {'action': 'approveAllProcess', 'data': {'viewOption': viewOption, 'approveCheckAll': approveCheckAll}},
                success: function(data) {
                    

                    var result = jQuery.parseJSON(data);                    
                   
                    // sessionDisplayMessage(result.action, result.msg);
                    if(result.action == 'alert-danger'){
                        showToster('error', result.msg, '');   
                    }else{
                        showToster('success', result.msg, '');
                    }
                    processCommissionList();
                   
                }
            });
        });

        function processCommissionList() {

            var viewOption = $('#view_option').val();
            var agentId = $('#agent_id').val();
            var toDate = $('#to_date').val();
            var fromDateValue = $('#from_date').val();


            if (viewOption == 2) {
                var searchBy = 0;
            }
            else {
                var searchBy = $('#search_by').val();
            }

            if (fromDateValue != '') {
                var fromDate = fromDateValue;
            }
            else {
                var fromDate = 0;
            }


            if (viewOption && toDate != '') {
                $.ajax({
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    url: site_url + "agent-payment/ajaxAction",
                    data: {'action': 'getAgentCommissionProcess', 'data': {'viewOption': viewOption, 'searchBy': searchBy, 'agentId': agentId, 'toDate': toDate, 'fromDate': fromDate}},
                    success: function(data) {
                        var obj = jQuery.parseJSON(data);
                        $("#processCommission").html(obj.html);
                        radioCheckboxClass();

                        //noRecordFoundNew("#processCommission tbody", obj.length, 16);

                        // if (obj.count > 0) {
                        //     $('#noRecords').hide();
                        // } else {
                        //     $('#noRecords').show();
                        // }

                        if (viewOption == 2) {
                            $('.delete_icon').show();
                            $('.single-process').show();
                            $('#syncToXero').show();
                            $('#createPO').show();
                            $('#approve_commission').hide();
                            //$('#approve_process').show();
                        } else {
                            $('.delete_icon').hide();
                            $('.single-process').hide();
                            $('#syncToXero').hide();
                            $('#createPO').hide();
                            $('#approve_commission').show();
                            //$('#approve_process').hide();
                        }
                    },
                });
            }
            else {
                alert('Please Select all field');
            }
        }

        $('body').on('click', '#syncToXero', function() {
            getSelectedComm('#syncConfirmModal');
        });

        $('body').on('click', '#createPO', function() {
            getSelectedComm('#poConfirmModal');
        });

        $('body').on('click', '.syncConfirm', function() {
            syncData('#syncConfirmModal');
        });

        $('body').on('click', '.poConfirm', function() {
            syncData('#poConfirmModal');
        });

        function syncData(modalId){
            let selectedIds = $(modalId).find('.commissionIds').val();

            let dataArr = {
                'action': 'bulkSyncOrCreatePO',
                'data': {
                    'selectedIds': selectedIds.split(',')
                }
            };
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                url: site_url + "agent-payment/ajaxAction",
                data: dataArr,
                success: function(data) {
                    let res = jQuery.parseJSON(data);
                    showToster(res.status, res.msg, '');
                    $(modalId).modal('hide');
                    processCommissionList();
                }
            });
        }

        function getSelectedComm(modalId){
            let idArr = $(".check_all:checked").map(function() {
                return $(this).val();
            }).get();

            if(idArr.length == 0){
                showToster('error', 'Please select at least one record', '');
                return false;
            }

            $(modalId).modal('show');
            $(modalId).find('.commissionIds').val(idArr);
        }

        // Single Process
        $('body').on('click', '.single-process', function() {
            let agentCommId = $(this).attr('data-id');
            if (agentCommId > 0) {
                $.ajax({
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    url: site_url + "agent-payment/ajaxAction",
                    data: {'action': 'getSingleProcessForCommission', 'data': {'id': agentCommId}},
                    success: function(response) {
                        let res = jQuery.parseJSON(response);
                        if(res.status == 'success'){
                            $('#formatted_invoice_no_value').html(res.data.formatted_invoice_number);
                            $('#total_amount_payable_value').html(res.data.commission_payable);
                            $('#gst_amount_value').html(res.data.gst_amount);
                            $('#total_amount_value').html(res.data.commission_paid);
                            $('#hidden_processing_id').val(agentCommId);
                            $('#processingModal').modal('show');
                        }else{
                            showToster(res.status, res.msg, '');
                            return false;
                        }
                    },
                });
            }
        });

        $("#processing_modal_form").validate({
            rules: {
                payment_mode: {
                    required: true
                },
                commission_paid_date: {
                    required: true
                },
                remarks_processing: {
                    required: true
                },
            },
            errorPlacement: function(error, element) {
                // error;
            }
        });

        $('#processing_modal_form').on('submit', function(e) {
            e.preventDefault();
            let dataArr = {
                'id'        : $('#hidden_processing_id').val(),
                'mode'      : $('#payment_mode').val(),
                'paid_date' : $('#commission_paid_date').val(),
                'remarks'   : $('#remarks_processing').val()
            };
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "agent-payment/ajaxAction",
                data: {'action': 'saveSingleProcessForCommission', 'data': dataArr},
                success: function(response) {
                    let res = jQuery.parseJSON(response);
                    showToster(res.status, res.msg, '');
                    if(res.status == 'success'){
                        $('#processingModal').modal('hide');
                        processCommissionList();
                    }
                },
            });

        });

        // Bulk Approve
        /*$('.approve-process').on('click', function() {

            var processCheckAll = [];
            $(".check_all").each(function() {
                if ($(this).is(":checked")) {
                    var newVal = $(this).val();
                    processCheckAll.push(newVal);
                }
            });
            if (processCheckAll.length > 0) {
                $.ajax({
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    url: site_url + "agent-payment/ajaxAction",
                    data: {'action': 'getProcessingCommission', 'data': {'processCheckAll': processCheckAll}},
                    success: function(data) {
                        var obj = jQuery.parseJSON(data);

                        $('#total_amount_payable_value').html(obj.commissionPayable);
                        $('#gst_amount_value').html(obj.gstAmount);
                        $('#total_amount_value').html(obj.commissionPaid);

                        var processingId = processCheckAll.toString();
                        $('#hidden_processing_id').val(processingId);

                    },
                });
                $('#processingModal').modal('show');
            } else {
                sessionDisplayMessage('alert-error', 'Please select at least one record');
                
            }
        });

        $("#processing_modal_form").validate({
            rules: {
                invoice_no: {
                    required: true
                },
                payment_mode: {
                    required: true
                },
                commission_paid_date: {
                    required: true
                },
                remarks_processing: {
                    required: true
                },
            },
            errorPlacement: function(error, element) {
                // error;
            }
        });

        $('#processing_modal_form').on('submit', function(e) {
            e.preventDefault();
            var processingId = $('#hidden_processing_id').val();
            var arrProcessingId = new Array();
            arrProcessingId = processingId.split(',');

            for (var i = 0; i < arrProcessingId.length; i++) {
                arrProcessingId[i] = arrProcessingId[i];
            }
            var invoiceNo = $('#invoice_no').val();
            var paymentMode = $('#payment_mode').val();
            var commissionPaidDate = $('#commission_paid_date').val();
            var remarksProcessing = $('#remarks_processing').val();

            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                url: site_url + "agent-payment/ajaxAction",
                data: {'action': 'saveProcessingCommission', 'data': {'arrProcessingId': arrProcessingId, 'invoiceNo': invoiceNo, 'paymentMode': paymentMode, 'commissionPaidDate': commissionPaidDate, 'remarksProcessing': remarksProcessing}},
                success: function(data) {
                    $('#processingModal').modal('hide');
                    processCommissionList();
                },
            });

        });*/

        $('body').on('click', '.exportExcel', function() {
            $('#processCommissionAgent').submit();
        });

        $('body').on('click', '.btn-xero-process', function(e) {
            e.preventDefault();
            showToster('error', 'Xero is connected. Please proceed with payments through Xero only.', '');
        });

        $('body').on('click', '.already-synced', function(e) {
            e.preventDefault();
            showToster('error', 'Synced item cannot be disapproved.', '');
        });

        //Disapprove
        $('body').on('click', '.disapprove', function (e) {
            e.preventDefault();
            $('.yes-sure-disapprove').attr('data-id', $(this).attr('data-id'));
        });

        $('body').on('click', '.yes-sure-disapprove', function(e) {
            e.preventDefault();
            let processingId = $(this).attr('data-id');
            if(processingId){
                $.ajax({
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    url: site_url + "agent-payment/ajaxAction",
                    data: {'action': 'disapproveCommission', 'data': {'processingId': processingId}},
                    success: function(response) {
                        let res = jQuery.parseJSON(response);
                        showToster(res.status, res.msg, '');
                        if(res.status == 'success'){
                            $('#disapproveModal').modal('hide');
                            processCommissionList();
                        }
                    },
                });
            }
        });
    }

    var paymentHistory = function() {

        dateFormate('.dateField');
        var agentParentId = $('#agent_id').val();
        paymentList();
        deleteSingleData(site_url + 'delete-credit-bonus-allocation/', agentParentId);

        $("#creditBonusAllocation").validate({
            rules: {
                amount: {required: true},
                payment_mode: {required: true},
                payment_date: {required: true},
                comment: {required: true}
            },
            messages: {},
            errorPlacement: function(error, element) {
            }
        });
        $("#updateInfo").validate({
            rules: {
                amount: {required: true},
                payment_mode: {required: true},
                payment_date: {required: true},
                comment: {required: true}
            },
            messages: {},
            errorPlacement: function(error, element) {
            }
        });

        $('#type').on('change', function() {
            if ($(this).val() == 2) {
                $('#paymnetMode').hide();
                $('#paymentDate').hide();
            } else {
                $('#paymnetMode').show();
                $('#paymentDate').show();
            }
        });

        paymentList();
//        setTimeout(function() {
//            //handelPayment();
//            $('.filterData').trigger('change');
//        }, 1000);

        $('.filterData').on('change', function() {
            paymentList();
        });

        $('body').on('click', '.payScheduleStudent', function() {
            var paymentDetailId = $(this).attr('id');
            $("#paymentSchesuleForPayment").show();
            $(".dataHide").hide();
            $("." + paymentDetailId + "Data").show();
        });

        function paymentList() {
            var view_type = $('#view_type').val();
            var pay_year = $('#pay_year').val();
            var agent_id = $('#agent_id').val();

            $("#dataAppend").replaceWith("<tbody id='dataAppend'>" + "</tbody>");
            $("#dataAppendInvoice").replaceWith("<tbody id='dataAppendInvoice'>" + "</tbody>");

            if (view_type == 2)
            {
                $("#invoiceAgentCommission").show();
                $("#paymentSchesuleForPayment").hide();
            } else {
                $("#invoiceAgentCommission").hide();
                $("#paymentSchesuleForPayment").show();
            }
            if (view_type != "" && pay_year != "") {
                $.ajax({
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    url: site_url + "agent-payment/ajaxAction",
                    data: {'action': 'agentPaymentView', 'data': {'view_type': view_type, 'pay_year': pay_year, 'agent_id': agent_id}},
                    success: function(data) {
                        //   loadingEnd(that);
                        var obj = jQuery.parseJSON(data);
                        if (view_type == 1) {
                            $('#auto_deducted').show();
                            $('#other_type').hide();
                            $('#invoiced_commission').hide();
                        } else if (view_type == 3) {
                            $('#other_type').show();
                            $('#auto_deducted').hide();
                            $('#invoiced_commission').hide();
                        }else if (view_type == 2) {
                            $('#other_type').hide();
                            $('#auto_deducted').hide();
                            $('#invoiced_commission').show();
                        }

                        $.each(obj, function(i, item) {
                            var invoiceAmount = parseInt(item.commission_paid)
                            var tableInvoice = '<tr id="dataAppendInvoice">' +
                                    '<td>' + item.formatted_invoice_number +
                                    '<div class="action-overlay"><ul class="icon-actions-set"><li>' + '<a class="link-black text-sm payScheduleStudent" data-toggle="tooltip" data-original-title="View Schedule Payment Info" style="font-size: 13px;" id="' + item.id + '"><i class="fa fa-search margin-r-5"></i></a>' + '</li></ul></div>' +
                                    '</td>' +
                                    '<td>' + item.paid_date + '</td>' +
                                    '<td>$' + invoiceAmount + '</td>' +
                                    '</tr>';

                            $("#dataAppendInvoice").append(tableInvoice);
                            var table = '<tr id="dataAppend" class="' + item.id + "Data" + ' dataHide"><td>' +
                                    item.agency_name + '</td><td>' +
                                    //item.transaction_no + '</td><td>' +
                                    item.formatted_invoice_number + '</td><td>' +
                                    item.generated_stud_id + '</td><td>' +
                                    item.first_name + " " + (item.middel_name != null && item.middel_name!='' ? item.middel_name : '') + '</td><td>' +
                                    item.course_code + '</td><td>' +
                                    1 + '</td><td>$' +
                                    item.upfront_fee_to_pay.toFixed(2) + '</td><td>$' +
                                    item.paid_amount.toFixed(2) + '</td><td>' +
                                    item.comm_paid_date + '</td><td>$' +
                                    item.commission_payable.toFixed(2) + '</td><td>$' +
                                    item.gst_amount.toFixed(2) + '</td><td>$' +
                                    item.commission_paid.toFixed(2) + '</td><td>' +
                                    item.paid_date + '</td><td>' +
                                    (item.remarks != null && item.remarks !='' ? item.remarks : '') + '</td><td>' +
                                    '</td></tr>';
                            $("#dataAppend").append(table);
                        });
                        noRecordFoundNew("#subjectData tbody", obj.length, 15);
                        /*if (obj.length == 0) {
                         var noRecord = '<tr class="text-center" id="noRecords"><td colspan="12"><p  style="color:red;">No Record Found</td></p> </tr>';
                         //console.log(view_type + ' view_type');
                         if (view_type == 1) {
                         $('.type1').show();
                         } else if (view_type == 2) {
                         $('.type2').show();
                         } else if (view_type == 3) {
                         $('.type1').show();
                         }
                         }
                         if (obj.length > 0) {
                         $('.type2').hide();
                         $('.type1').hide();
                         $('#noRecords').hide();
                         }*/
                    },
                    error: function(err) {
                    }
                });
            } else {
                $('.type1').show();
            }
        }
    };

    var creditBonus = function() {
        $('body').on('click', '.viewInfo', function() {
            $('#creditUsedInfoModel').modal('show');
            var bonusId = $(this).attr('data-id');
            var dataArr = {'bonusId': bonusId};
            var url = "agent-payment/ajaxAction";
            var action = 'getCreditAgentStudent';

            ajaxAction(url, action, dataArr, function(resultData) {
//                var result = jQuery.parseJSON(resultData);
                $('#appendBonusData').html(resultData);
            });
        });
        $('body').on('click', '.editInfo', function() {
            $('#creditUsedEditModel').modal('show');
            var bonusId = $(this).attr('data-id');
            var dataArr = {'bonusId': bonusId};
            var url = "agent-payment/ajaxAction";
            var action = 'getCreditAgentEditData';

            ajaxAction(url, action, dataArr, function(resultData) {
                var result = jQuery.parseJSON(resultData);
                console.log(result);
                $('#edit_type').val(result['type']);
                $('#edit_amount').val(result['amount']);
                $('#edit_payment_mode').val(result['payment_mode']);
                $('#edit_payment_date').val(result['payment_date']);
                $('#edit_comment').val(result['comment']);
                $('#edit_id').val(result['id']);
                $('#edit_agent_id').val(result['agent_id']);
//                $('#appendBonusData').html(resultData);
            });
        });
    }

    return{
        initProcessCommission: function() {
            processCommission();
        },
        initPaymentHistory: function() {
            paymentHistory();
            creditBonus();
        }
    };
}();