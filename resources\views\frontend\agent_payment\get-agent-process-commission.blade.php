<table class="table table-hover table-custom" id="processCommission">
    <thead>
        <tr>
            <th scope="col">
                {{ Form::checkbox('check_or_not',  1 , false, array('class' => 'check_or_not', 'id' => 'check_or_not')) }}
            </th>
            <th scope="col">Invoice <br/>No</th>
            <th scope="col">Student <br/>Id</th>
            <th scope="col">Student <br/>Name</th>
            <th scope="col">Course <br/>Name</th>
            <th scope="col">Due <br/>Date</th>
            <th scope="col">Paid<br />Date</th>
            <th scope="col">Transaction <br/> Amount</th>
            <th scope="col">Refunded</th>
            <th scope="col">Commission <br/>Payable</th>
            <th scope="col">GST</th>
            <th scope="col">Total <br/>Paid</th>
            <th scope="col">Payable</th>
            @if($isXeroConnect)
                <th scope="col">Xero Sync</th>
                <th scope="col">Xero Sync Time</th>
            @endif
            <th scope="col">Remarks</th>
            <th scope="col">Action</th>
        </tr>
    </thead>
    <tbody>
        @if(count($objAgentCommissionData) > 0)
        @foreach($objAgentCommissionData as $agentCommissionData)
        <tr>
            <td>
                {{ Form::checkbox('check_update[]', $agentCommissionData->id, false, array('class' => 'check_all', 'id' => 'check_update')) }}
            </td>
            <td>{{ $agentCommissionData->formatted_invoice_number }}</td>
            <td>{{ $agentCommissionData->generated_stud_id }}</td>
            <td>{{ $agentCommissionData->first_name .' '.$agentCommissionData->family_name }}</td>
            <td>{{ $agentCommissionData->course_code.':'.$agentCommissionData->course_name }}</td>
            <td>{{ date('d-m-Y',strtotime($agentCommissionData->due_date)) }}</td>
            <td>{{ (!empty($agentCommissionData->paid_date)) ? date('d-m-Y',strtotime($agentCommissionData->paid_date)) : 'N/A' }}</td>
            <td>${{ number_format($agentCommissionData->transaction_paid_amount,2) }}</td>
            <td>${{ number_format($agentCommissionData->refund_amount,2) }}</td>
            <td>${{ $agentCommissionData->commission_payable- $agentCommissionData->comm_to_refund }}</td>
            <td>${{ number_format($agentCommissionData->gst_amount- $agentCommissionData->GST_to_refund,2) }}</td>
            <td>${{ $agentCommissionData->commission_paid }}</td>
            <td>${{ ($agentCommissionData->commission_payable + $agentCommissionData->gst_amount)  - ($agentCommissionData->comm_to_refund + $agentCommissionData->GST_to_refund) }}</td>
            @if($isXeroConnect)
                @php $xeroInvoice = $agentCommissionData->xeroInvoice; @endphp
                <td>{!! manageXeroStatus($xeroInvoice) !!}</td>
                <td>{!! manageXeroDateTime($xeroInvoice) !!}</td>
                {{--<td>{{ (!empty($xeroInvoice) && !empty($xeroInvoice->xero_synced_at)) ? date('d-m-Y h:i:s A',strtotime($xeroInvoice->xero_synced_at)) : '--' }}</td>--}}
            @endif
            <td>{{ Form::text('remarks[]',  $agentCommissionData->remarks , array('class' => 'remarks')) }}</td>
            <td class="space-x-2 flex">
                @if($sessionPermissionData == "yes")
                    @if($isXeroConnect)
                        <a href="javascript:;" class="link-black btn-xero-process" data-toggle="tooltip" data-original-title="Process">
                            <i class="fa fa-check"></i>
                        </a>
                    @else
                    <a href="javascript:;" class="link-black single-process" data-toggle="tooltip" data-original-title="Process" data-id="{{ $agentCommissionData->id }}" >
                        <i class="fa fa-check"></i>
                    </a>
                    @endif
                @endif

                @if($isXeroConnect && ($agentCommissionData->xeroInvoice != NULL || !empty($agentCommissionData->xeroInvoice->xero_invoice_id)))
                    <span class="delete_icon">
                        <a href="javascript:;" class="link-black already-synced" data-toggle="tooltip" data-original-title="Disapprove">
                            <i class="fa fa-remove"></i>
                        </a>
                    </span>
                @else
                    <span id="" data-toggle="modal" class="disapprove delete_icon" data-id="{{ $agentCommissionData->id }}" data-target="#disapproveModal" style="display:none;">
                        <a href="javascript:;" class="link-black delete" data-toggle="tooltip" data-original-title="Disapprove">
                            <i class="fa fa-remove"></i>
                        </a>
                    </span>
                @endif
            </td>
        </tr>
        @endforeach
        @else
        <tr>
            <td colspan="{{ ($isXeroConnect) ? 18 : 16 }}" style="text-align: center">
                <p style="color:red;">No Record Found</p>
            </td>
        </tr>
        @endif
    </tbody>
</table>